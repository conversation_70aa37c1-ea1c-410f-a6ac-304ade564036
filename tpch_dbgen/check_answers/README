Answer sets to be compared should all be named q<1..22>.out .
Each file should have 1 header (col name line, that is ignored) followed by lines of data with fields separated by the pipe character "|".  Lines should not start or end with a pipe char however.  No trailing line of blank lines or "rows selected" should be present.

./pairs.sh <dir1> <dir2> 
	This will create or overwrite a dir called <dir1>_<dir2> and put in it 22 files of the form analysis_<1..22>.out .

cmpq.pl
	This is the pearl script that compares two files.  The current version treats date output as a string and thus dates that (legally according to the spec) are right justified in a larger field may compare incorrectly.  This will be fixed shortly.  
