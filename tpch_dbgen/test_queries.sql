-- Test queries to verify TPC-H data import

-- Show database schema
.schema

-- Show table row counts
SELECT 'Table Row Counts:' as info;
SELECT 'REGION' as table_name, COUNT(*) as row_count FROM REGION
UNION ALL
SELECT 'NATION', COUNT(*) FROM NATION
UNION ALL
SELECT 'SUPPLIER', COUNT(*) FROM SUPPLIER
UNION ALL
SELECT 'CUSTOMER', COUNT(*) FROM CUSTOMER
UNION ALL
SELECT 'PART', COUNT(*) FROM PART
UNION ALL
SELECT 'PARTSUPP', COUNT(*) FROM PARTSUPP
UNION ALL
SELECT 'ORDERS', COUNT(*) FROM ORDERS
UNION ALL
SELECT 'LINEITEM', COUNT(*) FROM LINEITEM;

-- Sample data from each table
SELECT 'Sample REGION data:' as info;
SELECT * FROM REGION LIMIT 5;

SELECT 'Sample NATION data:' as info;
SELECT * FROM NATION LIMIT 5;

SELECT 'Sample SUPPLIER data:' as info;
SELECT * FROM SUPPLIER LIMIT 3;

SELECT 'Sample CUSTOMER data:' as info;
SELECT * FROM CUSTOMER LIMIT 3;

SELECT 'Sample PART data:' as info;
SELECT * FROM PART LIMIT 3;

SELECT 'Sample ORDERS data:' as info;
SELECT * FROM ORDERS LIMIT 3;

-- Simple TPC-H style query: Top 10 customers by account balance
SELECT 'Top 10 customers by account balance:' as info;
SELECT C_CUSTKEY, C_NAME, C_ACCTBAL, N_NAME as nation
FROM CUSTOMER C
JOIN NATION N ON C.C_NATIONKEY = N.N_NATIONKEY
ORDER BY C_ACCTBAL DESC
LIMIT 10;

-- Simple aggregation query: Orders by year
SELECT 'Orders by year:' as info;
SELECT substr(O_ORDERDATE, 1, 4) as year, COUNT(*) as order_count, 
       ROUND(SUM(O_TOTALPRICE), 2) as total_revenue
FROM ORDERS
GROUP BY substr(O_ORDERDATE, 1, 4)
ORDER BY year;

-- Join query: Parts and suppliers by nation
SELECT 'Parts and suppliers by nation (top 5):' as info;
SELECT N.N_NAME as nation, COUNT(DISTINCT P.P_PARTKEY) as part_count, 
       COUNT(DISTINCT S.S_SUPPKEY) as supplier_count
FROM NATION N
JOIN SUPPLIER S ON N.N_NATIONKEY = S.S_NATIONKEY
JOIN PARTSUPP PS ON S.S_SUPPKEY = PS.PS_SUPPKEY
JOIN PART P ON PS.PS_PARTKEY = P.P_PARTKEY
GROUP BY N.N_NAME
ORDER BY part_count DESC
LIMIT 5;
