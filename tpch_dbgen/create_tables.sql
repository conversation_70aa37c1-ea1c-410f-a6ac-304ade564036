-- TPC-H Database Schema for SQLite
-- Based on TPC-H specification

-- Drop tables if they exist
DROP TABLE IF EXISTS LINEITEM;
DROP TABLE IF EXISTS ORDERS;
DROP TABLE IF EXISTS CUSTOMER;
DROP TABLE IF EXISTS PARTSUPP;
DROP TABLE IF EXISTS SUPPLIER;
DROP TABLE IF EXISTS PART;
DROP TABLE IF EXISTS NATION;
DROP TABLE IF EXISTS REGION;

-- Create REGION table
CREATE TABLE REGION (
    R_REGIONKEY  INTEGER NOT NULL,
    R_NAME       CHAR(25) NOT NULL,
    R_COMMENT    VARCHAR(152),
    PRIMARY KEY (R_REGIONKEY)
);

-- Create NATION table
CREATE TABLE NATION (
    N_NATIONKEY  INTEGER NOT NULL,
    N_NAME       CHAR(25) NOT NULL,
    N_REGIONKEY  INTEGER NOT NULL,
    N_COMMENT    VARCHAR(152),
    PRIMARY KEY (N_NATIONKEY),
    FOREIGN KEY (N_REGIONKEY) REFERENCES REGION(R_REGIONKEY)
);

-- Create SUPPLIER table
CREATE TABLE SUPPLIER (
    S_SUPPKEY     INTEGER NOT NULL,
    S_NAME        CHAR(25) NOT NULL,
    S_ADDRESS     VARCHAR(40) NOT NULL,
    S_NATIONKEY   INTEGER NOT NULL,
    S_PHONE       CHAR(15) NOT NULL,
    S_ACCTBAL     DECIMAL(15,2) NOT NULL,
    S_COMMENT     VARCHAR(101) NOT NULL,
    PRIMARY KEY (S_SUPPKEY),
    FOREIGN KEY (S_NATIONKEY) REFERENCES NATION(N_NATIONKEY)
);

-- Create CUSTOMER table
CREATE TABLE CUSTOMER (
    C_CUSTKEY     INTEGER NOT NULL,
    C_NAME        VARCHAR(25) NOT NULL,
    C_ADDRESS     VARCHAR(40) NOT NULL,
    C_NATIONKEY   INTEGER NOT NULL,
    C_PHONE       CHAR(15) NOT NULL,
    C_ACCTBAL     DECIMAL(15,2) NOT NULL,
    C_MKTSEGMENT  CHAR(10) NOT NULL,
    C_COMMENT     VARCHAR(117) NOT NULL,
    PRIMARY KEY (C_CUSTKEY),
    FOREIGN KEY (C_NATIONKEY) REFERENCES NATION(N_NATIONKEY)
);

-- Create PART table
CREATE TABLE PART (
    P_PARTKEY     INTEGER NOT NULL,
    P_NAME        VARCHAR(55) NOT NULL,
    P_MFGR        CHAR(25) NOT NULL,
    P_BRAND       CHAR(10) NOT NULL,
    P_TYPE        VARCHAR(25) NOT NULL,
    P_SIZE        INTEGER NOT NULL,
    P_CONTAINER   CHAR(10) NOT NULL,
    P_RETAILPRICE DECIMAL(15,2) NOT NULL,
    P_COMMENT     VARCHAR(23) NOT NULL,
    PRIMARY KEY (P_PARTKEY)
);

-- Create PARTSUPP table
CREATE TABLE PARTSUPP (
    PS_PARTKEY     INTEGER NOT NULL,
    PS_SUPPKEY     INTEGER NOT NULL,
    PS_AVAILQTY    INTEGER NOT NULL,
    PS_SUPPLYCOST  DECIMAL(15,2) NOT NULL,
    PS_COMMENT     VARCHAR(199) NOT NULL,
    PRIMARY KEY (PS_PARTKEY, PS_SUPPKEY),
    FOREIGN KEY (PS_PARTKEY) REFERENCES PART(P_PARTKEY),
    FOREIGN KEY (PS_SUPPKEY) REFERENCES SUPPLIER(S_SUPPKEY)
);

-- Create ORDERS table
CREATE TABLE ORDERS (
    O_ORDERKEY       INTEGER NOT NULL,
    O_CUSTKEY        INTEGER NOT NULL,
    O_ORDERSTATUS    CHAR(1) NOT NULL,
    O_TOTALPRICE     DECIMAL(15,2) NOT NULL,
    O_ORDERDATE      DATE NOT NULL,
    O_ORDERPRIORITY  CHAR(15) NOT NULL,
    O_CLERK          CHAR(15) NOT NULL,
    O_SHIPPRIORITY   INTEGER NOT NULL,
    O_COMMENT        VARCHAR(79) NOT NULL,
    PRIMARY KEY (O_ORDERKEY),
    FOREIGN KEY (O_CUSTKEY) REFERENCES CUSTOMER(C_CUSTKEY)
);

-- Create LINEITEM table
CREATE TABLE LINEITEM (
    L_ORDERKEY    INTEGER NOT NULL,
    L_PARTKEY     INTEGER NOT NULL,
    L_SUPPKEY     INTEGER NOT NULL,
    L_LINENUMBER  INTEGER NOT NULL,
    L_QUANTITY    DECIMAL(15,2) NOT NULL,
    L_EXTENDEDPRICE  DECIMAL(15,2) NOT NULL,
    L_DISCOUNT    DECIMAL(15,2) NOT NULL,
    L_TAX         DECIMAL(15,2) NOT NULL,
    L_RETURNFLAG  CHAR(1) NOT NULL,
    L_LINESTATUS  CHAR(1) NOT NULL,
    L_SHIPDATE    DATE NOT NULL,
    L_COMMITDATE  DATE NOT NULL,
    L_RECEIPTDATE DATE NOT NULL,
    L_SHIPINSTRUCT CHAR(25) NOT NULL,
    L_SHIPMODE     CHAR(10) NOT NULL,
    L_COMMENT      VARCHAR(44) NOT NULL,
    PRIMARY KEY (L_ORDERKEY, L_LINENUMBER),
    FOREIGN KEY (L_ORDERKEY) REFERENCES ORDERS(O_ORDERKEY),
    FOREIGN KEY (L_PARTKEY, L_SUPPKEY) REFERENCES PARTSUPP(PS_PARTKEY, PS_SUPPKEY)
);

-- Create indexes for better performance
CREATE INDEX idx_customer_nationkey ON CUSTOMER(C_NATIONKEY);
CREATE INDEX idx_supplier_nationkey ON SUPPLIER(S_NATIONKEY);
CREATE INDEX idx_nation_regionkey ON NATION(N_REGIONKEY);
CREATE INDEX idx_orders_custkey ON ORDERS(O_CUSTKEY);
CREATE INDEX idx_orders_orderdate ON ORDERS(O_ORDERDATE);
CREATE INDEX idx_lineitem_orderkey ON LINEITEM(L_ORDERKEY);
CREATE INDEX idx_lineitem_partkey ON LINEITEM(L_PARTKEY);
CREATE INDEX idx_lineitem_suppkey ON LINEITEM(L_SUPPKEY);
CREATE INDEX idx_lineitem_shipdate ON LINEITEM(L_SHIPDATE);
CREATE INDEX idx_partsupp_partkey ON PARTSUPP(PS_PARTKEY);
CREATE INDEX idx_partsupp_suppkey ON PARTSUPP(PS_SUPPKEY);
