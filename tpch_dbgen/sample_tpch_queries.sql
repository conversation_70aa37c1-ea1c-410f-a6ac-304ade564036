-- Sample TPC-H Queries for SQLite Database
-- These are simplified versions of standard TPC-H benchmark queries

-- Query 1: Pricing Summary Report (simplified version of TPC-H Q1)
-- This query reports the amount of business that was billed, shipped, and returned
SELECT 
    L_RETURNFLAG,
    L_LINESTATUS,
    SUM(L_QUANTITY) as sum_qty,
    SUM(L_EXTENDEDPRICE) as sum_base_price,
    SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT)) as sum_disc_price,
    SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT) * (1 + L_TAX)) as sum_charge,
    AVG(L_QUANTITY) as avg_qty,
    AVG(L_EXTENDEDPRICE) as avg_price,
    AVG(L_DISCOUNT) as avg_disc,
    COUNT(*) as count_order
FROM LINEITEM
WHERE L_SHIPDATE <= date('1998-12-01', '-90 days')
GROUP BY L_RETURNFLAG, L_LINESTATUS
ORDER BY L_RETURNFLAG, L_LINESTATUS;

-- Query 2: Minimum Cost Supplier (simplified version of TPC-H Q2)
-- This query finds which supplier should be selected to place an order for a given part
SELECT 
    S_ACCTBAL,
    S_NAME,
    N_NAME,
    P_PARTKEY,
    P_MFGR,
    S_ADDRESS,
    S_PHONE,
    S_COMMENT
FROM PART, SUPPLIER, PARTSUPP, NATION, REGION
WHERE P_PARTKEY = PS_PARTKEY
    AND S_SUPPKEY = PS_SUPPKEY
    AND P_SIZE = 15
    AND P_TYPE LIKE '%BRASS'
    AND S_NATIONKEY = N_NATIONKEY
    AND N_REGIONKEY = R_REGIONKEY
    AND R_NAME = 'EUROPE'
    AND PS_SUPPLYCOST = (
        SELECT MIN(PS_SUPPLYCOST)
        FROM PARTSUPP, SUPPLIER, NATION, REGION
        WHERE P_PARTKEY = PS_PARTKEY
            AND S_SUPPKEY = PS_SUPPKEY
            AND S_NATIONKEY = N_NATIONKEY
            AND N_REGIONKEY = R_REGIONKEY
            AND R_NAME = 'EUROPE'
    )
ORDER BY S_ACCTBAL DESC, N_NAME, S_NAME, P_PARTKEY
LIMIT 100;

-- Query 3: Shipping Priority (simplified version of TPC-H Q3)
-- This query retrieves the 10 unshipped orders with the highest value
SELECT 
    L_ORDERKEY,
    SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT)) as revenue,
    O_ORDERDATE,
    O_SHIPPRIORITY
FROM CUSTOMER, ORDERS, LINEITEM
WHERE C_MKTSEGMENT = 'BUILDING'
    AND C_CUSTKEY = O_CUSTKEY
    AND L_ORDERKEY = O_ORDERKEY
    AND O_ORDERDATE < '1995-03-15'
    AND L_SHIPDATE > '1995-03-15'
GROUP BY L_ORDERKEY, O_ORDERDATE, O_SHIPPRIORITY
ORDER BY revenue DESC, O_ORDERDATE
LIMIT 10;

-- Query 4: Order Priority Checking (simplified version of TPC-H Q4)
-- This query counts orders by priority for orders placed in a given quarter
SELECT 
    O_ORDERPRIORITY,
    COUNT(*) as order_count
FROM ORDERS
WHERE O_ORDERDATE >= '1993-07-01'
    AND O_ORDERDATE < '1993-10-01'
    AND EXISTS (
        SELECT *
        FROM LINEITEM
        WHERE L_ORDERKEY = O_ORDERKEY
            AND L_COMMITDATE < L_RECEIPTDATE
    )
GROUP BY O_ORDERPRIORITY
ORDER BY O_ORDERPRIORITY;

-- Query 5: Local Supplier Volume (simplified version of TPC-H Q5)
-- This query lists the revenue volume done through local suppliers
SELECT 
    N_NAME,
    SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT)) as revenue
FROM CUSTOMER, ORDERS, LINEITEM, SUPPLIER, NATION, REGION
WHERE C_CUSTKEY = O_CUSTKEY
    AND L_ORDERKEY = O_ORDERKEY
    AND L_SUPPKEY = S_SUPPKEY
    AND C_NATIONKEY = S_NATIONKEY
    AND S_NATIONKEY = N_NATIONKEY
    AND N_REGIONKEY = R_REGIONKEY
    AND R_NAME = 'ASIA'
    AND O_ORDERDATE >= '1994-01-01'
    AND O_ORDERDATE < '1995-01-01'
GROUP BY N_NAME
ORDER BY revenue DESC;

-- Query 6: Forecasting Revenue Change (TPC-H Q6)
-- This query quantifies the amount of revenue increase that would have resulted
SELECT 
    SUM(L_EXTENDEDPRICE * L_DISCOUNT) as revenue
FROM LINEITEM
WHERE L_SHIPDATE >= '1994-01-01'
    AND L_SHIPDATE < '1995-01-01'
    AND L_DISCOUNT BETWEEN 0.05 AND 0.07
    AND L_QUANTITY < 24;
