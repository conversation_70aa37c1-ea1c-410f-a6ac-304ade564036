-- Import TPC-H data into SQLite database
-- Data files are pipe-delimited (|)

.mode csv
.separator |

-- Import REGION table
.import region.tbl REGION

-- Import NATION table  
.import nation.tbl NATION

-- Import SUPPLIER table
.import supplier.tbl SUPPLIER

-- Import CUSTOMER table
.import customer.tbl CUSTOMER

-- Import PART table
.import part.tbl PART

-- Import PARTSUPP table
.import partsupp.tbl PARTSUPP

-- Import ORDERS table
.import orders.tbl ORDERS

-- Import LINEITEM table (largest table, may take some time)
.import lineitem.tbl LINEITEM

-- Show table counts to verify import
SELECT 'REGION' as table_name, COUNT(*) as row_count FROM REGION
UNION ALL
SELECT 'NATION', COUNT(*) FROM NATION
UNION ALL
SELECT 'SUPPLIER', COUNT(*) FROM SUPPLIER
UNION ALL
SELECT 'CUSTOMER', COUNT(*) FROM CUSTOMER
UNION ALL
SELECT 'PART', COUNT(*) FROM PART
UNION ALL
SELECT 'PARTSUPP', COUNT(*) FROM PARTSUPP
UNION ALL
SELECT 'ORDERS', COUNT(*) FROM ORDERS
UNION ALL
SELECT 'LINEITEM', COUNT(*) FROM LINEITEM;
