# TPC-H Database for SQLite

This directory contains a complete TPC-H benchmark database setup for SQLite.

## Files Overview

- `tpch.db` - SQLite database file (159MB) containing all TPC-H tables with data
- `create_tables.sql` - SQL script to create TPC-H table schema
- `import_data.sql` - SQL script to import data from .tbl files
- `test_queries.sql` - Basic queries to verify data import
- `sample_tpch_queries.sql` - Sample TPC-H benchmark queries
- `*.tbl` - Raw data files generated by dbgen (pipe-delimited)

## Database Schema

The database contains 8 tables following TPC-H specification:

1. **REGION** (5 rows) - Geographic regions
2. **NATION** (25 rows) - Countries within regions  
3. **SUPPLIER** (1,000 rows) - Parts suppliers
4. **CUSTOMER** (15,000 rows) - Customers
5. **PART** (20,000 rows) - Parts catalog
6. **PARTSUPP** (80,000 rows) - Parts supplied by suppliers
7. **ORDERS** (150,000 rows) - Customer orders
8. **LINEITEM** (600,572 rows) - Order line items

## Data Scale

- Scale Factor: 0.1 (approximately 100MB of raw data)
- Total database size: 159MB (with indexes)
- Suitable for development and testing

## Usage Examples

### Connect to database:
```bash
sqlite3 tpch.db
```

### Run sample queries:
```bash
sqlite3 tpch.db < sample_tpch_queries.sql
```

### Basic table information:
```sql
-- Show all tables
.tables

-- Count records in each table
SELECT 'LINEITEM' as table_name, COUNT(*) as rows FROM LINEITEM
UNION ALL SELECT 'ORDERS', COUNT(*) FROM ORDERS
UNION ALL SELECT 'CUSTOMER', COUNT(*) FROM CUSTOMER;
```

### Sample analytical queries:

```sql
-- Top 10 customers by account balance
SELECT C_NAME, C_ACCTBAL, N_NAME 
FROM CUSTOMER C JOIN NATION N ON C.C_NATIONKEY = N.N_NATIONKEY 
ORDER BY C_ACCTBAL DESC LIMIT 10;

-- Revenue by year
SELECT substr(O_ORDERDATE, 1, 4) as year, 
       COUNT(*) as orders, 
       SUM(O_TOTALPRICE) as revenue
FROM ORDERS 
GROUP BY year 
ORDER BY year;
```

## Performance Notes

- All tables have appropriate indexes for common query patterns
- Foreign key constraints are defined but not enforced (SQLite default)
- For better performance on large queries, consider:
  - `PRAGMA journal_mode = WAL;`
  - `PRAGMA synchronous = NORMAL;`
  - `PRAGMA cache_size = 10000;`

## Regenerating Data

To generate data with different scale factors:

```bash
# Generate larger dataset (scale factor 1.0 = ~1GB)
./dbgen -vf -s 1.0

# Generate smaller dataset (scale factor 0.01 = ~10MB)  
./dbgen -vf -s 0.01
```

Then re-run the import process:
```bash
sqlite3 new_tpch.db < create_tables.sql
sqlite3 new_tpch.db < import_data.sql
```

## TPC-H Benchmark Queries

The `sample_tpch_queries.sql` file contains simplified versions of standard TPC-H queries:

- Q1: Pricing Summary Report
- Q2: Minimum Cost Supplier  
- Q3: Shipping Priority
- Q4: Order Priority Checking
- Q5: Local Supplier Volume
- Q6: Forecasting Revenue Change

These can be used for testing SQL query performance and functionality.
