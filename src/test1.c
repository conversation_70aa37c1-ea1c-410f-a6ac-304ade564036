/*
** 2001 September 15
**
** The author disclaims copyright to this source code.  In place of
** a legal notice, here is a blessing:
**
**    May you do good and not evil.
**    May you find forgiveness for yourself and forgive others.
**    May you share freely, never taking more than you give.
**
*************************************************************************
** Code for testing all sorts of SQLite interfaces.  This code
** is not included in the SQLite library.  It is used for automated
** testing of the SQLite library.
*/
#include "sqliteInt.h"
#if SQLITE_OS_WIN
#  include "os_win.h"
#  include <windows.h>
#else
#  include <unistd.h>
#  if defined(__APPLE__)
#    include <sys/param.h>
#    include <sys/sysctl.h>
#  endif
#endif

#include "vdbeInt.h"
#include "tclsqlite.h"
#include <stdlib.h>
#include <string.h>

/*
** This is a copy of the first part of the SqliteDb structure in 
** tclsqlite.c.  We need it here so that the get_sqlite_pointer routine
** can extract the sqlite3* pointer from an existing Tcl SQLite
** connection.
*/
struct SqliteDb {
  sqlite3 *db;
};

/*
** Convert text generated by the "%p" conversion format back into
** a pointer.
*/
static int testHexToInt(int h){
  if( h>='0' && h<='9' ){
    return h - '0';
  }else if( h>='a' && h<='f' ){
    return h - 'a' + 10;
  }else{
    assert( h>='A' && h<='F' );
    return h - 'A' + 10;
  }
}
void *sqlite3TestTextToPtr(const char *z){
  void *p;
  u64 v;
  u32 v2;
  if( z[0]=='0' && z[1]=='x' ){
    z += 2;
  }
  v = 0;
  while( *z ){
    v = (v<<4) + testHexToInt(*z);
    z++;
  }
  if( sizeof(p)==sizeof(v) ){
    memcpy(&p, &v, sizeof(p));
  }else{
    assert( sizeof(p)==sizeof(v2) );
    v2 = (u32)v;
    memcpy(&p, &v2, sizeof(p));
  }
  return p;
}


/*
** A TCL command that returns the address of the sqlite* pointer
** for an sqlite connection instance.  Bad things happen if the
** input is not an sqlite connection.
*/
static int SQLITE_TCLAPI get_sqlite_pointer(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  struct SqliteDb *p;
  Tcl_CmdInfo cmdInfo;
  char zBuf[100];
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "SQLITE-CONNECTION");
    return TCL_ERROR;
  }
  if( !Tcl_GetCommandInfo(interp, Tcl_GetString(objv[1]), &cmdInfo) ){
    Tcl_AppendResult(interp, "command not found: ",
           Tcl_GetString(objv[1]), (char*)0);
    return TCL_ERROR;
  }
  p = (struct SqliteDb*)cmdInfo.objClientData;
  sqlite3_snprintf(sizeof(zBuf), zBuf, "%p", p->db);
  Tcl_AppendResult(interp, zBuf, 0);
  return TCL_OK;
}

/*
** Decode a pointer to an sqlite3 object.
*/
int getDbPointer(Tcl_Interp *interp, const char *zA, sqlite3 **ppDb){
  struct SqliteDb *p;
  Tcl_CmdInfo cmdInfo;
  if( Tcl_GetCommandInfo(interp, zA, &cmdInfo) ){
    p = (struct SqliteDb*)cmdInfo.objClientData;
    *ppDb = p->db;
  }else{
    *ppDb = (sqlite3*)sqlite3TestTextToPtr(zA);
  }
  return TCL_OK;
}

#if SQLITE_OS_WIN
/*
** Decode a Win32 HANDLE object.
*/
int getWin32Handle(Tcl_Interp *interp, const char *zA, LPHANDLE phFile){
  *phFile = (HANDLE)sqlite3TestTextToPtr(zA);
  return TCL_OK;
}
#endif

extern const char *sqlite3ErrName(int);
#define t1ErrorName sqlite3ErrName

/*
** Convert an sqlite3_stmt* into an sqlite3*.  This depends on the
** fact that the sqlite3* is the first field in the Vdbe structure.
*/
#define StmtToDb(X)   sqlite3_db_handle(X)

/*
** Check a return value to make sure it agrees with the results
** from sqlite3_errcode.
*/
int sqlite3TestErrCode(Tcl_Interp *interp, sqlite3 *db, int rc){
  if( sqlite3_threadsafe()==0 && rc!=SQLITE_MISUSE && rc!=SQLITE_OK
   && sqlite3_errcode(db)!=rc ){
    char zBuf[200];
    int r2 = sqlite3_errcode(db);
    sqlite3_snprintf(sizeof(zBuf), zBuf,
       "error code %s (%d) does not match sqlite3_errcode %s (%d)",
       t1ErrorName(rc), rc, t1ErrorName(r2), r2);
    Tcl_ResetResult(interp);
    Tcl_AppendResult(interp, zBuf, 0);
    return 1;
  }
  return 0;
}

/*
** Decode a pointer to an sqlite3_stmt object.
*/
static int getStmtPointer(
  Tcl_Interp *interp, 
  const char *zArg,  
  sqlite3_stmt **ppStmt
){
  *ppStmt = (sqlite3_stmt*)sqlite3TestTextToPtr(zArg);
  return TCL_OK;
}

/*
** Generate a text representation of a pointer that can be understood
** by the getDbPointer and getVmPointer routines above.
**
** The problem is, on some machines (Solaris) if you do a printf with
** "%p" you cannot turn around and do a scanf with the same "%p" and
** get your pointer back.  You have to prepend a "0x" before it will
** work.  Or at least that is what is reported to me (drh).  But this
** behavior varies from machine to machine.  The solution used her is
** to test the string right after it is generated to see if it can be
** understood by scanf, and if not, try prepending an "0x" to see if
** that helps.  If nothing works, a fatal error is generated.
*/
int sqlite3TestMakePointerStr(Tcl_Interp *interp, char *zPtr, void *p){
  sqlite3_snprintf(100, zPtr, "%p", p);
  return TCL_OK;
}

/*
** The callback routine for sqlite3_exec_printf().
*/
static int exec_printf_cb(void *pArg, int argc, char **argv, char **name){
  Tcl_DString *str = (Tcl_DString*)pArg;
  int i;

  if( Tcl_DStringLength(str)==0 ){
    for(i=0; i<argc; i++){
      Tcl_DStringAppendElement(str, name[i] ? name[i] : "NULL");
    }
  }
  for(i=0; i<argc; i++){
    Tcl_DStringAppendElement(str, argv[i] ? argv[i] : "NULL");
  }
  return 0;
}

/*
** The I/O tracing callback.
*/
#if !defined(SQLITE_OMIT_TRACE) && defined(SQLITE_ENABLE_IOTRACE)
static FILE *iotrace_file = 0;
static void io_trace_callback(const char *zFormat, ...){
  va_list ap;
  va_start(ap, zFormat);
  vfprintf(iotrace_file, zFormat, ap);
  va_end(ap);
  fflush(iotrace_file);
}
#endif

/*
** Usage:  io_trace FILENAME
**
** Turn I/O tracing on or off.  If FILENAME is not an empty string,
** I/O tracing begins going into FILENAME. If FILENAME is an empty
** string, I/O tracing is turned off.
*/
static int SQLITE_TCLAPI test_io_trace(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
#if !defined(SQLITE_OMIT_TRACE) && defined(SQLITE_ENABLE_IOTRACE)
  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
          " FILENAME\"", 0);
    return TCL_ERROR;
  }
  if( iotrace_file ){
    if( iotrace_file!=stdout && iotrace_file!=stderr ){
      fclose(iotrace_file);
    }
    iotrace_file = 0;
    sqlite3IoTrace = 0;
  }
  if( argv[1][0] ){
    if( strcmp(argv[1],"stdout")==0 ){
      iotrace_file = stdout;
    }else if( strcmp(argv[1],"stderr")==0 ){
      iotrace_file = stderr;
    }else{
      iotrace_file = fopen(argv[1], "w");
    }
    sqlite3IoTrace = io_trace_callback;
  }
#endif
  return TCL_OK;
}

/*
** Usage:  clang_sanitize_address 
**
** Returns true if the program was compiled using clang with the 
** -fsanitize=address switch on the command line. False otherwise.
**
** Also return true if the OMIT_MISUSE environment variable exists.
*/
static int SQLITE_TCLAPI clang_sanitize_address(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  int res = 0;
#if defined(__has_feature)
# if __has_feature(address_sanitizer)
  res = 1;
# endif
#endif
#ifdef __SANITIZE_ADDRESS__
  res = 1;
#endif
  if( res==0 && getenv("OMIT_MISUSE")!=0 ) res = 1;
  Tcl_SetObjResult(interp, Tcl_NewIntObj(res));
  return TCL_OK;
}
  
/*
** Usage:  sqlite3_exec_printf  DB  FORMAT  STRING
**
** Invoke the sqlite3_exec_printf() interface using the open database
** DB.  The SQL is the string FORMAT.  The format string should contain
** one %s or %q.  STRING is the value inserted into %s or %q.
*/
static int SQLITE_TCLAPI test_exec_printf(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  Tcl_DString str;
  int rc;
  char *zErr = 0;
  char *zSql;
  char zBuf[30];
  if( argc!=4 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
       " DB FORMAT STRING", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  Tcl_DStringInit(&str);
  zSql = sqlite3_mprintf(argv[2], argv[3]);
  rc = sqlite3_exec(db, zSql, exec_printf_cb, &str, &zErr);
  sqlite3_free(zSql);
  sqlite3_snprintf(sizeof(zBuf), zBuf, "%d", rc);
  Tcl_AppendElement(interp, zBuf);
  Tcl_AppendElement(interp, rc==SQLITE_OK ? Tcl_DStringValue(&str) : zErr);
  Tcl_DStringFree(&str);
  if( zErr ) sqlite3_free(zErr);
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  return TCL_OK;
}

/*
** Usage:  sqlite3_exec_hex  DB  HEX
**
** Invoke the sqlite3_exec() on a string that is obtained by translating
** HEX into ASCII.  Most characters are translated as is.  %HH becomes
** a hex character.
*/
static int SQLITE_TCLAPI test_exec_hex(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  Tcl_DString str;
  int rc, i, j;
  char *zErr = 0;
  char *zHex;
  char zSql[501];
  char zBuf[30];
  if( argc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
       " DB HEX", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  zHex = argv[2];
  for(i=j=0; i<(sizeof(zSql)-1) && zHex[j]; i++, j++){
    if( zHex[j]=='%' && zHex[j+2] && zHex[j+2] ){
      zSql[i] = (testHexToInt(zHex[j+1])<<4) + testHexToInt(zHex[j+2]);
      j += 2;
    }else{
      zSql[i] = zHex[j];
    }
  }
  zSql[i] = 0;
  Tcl_DStringInit(&str);
  rc = sqlite3_exec(db, zSql, exec_printf_cb, &str, &zErr);
  sqlite3_snprintf(sizeof(zBuf), zBuf, "%d", rc);
  Tcl_AppendElement(interp, zBuf);
  Tcl_AppendElement(interp, rc==SQLITE_OK ? Tcl_DStringValue(&str) : zErr);
  Tcl_DStringFree(&str);
  if( zErr ) sqlite3_free(zErr);
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  return TCL_OK;
}

/*
** Usage:  db_enter DB
**         db_leave DB
**
** Enter or leave the mutex on a database connection.
*/
static int SQLITE_TCLAPI db_enter(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
       " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  sqlite3_mutex_enter(db->mutex);
  return TCL_OK;
}
static int SQLITE_TCLAPI db_leave(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
       " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  sqlite3_mutex_leave(db->mutex);
  return TCL_OK;
}

/*
** Usage:  sqlite3_exec  DB  SQL
**
** Invoke the sqlite3_exec interface using the open database DB
*/
static int SQLITE_TCLAPI test_exec(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  Tcl_DString str;
  int rc;
  char *zErr = 0;
  char *zSql;
  int i, j;
  char zBuf[30];
  if( argc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
       " DB SQL", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  Tcl_DStringInit(&str);
  zSql = sqlite3_mprintf("%s", argv[2]);
  for(i=j=0; zSql[i];){
    if( zSql[i]=='%' ){
      zSql[j++] = (testHexToInt(zSql[i+1])<<4) + testHexToInt(zSql[i+2]);
      i += 3;
    }else{
      zSql[j++] = zSql[i++];
    }
  }
  zSql[j] = 0;
  rc = sqlite3_exec(db, zSql, exec_printf_cb, &str, &zErr);
  sqlite3_free(zSql);
  sqlite3_snprintf(sizeof(zBuf), zBuf, "%d", rc);
  Tcl_AppendElement(interp, zBuf);
  Tcl_AppendElement(interp, rc==SQLITE_OK ? Tcl_DStringValue(&str) : zErr);
  Tcl_DStringFree(&str);
  if( zErr ) sqlite3_free(zErr);
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  return TCL_OK;
}

/*
** Usage:  sqlite3_exec_nr  DB  SQL
**
** Invoke the sqlite3_exec interface using the open database DB.  Discard
** all results
*/
static int SQLITE_TCLAPI test_exec_nr(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  int rc;
  char *zErr = 0;
  if( argc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
       " DB SQL", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  rc = sqlite3_exec(db, argv[2], 0, 0, &zErr);
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  return TCL_OK;
}

/*
** Usage:  sqlite3_mprintf_z_test  SEPARATOR  ARG0  ARG1 ...
**
** Test the %z format of sqlite_mprintf().  Use multiple mprintf() calls to 
** concatenate arg0 through argn using separator as the separator.
** Return the result.
*/
static int SQLITE_TCLAPI test_mprintf_z(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  char *zResult = 0;
  int i;

  for(i=2; i<argc && (i==2 || zResult); i++){
    zResult = sqlite3_mprintf("%z%s%s", zResult, argv[1], argv[i]);
  }
  Tcl_AppendResult(interp, zResult, 0);
  sqlite3_free(zResult);
  return TCL_OK;
}

/*
** Usage:  sqlite3_mprintf_n_test  STRING
**
** Test the %n format of sqlite_mprintf().  Return the length of the
** input string.
*/
static int SQLITE_TCLAPI test_mprintf_n(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  char *zStr;
  int n = 0;
  zStr = sqlite3_mprintf("%s%n", argv[1], &n);
  sqlite3_free(zStr);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(n));
  return TCL_OK;
}

/*
** Usage:  sqlite3_snprintf_int  SIZE FORMAT  INT
**
** Test the of sqlite3_snprintf() routine.  SIZE is the size of the
** output buffer in bytes.  The maximum size is 100.  FORMAT is the
** format string.  INT is a single integer argument.  The FORMAT
** string must require no more than this one integer argument.  If
** You pass in a format string that requires more than one argument,
** bad things will happen.
*/
static int SQLITE_TCLAPI test_snprintf_int(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  char zStr[100];
  int n = atoi(argv[1]);
  const char *zFormat = argv[2];
  int a1 = atoi(argv[3]);
  if( n>sizeof(zStr) ) n = sizeof(zStr);
  sqlite3_snprintf(sizeof(zStr), zStr, "abcdefghijklmnopqrstuvwxyz");
  sqlite3_snprintf(n, zStr, zFormat, a1);
  Tcl_AppendResult(interp, zStr, 0);
  return TCL_OK;
}

#ifndef SQLITE_OMIT_GET_TABLE

/*
** Usage:  sqlite3_get_table_printf  DB  FORMAT  STRING  ?--no-counts?
**
** Invoke the sqlite3_get_table_printf() interface using the open database
** DB.  The SQL is the string FORMAT.  The format string should contain
** one %s or %q.  STRING is the value inserted into %s or %q.
*/
static int SQLITE_TCLAPI test_get_table_printf(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  Tcl_DString str;
  int rc;
  char *zErr = 0;
  int nRow = 0, nCol = 0;
  char **aResult;
  int i;
  char zBuf[30];
  char *zSql;
  int resCount = -1;
  if( argc==5 ){
    if( Tcl_GetInt(interp, argv[4], &resCount) ) return TCL_ERROR;
  }
  if( argc!=4 && argc!=5 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
       " DB FORMAT STRING ?COUNT?", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  Tcl_DStringInit(&str);
  zSql = sqlite3_mprintf(argv[2],argv[3]);
  if( argc==5 ){
    rc = sqlite3_get_table(db, zSql, &aResult, 0, 0, &zErr);
  }else{
    rc = sqlite3_get_table(db, zSql, &aResult, &nRow, &nCol, &zErr);
    resCount = (nRow+1)*nCol;
  }
  sqlite3_free(zSql);
  sqlite3_snprintf(sizeof(zBuf), zBuf, "%d", rc);
  Tcl_ResetResult(interp);
  Tcl_AppendElement(interp, zBuf);
  if( rc==SQLITE_OK ){
    if( argc==4 ){
      sqlite3_snprintf(sizeof(zBuf), zBuf, "%d", nRow);
      Tcl_AppendElement(interp, zBuf);
      sqlite3_snprintf(sizeof(zBuf), zBuf, "%d", nCol);
      Tcl_AppendElement(interp, zBuf);
    }
    for(i=0; i<resCount; i++){
      Tcl_AppendElement(interp, aResult[i] ? aResult[i] : "NULL");
    }
  }else{
    Tcl_AppendElement(interp, zErr);
  }
  sqlite3_free_table(aResult);
  if( zErr ) sqlite3_free(zErr);
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  return TCL_OK;
}

#endif /* SQLITE_OMIT_GET_TABLE */


/*
** Usage:  sqlite3_last_insert_rowid DB
**
** Returns the integer ROWID of the most recent insert.
*/
static int SQLITE_TCLAPI test_last_rowid(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  char zBuf[30];

  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], " DB\"", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  sqlite3_snprintf(sizeof(zBuf), zBuf, "%lld", sqlite3_last_insert_rowid(db));
  Tcl_AppendResult(interp, zBuf, 0);
  return SQLITE_OK;
}

/*
** Usage:  sqlite3_key DB KEY
**
** Set the codec key.
*/
static int SQLITE_TCLAPI test_key(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  return TCL_OK;
}

/*
** Usage:  sqlite3_rekey DB KEY
**
** Change the codec key.
*/
static int SQLITE_TCLAPI test_rekey(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  return TCL_OK;
}

/*
** Usage:  sqlite3_close DB
**
** Closes the database opened by sqlite3_open.
*/
static int SQLITE_TCLAPI sqlite_test_close(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  int rc;
  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " FILENAME\"", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  rc = sqlite3_close(db);
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), TCL_STATIC);
  return TCL_OK;
}

/*
** Usage:  sqlite3_close_v2 DB
**
** Closes the database opened by sqlite3_open.
*/
static int SQLITE_TCLAPI sqlite_test_close_v2(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  int rc;
  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " FILENAME\"", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  rc = sqlite3_close_v2(db);
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), TCL_STATIC);
  return TCL_OK;
}

/*
** Implementation of the x_coalesce() function.
** Return the first argument non-NULL argument.
*/
static void t1_ifnullFunc(
  sqlite3_context *context,
  int argc,
  sqlite3_value **argv
){
  int i;
  for(i=0; i<argc; i++){
    if( SQLITE_NULL!=sqlite3_value_type(argv[i]) ){
      int n = sqlite3_value_bytes(argv[i]);
      sqlite3_result_text(context, (char*)sqlite3_value_text(argv[i]),
          n, SQLITE_TRANSIENT);
      break;
    }
  }
}

/*
** These are test functions.    hex8() interprets its argument as
** UTF8 and returns a hex encoding.  hex16le() interprets its argument
** as UTF16le and returns a hex encoding.
*/
static void hex8Func(sqlite3_context *p, int argc, sqlite3_value **argv){
  const unsigned char *z;
  int i;
  char zBuf[200];
  z = sqlite3_value_text(argv[0]);
  for(i=0; i<sizeof(zBuf)/2 - 2 && z[i]; i++){
    sqlite3_snprintf(sizeof(zBuf)-i*2, &zBuf[i*2], "%02x", z[i]);
  }
  zBuf[i*2] = 0;
  sqlite3_result_text(p, (char*)zBuf, -1, SQLITE_TRANSIENT);
}
#ifndef SQLITE_OMIT_UTF16
static void hex16Func(sqlite3_context *p, int argc, sqlite3_value **argv){
  const unsigned short int *z;
  int i;
  char zBuf[400];
  z = sqlite3_value_text16(argv[0]);
  for(i=0; i<sizeof(zBuf)/4 - 4 && z[i]; i++){
    sqlite3_snprintf(sizeof(zBuf)-i*4, &zBuf[i*4],"%04x", z[i]&0xff);
  }
  zBuf[i*4] = 0;
  sqlite3_result_text(p, (char*)zBuf, -1, SQLITE_TRANSIENT);
}
#endif

/*
** A structure into which to accumulate text.
*/
struct dstr {
  int nAlloc;  /* Space allocated */
  int nUsed;   /* Space used */
  char *z;     /* The space */
};

/*
** Append text to a dstr
*/
static void dstrAppend(struct dstr *p, const char *z, int divider){
  int n = (int)strlen(z);
  if( p->nUsed + n + 2 > p->nAlloc ){
    char *zNew;
    p->nAlloc = p->nAlloc*2 + n + 200;
    zNew = sqlite3_realloc(p->z, p->nAlloc);
    if( zNew==0 ){
      sqlite3_free(p->z);
      memset(p, 0, sizeof(*p));
      return;
    }
    p->z = zNew;
  }
  if( divider && p->nUsed>0 ){
    p->z[p->nUsed++] = divider;
  }
  memcpy(&p->z[p->nUsed], z, n+1);
  p->nUsed += n;
}

/*
** Invoked for each callback from sqlite3ExecFunc
*/
static int execFuncCallback(void *pData, int argc, char **argv, char **NotUsed){
  struct dstr *p = (struct dstr*)pData;
  int i;
  for(i=0; i<argc; i++){
    if( argv[i]==0 ){
      dstrAppend(p, "NULL", ' ');
    }else{
      dstrAppend(p, argv[i], ' ');
    }
  }
  return 0;
}

/*
** Implementation of the x_sqlite_exec() function.  This function takes
** a single argument and attempts to execute that argument as SQL code.
** This is illegal and should set the SQLITE_MISUSE flag on the database.
**
** 2004-Jan-07:  We have changed this to make it legal to call sqlite3_exec()
** from within a function call.  
** 
** This routine simulates the effect of having two threads attempt to
** use the same database at the same time.
*/
static void sqlite3ExecFunc(
  sqlite3_context *context, 
  int argc,  
  sqlite3_value **argv
){
  struct dstr x;
  memset(&x, 0, sizeof(x));
  (void)sqlite3_exec((sqlite3*)sqlite3_user_data(context),
      (char*)sqlite3_value_text(argv[0]),
      execFuncCallback, &x, 0);
  sqlite3_result_text(context, x.z, x.nUsed, SQLITE_TRANSIENT);
  sqlite3_free(x.z);
}

/*
** Implementation of tkt2213func(), a scalar function that takes exactly
** one argument. It has two interesting features:
**
** * It calls sqlite3_value_text() 3 times on the argument sqlite3_value*.
**   If the three pointers returned are not the same an SQL error is raised.
**
** * Otherwise it returns a copy of the text representation of its 
**   argument in such a way as the VDBE representation is a Mem* cell 
**   with the MEM_Term flag clear. 
**
** Ticket #2213 can therefore be tested by evaluating the following
** SQL expression:
**
**   tkt2213func(tkt2213func('a string'));
*/
static void tkt2213Function(
  sqlite3_context *context, 
  int argc,  
  sqlite3_value **argv
){
  int nText;
  unsigned char const *zText1;
  unsigned char const *zText2;
  unsigned char const *zText3;

  nText = sqlite3_value_bytes(argv[0]);
  zText1 = sqlite3_value_text(argv[0]);
  zText2 = sqlite3_value_text(argv[0]);
  zText3 = sqlite3_value_text(argv[0]);

  if( zText1!=zText2 || zText2!=zText3 ){
    sqlite3_result_error(context, "tkt2213 is not fixed", -1);
  }else{
    char *zCopy = (char *)sqlite3_malloc(nText);
    memcpy(zCopy, zText1, nText);
    sqlite3_result_text(context, zCopy, nText, sqlite3_free);
  }
}

/*
** The following SQL function takes 4 arguments.  The 2nd and
** 4th argument must be one of these strings:  'text', 'text16',
** or 'blob' corresponding to API functions
**
**      sqlite3_value_text()
**      sqlite3_value_text16()
**      sqlite3_value_blob()
**
** The third argument is a string, either 'bytes' or 'bytes16' or 'noop',
** corresponding to APIs:
**
**      sqlite3_value_bytes()
**      sqlite3_value_bytes16()
**      noop
**
** The APIs designated by the 2nd through 4th arguments are applied
** to the first argument in order.  If the pointers returned by the
** second and fourth are different, this routine returns 1.  Otherwise,
** this routine returns 0.
**
** This function is used to test to see when returned pointers from
** the _text(), _text16() and _blob() APIs become invalidated.
*/
static void ptrChngFunction(
  sqlite3_context *context, 
  int argc,  
  sqlite3_value **argv
){
  const void *p1, *p2;
  const char *zCmd;
  if( argc!=4 ) return;
  zCmd = (const char*)sqlite3_value_text(argv[1]);
  if( zCmd==0 ) return;
  if( strcmp(zCmd,"text")==0 ){
    p1 = (const void*)sqlite3_value_text(argv[0]);
#ifndef SQLITE_OMIT_UTF16
  }else if( strcmp(zCmd, "text16")==0 ){
    p1 = (const void*)sqlite3_value_text16(argv[0]);
#endif
  }else if( strcmp(zCmd, "blob")==0 ){
    p1 = (const void*)sqlite3_value_blob(argv[0]);
  }else{
    return;
  }
  zCmd = (const char*)sqlite3_value_text(argv[2]);
  if( zCmd==0 ) return;
  if( strcmp(zCmd,"bytes")==0 ){
    sqlite3_value_bytes(argv[0]);
#ifndef SQLITE_OMIT_UTF16
  }else if( strcmp(zCmd, "bytes16")==0 ){
    sqlite3_value_bytes16(argv[0]);
#endif
  }else if( strcmp(zCmd, "noop")==0 ){
    /* do nothing */
  }else{
    return;
  }
  zCmd = (const char*)sqlite3_value_text(argv[3]);
  if( zCmd==0 ) return;
  if( strcmp(zCmd,"text")==0 ){
    p2 = (const void*)sqlite3_value_text(argv[0]);
#ifndef SQLITE_OMIT_UTF16
  }else if( strcmp(zCmd, "text16")==0 ){
    p2 = (const void*)sqlite3_value_text16(argv[0]);
#endif
  }else if( strcmp(zCmd, "blob")==0 ){
    p2 = (const void*)sqlite3_value_blob(argv[0]);
  }else{
    return;
  }
  sqlite3_result_int(context, p1!=p2);
}

/*
** This SQL function returns a different answer each time it is called, even if
** the arguments are the same.
*/
static void nondeterministicFunction(
  sqlite3_context *context, 
  int argc,  
  sqlite3_value **argv
){
  static int cnt = 0;
  sqlite3_result_int(context, cnt++);
}

/*
** This SQL function returns the integer value of its argument as a MEM_IntReal
** value.
*/
static void intrealFunction(
  sqlite3_context *context, 
  int argc,  
  sqlite3_value **argv
){
  sqlite3_int64 v = sqlite3_value_int64(argv[0]);
  sqlite3_result_int64(context, v);
  sqlite3_test_control(SQLITE_TESTCTRL_RESULT_INTREAL, context);
}

/*
** These SQL functions attempt to return a value (their first argument)
** that has been modified to have multiple datatypes.  For example both
** TEXT and INTEGER.
*/
static void addTextTypeFunction(
  sqlite3_context *context, 
  int argc,  
  sqlite3_value **argv
){
  (void)sqlite3_value_text(argv[0]);
  (void)argc;
  sqlite3_result_value(context, argv[0]);
}
static void addIntTypeFunction(
  sqlite3_context *context, 
  int argc,  
  sqlite3_value **argv
){
  (void)sqlite3_value_int64(argv[0]);
  (void)argc;
  sqlite3_result_value(context, argv[0]);
}
static void addRealTypeFunction(
  sqlite3_context *context, 
  int argc,  
  sqlite3_value **argv
){
  (void)sqlite3_value_double(argv[0]);
  (void)argc;
  sqlite3_result_value(context, argv[0]);
}

/*
** SQL function:  strtod(X)
**
** Use the C-library strtod() function to convert string X into a double.
** Used for comparing the accuracy of SQLite's internal text-to-float conversion
** routines against the C-library.
*/
static void shellStrtod(
  sqlite3_context *pCtx,
  int nVal,
  sqlite3_value **apVal
){
  char *z = (char*)sqlite3_value_text(apVal[0]);
  UNUSED_PARAMETER(nVal);
  if( z==0 ) return;
  sqlite3_result_double(pCtx, strtod(z,0));
}

/*
** SQL function:  dtostr(X)
**
** Use the C-library printf() function to convert real value X into a string.
** Used for comparing the accuracy of SQLite's internal float-to-text conversion
** routines against the C-library.
*/
static void shellDtostr(
  sqlite3_context *pCtx,
  int nVal,
  sqlite3_value **apVal
){
  double r = sqlite3_value_double(apVal[0]);
  int n = nVal>=2 ? sqlite3_value_int(apVal[1]) : 26;
  char z[400];
  if( n<1 ) n = 1;
  if( n>350 ) n = 350;
  sprintf(z, "%#+.*e", n, r);
  sqlite3_result_text(pCtx, z, -1, SQLITE_TRANSIENT);
}


/*
** Usage:  sqlite3_create_function DB
**
** Call the sqlite3_create_function API on the given database in order
** to create a function named "x_coalesce".  This function does the same thing
** as the "coalesce" function.  This function also registers an SQL function
** named "x_sqlite_exec" that invokes sqlite3_exec().  Invoking sqlite3_exec()
** in this way is illegal recursion and should raise an SQLITE_MISUSE error.
** The effect is similar to trying to use the same database connection from
** two threads at the same time.
**
** The original motivation for this routine was to be able to call the
** sqlite3_create_function function while a query is in progress in order
** to test the SQLITE_MISUSE detection logic.
*/
static int SQLITE_TCLAPI test_create_function(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  int rc;
  sqlite3 *db;

  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " DB\"", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  rc = sqlite3_create_function(db, "x_coalesce", -1, SQLITE_UTF8, 0, 
        t1_ifnullFunc, 0, 0);
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "hex8", 1, SQLITE_UTF8 | SQLITE_DETERMINISTIC,
          0, hex8Func, 0, 0);
  }
#ifndef SQLITE_OMIT_UTF16
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "hex16", 1, SQLITE_UTF16 | SQLITE_DETERMINISTIC,
          0, hex16Func, 0, 0);
  }
#endif
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "tkt2213func", 1, SQLITE_ANY, 0, 
          tkt2213Function, 0, 0);
  }
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "pointer_change", 4, SQLITE_ANY, 0, 
          ptrChngFunction, 0, 0);
  }

  /* Functions counter1() and counter2() have the same implementation - they
  ** both return an ascending integer with each call.  But counter1() is marked
  ** as non-deterministic and counter2() is marked as deterministic.
  */
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "counter1", -1, SQLITE_UTF8,
          0, nondeterministicFunction, 0, 0);
  }
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "counter2", -1, SQLITE_UTF8|SQLITE_DETERMINISTIC,
          0, nondeterministicFunction, 0, 0);
  }

  /* The intreal() function converts its argument to an integer and returns
  ** it as a MEM_IntReal.
  */
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "intreal", 1, SQLITE_UTF8,
          0, intrealFunction, 0, 0);
  }

  /* The add_text_type(), add_int_type(), and add_real_type() functions
  ** attempt to return a value that has multiple datatypes.
  */
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "add_text_type", 1, SQLITE_UTF8,
          0, addTextTypeFunction, 0, 0);
  }
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "add_int_type", 1, SQLITE_UTF8,
          0, addIntTypeFunction, 0, 0);
  }
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "add_real_type", 1, SQLITE_UTF8,
          0, addRealTypeFunction, 0, 0);
  }

  /* Functions strtod() and dtostr() work as in the shell.  These routines
  ** use the standard C library to convert between floating point and
  ** text.  This is used to compare SQLite's internal conversion routines
  ** against the standard library conversion routines.
  **
  ** Both routines copy/pasted from the shell.c.in implementation
  ** on 2023-07-03.
  */
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "strtod", 1, SQLITE_UTF8, 0,
                                 shellStrtod, 0, 0);
  }
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "dtostr", 1, SQLITE_UTF8, 0,
                                 shellDtostr, 0, 0);
  }
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "dtostr", 2, SQLITE_UTF8, 0,
                                 shellDtostr, 0, 0);
  }

#ifndef SQLITE_OMIT_UTF16
  /* Use the sqlite3_create_function16() API here. Mainly for fun, but also 
  ** because it is not tested anywhere else. */
  if( rc==SQLITE_OK ){
    const void *zUtf16;
    sqlite3_value *pVal;
    sqlite3_mutex_enter(db->mutex);
    pVal = sqlite3ValueNew(db);
    sqlite3ValueSetStr(pVal, -1, "x_sqlite_exec", SQLITE_UTF8, SQLITE_STATIC);
    zUtf16 = sqlite3ValueText(pVal, SQLITE_UTF16NATIVE);
    if( db->mallocFailed ){
      rc = SQLITE_NOMEM;
    }else{
      rc = sqlite3_create_function16(db, zUtf16, 
                1, SQLITE_UTF16, db, sqlite3ExecFunc, 0, 0);
    }
    sqlite3ValueFree(pVal);
    sqlite3_mutex_leave(db->mutex);
  }
#endif

  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), 0);
  return TCL_OK;
}

/*
** Usage:  sqlite3_drop_modules DB ?NAME ...?
**
** Invoke the sqlite3_drop_modules(D,L) interface on database
** connection DB, in order to drop all modules except those named in
** the argument.
*/
static int SQLITE_TCLAPI test_drop_modules(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;

  if( argc<2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " DB\"", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
#ifndef SQLITE_OMIT_VIRTUALTABLE
  sqlite3_drop_modules(db, argc>2 ? (const char**)(argv+2) : 0);
#endif
  return TCL_OK;
}

/*
** Routines to implement the x_count() aggregate function.
**
** x_count() counts the number of non-null arguments.  But there are
** some twists for testing purposes.
**
** If the argument to x_count() is 40 then a UTF-8 error is reported
** on the step function.  If x_count(41) is seen, then a UTF-16 error
** is reported on the step function.  If the total count is 42, then
** a UTF-8 error is reported on the finalize function.
*/
typedef struct t1CountCtx t1CountCtx;
struct t1CountCtx {
  int n;
};
static void t1CountStep(
  sqlite3_context *context,
  int argc,
  sqlite3_value **argv
){
  t1CountCtx *p;
  p = sqlite3_aggregate_context(context, sizeof(*p));
  if( (argc==0 || SQLITE_NULL!=sqlite3_value_type(argv[0]) ) && p ){
    p->n++;
  }
  if( argc>0 ){
    int v = sqlite3_value_int(argv[0]);
    if( v==40 ){
      sqlite3_result_error(context, "value of 40 handed to x_count", -1);
#ifndef SQLITE_OMIT_UTF16
    }else if( v==41 ){
      const char zUtf16ErrMsg[] = { 0, 0x61, 0, 0x62, 0, 0x63, 0, 0, 0};
      sqlite3_result_error16(context, &zUtf16ErrMsg[1-SQLITE_BIGENDIAN], -1);
#endif
    }
  }
}   
static void t1CountFinalize(sqlite3_context *context){
  t1CountCtx *p;
  p = sqlite3_aggregate_context(context, sizeof(*p));
  if( p ){
    if( p->n==42 ){
      sqlite3_result_error(context, "x_count totals to 42", -1);
    }else{
      sqlite3_result_int(context, p ? p->n : 0);
    }
  }
}

#ifndef SQLITE_OMIT_DEPRECATED
static void legacyCountStep(
  sqlite3_context *context,
  int argc,
  sqlite3_value **argv
){
  /* no-op */
}

static void legacyCountFinalize(sqlite3_context *context){
  sqlite3_result_int(context, sqlite3_aggregate_count(context));
}
#endif

/*
** Usage:  sqlite3_create_aggregate DB
**
** Call the sqlite3_create_function API on the given database in order
** to create a function named "x_count".  This function is similar
** to the built-in count() function, with a few special quirks
** for testing the sqlite3_result_error() APIs.
**
** The original motivation for this routine was to be able to call the
** sqlite3_create_aggregate function while a query is in progress in order
** to test the SQLITE_MISUSE detection logic.  See misuse.test.
**
** This routine was later extended to test the use of sqlite3_result_error()
** within aggregate functions.
**
** Later: It is now also extended to register the aggregate function
** "legacy_count()" with the supplied database handle. This is used
** to test the deprecated sqlite3_aggregate_count() API.
*/
static int SQLITE_TCLAPI test_create_aggregate(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  int rc;
  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " FILENAME\"", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  rc = sqlite3_create_function(db, "x_count", 0, SQLITE_UTF8, 0, 0,
      t1CountStep,t1CountFinalize);
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "x_count", 1, SQLITE_UTF8, 0, 0,
        t1CountStep,t1CountFinalize);
  }
#ifndef SQLITE_OMIT_DEPRECATED
  if( rc==SQLITE_OK ){
    rc = sqlite3_create_function(db, "legacy_count", 0, SQLITE_ANY, 0, 0,
        legacyCountStep, legacyCountFinalize
    );
  }
#endif
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), 0);
  return TCL_OK;
}


/*
** Usage:  printf TEXT
**
** Send output to printf.  Use this rather than puts to merge the output
** in the correct sequence with debugging printfs inserted into C code.
** Puts uses a separate buffer and debugging statements will be out of
** sequence if it is used.
*/
static int SQLITE_TCLAPI test_printf(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " TEXT\"", 0);
    return TCL_ERROR;
  }
  printf("%s\n", argv[1]);
  return TCL_OK;
}



/*
** Usage:  sqlite3_mprintf_int FORMAT INTEGER INTEGER INTEGER
**
** Call mprintf with three integer arguments
*/
static int SQLITE_TCLAPI sqlite3_mprintf_int(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  int a[3], i;
  char *z;
  if( argc!=5 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " FORMAT INT INT INT\"", 0);
    return TCL_ERROR;
  }
  for(i=2; i<5; i++){
    if( Tcl_GetInt(interp, argv[i], &a[i-2]) ) return TCL_ERROR;
  }
  z = sqlite3_mprintf(argv[1], a[0], a[1], a[2]);
  Tcl_AppendResult(interp, z, 0);
  sqlite3_free(z);
  return TCL_OK;
}

/*
** Usage:  sqlite3_mprintf_int64 FORMAT INTEGER INTEGER INTEGER
**
** Call mprintf with three 64-bit integer arguments
*/
static int SQLITE_TCLAPI sqlite3_mprintf_int64(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  int i;
  sqlite_int64 a[3];
  char *z;
  if( argc!=5 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " FORMAT INT INT INT\"", 0);
    return TCL_ERROR;
  }
  for(i=2; i<5; i++){
    if( sqlite3Atoi64(argv[i], &a[i-2], sqlite3Strlen30(argv[i]), SQLITE_UTF8) ){
      Tcl_AppendResult(interp, "argument is not a valid 64-bit integer", 0);
      return TCL_ERROR;
    }
  }
  z = sqlite3_mprintf(argv[1], a[0], a[1], a[2]);
  Tcl_AppendResult(interp, z, 0);
  sqlite3_free(z);
  return TCL_OK;
}

/*
** Usage:  sqlite3_mprintf_long FORMAT INTEGER INTEGER INTEGER
**
** Call mprintf with three long integer arguments.   This might be the
** same as sqlite3_mprintf_int or sqlite3_mprintf_int64, depending on
** platform.
*/
static int SQLITE_TCLAPI sqlite3_mprintf_long(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  int i;
  long int a[3];
  int b[3];
  char *z;
  if( argc!=5 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " FORMAT INT INT INT\"", 0);
    return TCL_ERROR;
  }
  for(i=2; i<5; i++){
    if( Tcl_GetInt(interp, argv[i], &b[i-2]) ) return TCL_ERROR;
    a[i-2] = (long int)b[i-2];
    a[i-2] &= (((u64)1)<<(sizeof(int)*8))-1;
  }
  z = sqlite3_mprintf(argv[1], a[0], a[1], a[2]);
  Tcl_AppendResult(interp, z, 0);
  sqlite3_free(z);
  return TCL_OK;
}

/*
** Usage:  sqlite3_mprintf_str FORMAT INTEGER INTEGER STRING
**
** Call mprintf with two integer arguments and one string argument
*/
static int SQLITE_TCLAPI sqlite3_mprintf_str(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  int a[3], i;
  char *z;
  if( argc<4 || argc>5 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " FORMAT INT INT ?STRING?\"", 0);
    return TCL_ERROR;
  }
  for(i=2; i<4; i++){
    if( Tcl_GetInt(interp, argv[i], &a[i-2]) ) return TCL_ERROR;
  }
  z = sqlite3_mprintf(argv[1], a[0], a[1], argc>4 ? argv[4] : NULL);
  Tcl_AppendResult(interp, z, 0);
  sqlite3_free(z);
  return TCL_OK;
}

/*
** Usage:  sqlite3_snprintf_str INTEGER FORMAT INTEGER INTEGER STRING
**
** Call mprintf with two integer arguments and one string argument
*/
static int SQLITE_TCLAPI sqlite3_snprintf_str(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  int a[3], i;
  int n;
  char *z;
  if( argc<5 || argc>6 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " INT FORMAT INT INT ?STRING?\"", 0);
    return TCL_ERROR;
  }
  if( Tcl_GetInt(interp, argv[1], &n) ) return TCL_ERROR;
  if( n<0 ){
    Tcl_AppendResult(interp, "N must be non-negative", 0);
    return TCL_ERROR;
  }
  for(i=3; i<5; i++){
    if( Tcl_GetInt(interp, argv[i], &a[i-3]) ) return TCL_ERROR;
  }
  z = sqlite3_malloc( n+1 );
  sqlite3_snprintf(n, z, argv[2], a[0], a[1], argc>4 ? argv[5] : NULL);
  Tcl_AppendResult(interp, z, 0);
  sqlite3_free(z);
  return TCL_OK;
}

/*
** Usage:  sqlite3_mprintf_double FORMAT INTEGER INTEGER DOUBLE
**
** Call mprintf with two integer arguments and one double argument
*/
static int SQLITE_TCLAPI sqlite3_mprintf_double(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  int a[3], i;
  double r;
  char *z;
  if( argc!=5 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " FORMAT INT INT DOUBLE\"", 0);
    return TCL_ERROR;
  }
  for(i=2; i<4; i++){
    if( Tcl_GetInt(interp, argv[i], &a[i-2]) ) return TCL_ERROR;
  }
  if( Tcl_GetDouble(interp, argv[4], &r) ) return TCL_ERROR;
  z = sqlite3_mprintf(argv[1], a[0], a[1], r);
  Tcl_AppendResult(interp, z, 0);
  sqlite3_free(z);
  return TCL_OK;
}

/*
** Usage:  sqlite3_mprintf_scaled FORMAT DOUBLE DOUBLE
**
** Call mprintf with a single double argument which is the product of the
** two arguments given above.  This is used to generate overflow and underflow
** doubles to test that they are converted properly.
*/
static int SQLITE_TCLAPI sqlite3_mprintf_scaled(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  int i;
  double r[2];
  char *z;
  if( argc!=4 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " FORMAT DOUBLE DOUBLE\"", 0);
    return TCL_ERROR;
  }
  for(i=2; i<4; i++){
    if( Tcl_GetDouble(interp, argv[i], &r[i-2]) ) return TCL_ERROR;
  }
  z = sqlite3_mprintf(argv[1], r[0]*r[1]);
  Tcl_AppendResult(interp, z, 0);
  sqlite3_free(z);
  return TCL_OK;
}

/*
** Usage:  sqlite3_mprintf_stronly FORMAT STRING
**
** Call mprintf with a single double argument which is the product of the
** two arguments given above.  This is used to generate overflow and underflow
** doubles to test that they are converted properly.
*/
static int SQLITE_TCLAPI sqlite3_mprintf_stronly(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  char *z;
  if( argc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " FORMAT STRING\"", 0);
    return TCL_ERROR;
  }
  z = sqlite3_mprintf(argv[1], argv[2]);
  Tcl_AppendResult(interp, z, 0);
  sqlite3_free(z);
  return TCL_OK;
}

/*
** Usage:  sqlite3_mprintf_hexdouble FORMAT HEX
**
** Call mprintf with a single double argument which is derived from the
** hexadecimal encoding of an IEEE double.
*/
static int SQLITE_TCLAPI sqlite3_mprintf_hexdouble(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  char *z;
  double r;
  unsigned int x1, x2;
  sqlite_uint64 d;
  if( argc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0],
       " FORMAT STRING\"", 0);
    return TCL_ERROR;
  }
  if( sscanf(argv[2], "%08x%08x", &x2, &x1)!=2 ){
    Tcl_AppendResult(interp, "2nd argument should be 16-characters of hex", 0);
    return TCL_ERROR;
  }
  d = x2;
  d = (d<<32) + x1;
  memcpy(&r, &d, sizeof(r));
  z = sqlite3_mprintf(argv[1], r);
  Tcl_AppendResult(interp, z, 0);
  sqlite3_free(z);
  return TCL_OK;
}

/*
** Usage: sqlite3_enable_shared_cache ?BOOLEAN?
**
*/
#if !defined(SQLITE_OMIT_SHARED_CACHE)
static int SQLITE_TCLAPI test_enable_shared(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int rc;
  int enable;
  int ret = 0;

  if( objc!=2 && objc!=1 ){
    Tcl_WrongNumArgs(interp, 1, objv, "?BOOLEAN?");
    return TCL_ERROR;
  }
  ret = sqlite3GlobalConfig.sharedCacheEnabled;

  if( objc==2 ){
    if( Tcl_GetBooleanFromObj(interp, objv[1], &enable) ){
      return TCL_ERROR;
    }
    rc = sqlite3_enable_shared_cache(enable);
    if( rc!=SQLITE_OK ){
      Tcl_SetResult(interp, (char *)sqlite3ErrStr(rc), TCL_STATIC);
      return TCL_ERROR;
    }
  }
  Tcl_SetObjResult(interp, Tcl_NewBooleanObj(ret));
  return TCL_OK;
}
#endif



/*
** Usage: sqlite3_extended_result_codes   DB    BOOLEAN
**
*/
static int SQLITE_TCLAPI test_extended_result_codes(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int enable;
  sqlite3 *db;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB BOOLEAN");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  if( Tcl_GetBooleanFromObj(interp, objv[2], &enable) ) return TCL_ERROR;
  sqlite3_extended_result_codes(db, enable);
  return TCL_OK;
}

/*
** Usage: sqlite3_libversion_number
**
*/
static int SQLITE_TCLAPI test_libversion_number(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  Tcl_SetObjResult(interp, Tcl_NewIntObj(sqlite3_libversion_number()));
  return TCL_OK;
}

/*
** Usage: sqlite3_table_column_metadata DB dbname tblname colname
**
*/
static int SQLITE_TCLAPI test_table_column_metadata(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  const char *zDb;
  const char *zTbl;
  const char *zCol;
  int rc;
  Tcl_Obj *pRet;

  const char *zDatatype;
  const char *zCollseq;
  int notnull;
  int primarykey;
  int autoincrement;

  if( objc!=5 && objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB dbname tblname colname");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zDb = Tcl_GetString(objv[2]);
  zTbl = Tcl_GetString(objv[3]);
  zCol = objc==5 ? Tcl_GetString(objv[4]) : 0;

  if( strlen(zDb)==0 ) zDb = 0;

  rc = sqlite3_table_column_metadata(db, zDb, zTbl, zCol, 
      &zDatatype, &zCollseq, &notnull, &primarykey, &autoincrement);

  if( rc!=SQLITE_OK ){
    Tcl_AppendResult(interp, sqlite3_errmsg(db), 0);
    return TCL_ERROR;
  }

  pRet = Tcl_NewObj();
  Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj(zDatatype, -1));
  Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj(zCollseq, -1));
  Tcl_ListObjAppendElement(0, pRet, Tcl_NewIntObj(notnull));
  Tcl_ListObjAppendElement(0, pRet, Tcl_NewIntObj(primarykey));
  Tcl_ListObjAppendElement(0, pRet, Tcl_NewIntObj(autoincrement));
  Tcl_SetObjResult(interp, pRet);

  return TCL_OK;
}

#ifndef SQLITE_OMIT_INCRBLOB

static int SQLITE_TCLAPI blobHandleFromObj(
  Tcl_Interp *interp, 
  Tcl_Obj *pObj,
  sqlite3_blob **ppBlob
){
  char *z;
  Tcl_Size n;

  z = Tcl_GetStringFromObj(pObj, &n);
  if( n==0 ){
    *ppBlob = 0;
  }else{
    int notUsed;
    Tcl_Channel channel;
    ClientData instanceData;
    
    channel = Tcl_GetChannel(interp, z, &notUsed);
    if( !channel ) return TCL_ERROR;

    Tcl_Flush(channel);
    Tcl_Seek(channel, 0, SEEK_SET);

    instanceData = Tcl_GetChannelInstanceData(channel);
    *ppBlob = *((sqlite3_blob **)instanceData);
  }

  return TCL_OK;
}

static int SQLITE_TCLAPI test_blob_reopen(
  ClientData clientData, /* Not used */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  Tcl_WideInt iRowid;
  sqlite3_blob *pBlob;
  int rc;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "CHANNEL ROWID");
    return TCL_ERROR;
  }

  if( blobHandleFromObj(interp, objv[1], &pBlob) ) return TCL_ERROR;
  if( Tcl_GetWideIntFromObj(interp, objv[2], &iRowid) ) return TCL_ERROR;

  rc = sqlite3_blob_reopen(pBlob, iRowid);
  if( rc!=SQLITE_OK ){
    Tcl_SetResult(interp, (char *)sqlite3ErrName(rc), TCL_VOLATILE);
  }

  return (rc==SQLITE_OK ? TCL_OK : TCL_ERROR);
}

#endif

/*
** Usage: sqlite3_create_collation_v2 DB-HANDLE NAME CMP-PROC DEL-PROC
**
**   This Tcl proc is used for testing the experimental
**   sqlite3_create_collation_v2() interface.
*/
struct TestCollationX {
  Tcl_Interp *interp;
  Tcl_Obj *pCmp;
  Tcl_Obj *pDel;
};
typedef struct TestCollationX TestCollationX;
static void testCreateCollationDel(void *pCtx){
  TestCollationX *p = (TestCollationX *)pCtx;

  int rc = Tcl_EvalObjEx(p->interp, p->pDel, TCL_EVAL_DIRECT|TCL_EVAL_GLOBAL);
  if( rc!=TCL_OK ){
    Tcl_BackgroundError(p->interp);
  }

  Tcl_DecrRefCount(p->pCmp);
  Tcl_DecrRefCount(p->pDel);
  sqlite3_free((void *)p);
}
static int testCreateCollationCmp(
  void *pCtx,
  int nLeft,
  const void *zLeft,
  int nRight,
  const void *zRight
){
  TestCollationX *p = (TestCollationX *)pCtx;
  Tcl_Obj *pScript = Tcl_DuplicateObj(p->pCmp);
  int iRes = 0;

  Tcl_IncrRefCount(pScript);
  Tcl_ListObjAppendElement(0, pScript, Tcl_NewStringObj((char *)zLeft, nLeft));
  Tcl_ListObjAppendElement(0, pScript, Tcl_NewStringObj((char *)zRight,nRight));

  if( TCL_OK!=Tcl_EvalObjEx(p->interp, pScript, TCL_EVAL_DIRECT|TCL_EVAL_GLOBAL)
   || TCL_OK!=Tcl_GetIntFromObj(p->interp, Tcl_GetObjResult(p->interp), &iRes)
  ){
    Tcl_BackgroundError(p->interp);
  }
  Tcl_DecrRefCount(pScript);

  return iRes;
}
static int SQLITE_TCLAPI test_create_collation_v2(
  ClientData clientData, /* Not used */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  TestCollationX *p;
  sqlite3 *db;
  int rc;

  if( objc!=5 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB-HANDLE NAME CMP-PROC DEL-PROC");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;

  p = (TestCollationX *)sqlite3_malloc(sizeof(TestCollationX));
  p->pCmp = objv[3];
  p->pDel = objv[4];
  p->interp = interp;
  Tcl_IncrRefCount(p->pCmp);
  Tcl_IncrRefCount(p->pDel);

  rc = sqlite3_create_collation_v2(db, Tcl_GetString(objv[2]), 16, 
      (void *)p, testCreateCollationCmp, testCreateCollationDel
  );
  if( rc!=SQLITE_MISUSE ){
    Tcl_AppendResult(interp, "sqlite3_create_collate_v2() failed to detect "
      "an invalid encoding", (char*)0);
    return TCL_ERROR;
  }
  rc = sqlite3_create_collation_v2(db, Tcl_GetString(objv[2]), SQLITE_UTF8, 
      (void *)p, testCreateCollationCmp, testCreateCollationDel
  );
  return TCL_OK;
}

/*
** USAGE: sqlite3_create_function_v2 DB NAME NARG ENC ?SWITCHES?
**
** Available switches are:
**
**   -func    SCRIPT
**   -step    SCRIPT
**   -final   SCRIPT
**   -destroy SCRIPT
*/
typedef struct CreateFunctionV2 CreateFunctionV2;
struct CreateFunctionV2 {
  Tcl_Interp *interp;
  Tcl_Obj *pFunc;                 /* Script for function invocation */
  Tcl_Obj *pStep;                 /* Script for agg. step invocation */
  Tcl_Obj *pFinal;                /* Script for agg. finalization invocation */
  Tcl_Obj *pDestroy;              /* Destructor script */
};
static void cf2Func(sqlite3_context *ctx, int nArg, sqlite3_value **aArg){
}
static void cf2Step(sqlite3_context *ctx, int nArg, sqlite3_value **aArg){
}
static void cf2Final(sqlite3_context *ctx){
}
static void cf2Destroy(void *pUser){
  CreateFunctionV2 *p = (CreateFunctionV2 *)pUser;

  if( p->interp && p->pDestroy ){
    int rc = Tcl_EvalObjEx(p->interp, p->pDestroy, 0);
    if( rc!=TCL_OK ) Tcl_BackgroundError(p->interp);
  }

  if( p->pFunc ) Tcl_DecrRefCount(p->pFunc); 
  if( p->pStep ) Tcl_DecrRefCount(p->pStep); 
  if( p->pFinal ) Tcl_DecrRefCount(p->pFinal); 
  if( p->pDestroy ) Tcl_DecrRefCount(p->pDestroy); 
  sqlite3_free(p);
}
static int SQLITE_TCLAPI test_create_function_v2(
  ClientData clientData,          /* Not used */
  Tcl_Interp *interp,             /* The invoking TCL interpreter */
  int objc,                       /* Number of arguments */
  Tcl_Obj *CONST objv[]           /* Command arguments */
){
  sqlite3 *db;
  const char *zFunc;
  int nArg;
  int enc;
  CreateFunctionV2 *p;
  int i;
  int rc;

  struct EncTable {
    const char *zEnc;
    int enc;
  } aEnc[] = {
    {"utf8",    SQLITE_UTF8 },
    {"utf16",   SQLITE_UTF16 },
    {"utf16le", SQLITE_UTF16LE },
    {"utf16be", SQLITE_UTF16BE },
    {"any",     SQLITE_ANY },
    {"0", 0 },
    {0, 0 }
  };

  if( objc<5 || (objc%2)==0 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB NAME NARG ENC SWITCHES...");
    return TCL_ERROR;
  }

  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zFunc = Tcl_GetString(objv[2]);
  if( Tcl_GetIntFromObj(interp, objv[3], &nArg) ) return TCL_ERROR;
  if( Tcl_GetIndexFromObjStruct(interp, objv[4], aEnc, sizeof(aEnc[0]), 
          "encoding", 0, &enc)
  ){
    return TCL_ERROR;
  }
  enc = aEnc[enc].enc;

  p = sqlite3_malloc(sizeof(CreateFunctionV2));
  assert( p );
  memset(p, 0, sizeof(CreateFunctionV2));
  p->interp = interp;

  for(i=5; i<objc; i+=2){
    int iSwitch;
    const char *azSwitch[] = {"-func", "-step", "-final", "-destroy", 0};
    if( Tcl_GetIndexFromObj(interp, objv[i], azSwitch, "switch", 0, &iSwitch) ){
      sqlite3_free(p);
      return TCL_ERROR;
    }

    switch( iSwitch ){
      case 0: p->pFunc = objv[i+1];      break;
      case 1: p->pStep = objv[i+1];      break;
      case 2: p->pFinal = objv[i+1];     break;
      case 3: p->pDestroy = objv[i+1];   break;
    }
  }
  if( p->pFunc ) p->pFunc = Tcl_DuplicateObj(p->pFunc); 
  if( p->pStep ) p->pStep = Tcl_DuplicateObj(p->pStep); 
  if( p->pFinal ) p->pFinal = Tcl_DuplicateObj(p->pFinal); 
  if( p->pDestroy ) p->pDestroy = Tcl_DuplicateObj(p->pDestroy); 

  if( p->pFunc ) Tcl_IncrRefCount(p->pFunc); 
  if( p->pStep ) Tcl_IncrRefCount(p->pStep); 
  if( p->pFinal ) Tcl_IncrRefCount(p->pFinal); 
  if( p->pDestroy ) Tcl_IncrRefCount(p->pDestroy); 

  rc = sqlite3_create_function_v2(db, zFunc, nArg, enc, (void *)p, 
      (p->pFunc ? cf2Func : 0),
      (p->pStep ? cf2Step : 0),
      (p->pFinal ? cf2Final : 0),
      cf2Destroy
  );
  if( rc!=SQLITE_OK ){
    Tcl_ResetResult(interp);
    Tcl_AppendResult(interp, sqlite3ErrName(rc), 0);
    return TCL_ERROR;
  }
  return TCL_OK;
}

/*
** Usage: sqlite3_load_extension DB-HANDLE FILE ?PROC?
*/
static int SQLITE_TCLAPI test_load_extension(
  ClientData clientData, /* Not used */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  Tcl_CmdInfo cmdInfo;
  sqlite3 *db;
  int rc;
  char *zDb;
  char *zFile;
  char *zProc = 0;
  char *zErr = 0;

  if( objc!=4 && objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB-HANDLE FILE ?PROC?");
    return TCL_ERROR;
  }
  zDb = Tcl_GetString(objv[1]);
  zFile = Tcl_GetString(objv[2]);
  if( objc==4 ){
    zProc = Tcl_GetString(objv[3]);
  }

  /* Extract the C database handle from the Tcl command name */
  if( !Tcl_GetCommandInfo(interp, zDb, &cmdInfo) ){
    Tcl_AppendResult(interp, "command not found: ", zDb, (char*)0);
    return TCL_ERROR;
  }
  db = ((struct SqliteDb*)cmdInfo.objClientData)->db;
  assert(db);

  /* Call the underlying C function. If an error occurs, set rc to 
  ** TCL_ERROR and load any error string into the interpreter. If no 
  ** error occurs, set rc to TCL_OK.
  */
#ifdef SQLITE_OMIT_LOAD_EXTENSION
  rc = SQLITE_ERROR;
  zErr = sqlite3_mprintf("this build omits sqlite3_load_extension()");
  (void)zProc;
  (void)zFile;
#else
  rc = sqlite3_load_extension(db, zFile, zProc, &zErr);
#endif
  if( rc!=SQLITE_OK ){
    Tcl_SetResult(interp, zErr ? zErr : "", TCL_VOLATILE);
    rc = TCL_ERROR;
  }else{
    rc = TCL_OK;
  }
  sqlite3_free(zErr);

  return rc;
}

/*
** Usage: sqlite3_enable_load_extension DB-HANDLE ONOFF
*/
static int SQLITE_TCLAPI test_enable_load(
  ClientData clientData, /* Not used */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  Tcl_CmdInfo cmdInfo;
  sqlite3 *db;
  char *zDb;
  int onoff;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB-HANDLE ONOFF");
    return TCL_ERROR;
  }
  zDb = Tcl_GetString(objv[1]);

  /* Extract the C database handle from the Tcl command name */
  if( !Tcl_GetCommandInfo(interp, zDb, &cmdInfo) ){
    Tcl_AppendResult(interp, "command not found: ", zDb, (char*)0);
    return TCL_ERROR;
  }
  db = ((struct SqliteDb*)cmdInfo.objClientData)->db;
  assert(db);

  /* Get the onoff parameter */
  if( Tcl_GetBooleanFromObj(interp, objv[2], &onoff) ){
    return TCL_ERROR;
  }

#ifdef SQLITE_OMIT_LOAD_EXTENSION
  Tcl_AppendResult(interp, "this build omits sqlite3_load_extension()");
  return TCL_ERROR;
#else
  sqlite3_enable_load_extension(db, onoff);
  return TCL_OK;
#endif
}

/*
** Usage:  sqlite_abort
**
** Shutdown the process immediately.  This is not a clean shutdown.
** This command is used to test the recoverability of a database in
** the event of a program crash.
*/
static int SQLITE_TCLAPI sqlite_abort(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
#if defined(_MSC_VER)
  /* We do this, otherwise the test will halt with a popup message
   * that we have to click away before the test will continue.
   */
  _set_abort_behavior( 0, _CALL_REPORTFAULT );
#endif
  exit(255);
  assert( interp==0 );   /* This will always fail */
  return TCL_OK;
}

/*
** The following routine is a user-defined SQL function whose purpose
** is to test the sqlite_set_result() API.
*/
static void testFunc(sqlite3_context *context, int argc, sqlite3_value **argv){
  while( argc>=2 ){
    const char *zArg0 = (char*)sqlite3_value_text(argv[0]);
    if( zArg0 ){
      if( 0==sqlite3StrICmp(zArg0, "int") ){
        sqlite3_result_int(context, sqlite3_value_int(argv[1]));
      }else if( sqlite3StrICmp(zArg0,"int64")==0 ){
        sqlite3_result_int64(context, sqlite3_value_int64(argv[1]));
      }else if( sqlite3StrICmp(zArg0,"string")==0 ){
        sqlite3_result_text(context, (char*)sqlite3_value_text(argv[1]), -1,
            SQLITE_TRANSIENT);
      }else if( sqlite3StrICmp(zArg0,"double")==0 ){
        sqlite3_result_double(context, sqlite3_value_double(argv[1]));
      }else if( sqlite3StrICmp(zArg0,"null")==0 ){
        sqlite3_result_null(context);
      }else if( sqlite3StrICmp(zArg0,"value")==0 ){
        sqlite3_result_value(context, argv[sqlite3_value_int(argv[1])]);
      }else{
        goto error_out;
      }
    }else{
      goto error_out;
    }
    argc -= 2;
    argv += 2;
  }
  return;

error_out:
  sqlite3_result_error(context,"first argument should be one of: "
      "int int64 string double null value", -1);
}

/*
** Usage:   sqlite_register_test_function  DB  NAME
**
** Register the test SQL function on the database DB under the name NAME.
*/
static int SQLITE_TCLAPI test_register_func(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3 *db;
  int rc;
  if( argc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
       " DB FUNCTION-NAME", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  rc = sqlite3_create_function(db, argv[2], -1, SQLITE_UTF8, 0, 
      testFunc, 0, 0);
  if( rc!=0 ){
    Tcl_AppendResult(interp, sqlite3ErrStr(rc), 0);
    return TCL_ERROR;
  }
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  return TCL_OK;
}

/*
** Usage:  sqlite3_finalize  STMT 
**
** Finalize a statement handle.
*/
static int SQLITE_TCLAPI test_finalize(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int rc;
  sqlite3 *db = 0;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " <STMT>", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;

  if( pStmt ){
    db = StmtToDb(pStmt);
  }
  rc = sqlite3_finalize(pStmt);
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), TCL_STATIC);
  if( db && sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  return TCL_OK;
}

/*
** Usage:  sqlite3_stmt_status  STMT  CODE  RESETFLAG
**
** Get the value of a status counter from a statement.
*/
static int SQLITE_TCLAPI test_stmt_status(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int iValue;
  int i, op = 0, resetFlag;
  const char *zOpName;
  sqlite3_stmt *pStmt;

  static const struct {
    const char *zName;
    int op;
  } aOp[] = {
    { "SQLITE_STMTSTATUS_FULLSCAN_STEP",   SQLITE_STMTSTATUS_FULLSCAN_STEP   },
    { "SQLITE_STMTSTATUS_SORT",            SQLITE_STMTSTATUS_SORT            },
    { "SQLITE_STMTSTATUS_AUTOINDEX",       SQLITE_STMTSTATUS_AUTOINDEX       },
    { "SQLITE_STMTSTATUS_VM_STEP",         SQLITE_STMTSTATUS_VM_STEP         },
    { "SQLITE_STMTSTATUS_REPREPARE",       SQLITE_STMTSTATUS_REPREPARE       },
    { "SQLITE_STMTSTATUS_RUN",             SQLITE_STMTSTATUS_RUN             },
    { "SQLITE_STMTSTATUS_MEMUSED",         SQLITE_STMTSTATUS_MEMUSED         },
  };
  if( objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT PARAMETER RESETFLAG");
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  zOpName = Tcl_GetString(objv[2]);
  for(i=0; i<ArraySize(aOp); i++){
    if( strcmp(aOp[i].zName, zOpName)==0 ){
      op = aOp[i].op;
      break;
    }
  }
  if( i>=ArraySize(aOp) ){
    if( Tcl_GetIntFromObj(interp, objv[2], &op) ) return TCL_ERROR;
  }
  if( Tcl_GetBooleanFromObj(interp, objv[3], &resetFlag) ) return TCL_ERROR;
  iValue = sqlite3_stmt_status(pStmt, op, resetFlag);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(iValue));
  return TCL_OK;
}

#ifdef SQLITE_ENABLE_STMT_SCANSTATUS
/*
** Usage:  sqlite3_stmt_scanstatus ?-flags FLAGS? STMT IDX
*/
static int SQLITE_TCLAPI test_stmt_scanstatus(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;            /* First argument */
  int idx;                        /* Second argument */

  const char *zName;
  const char *zExplain;
  sqlite3_int64 nLoop;
  sqlite3_int64 nVisit;
  sqlite3_int64 nCycle;
  double rEst;
  int res;
  int flags = 0;
  int iSelectId = 0;
  int iParentId = 0;
  int bDebug = 0;

  if( objc==5 ){
    struct Flag {
      const char *zFlag;
      int flag;
    } aTbl[] = {
      {"complex", SQLITE_SCANSTAT_COMPLEX},
      {"debug", -1},
      {0, 0}
    };

    Tcl_Obj **aFlag = 0;
    Tcl_Size nFlag = 0;
    int ii;

    if( Tcl_ListObjGetElements(interp, objv[2], &nFlag, &aFlag) ){
      return TCL_ERROR;
    }
    for(ii=0; ii<(int)nFlag; ii++){
      int iVal = 0;
      res = Tcl_GetIndexFromObjStruct(
          interp, aFlag[ii], aTbl, sizeof(aTbl[0]), "flag", 0, &iVal
      );
      if( res ) return TCL_ERROR;
      if( aTbl[iVal].flag==-1 ){
        bDebug = 1;
      }else{
        flags |= aTbl[iVal].flag;
      }
    }
  }

  if( objc!=3 && objc!=5 ){
    Tcl_WrongNumArgs(interp, 1, objv, "-flags FLAGS STMT IDX");
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, Tcl_GetString(objv[objc-2]), &pStmt) 
   || Tcl_GetIntFromObj(interp, objv[objc-1], &idx)
  ){
    return TCL_ERROR;
  }

  if( bDebug && 0==(flags & SQLITE_SCANSTAT_COMPLEX) ){
    Tcl_SetObjResult(interp, 
        Tcl_NewStringObj("may not specify debug without complex", -1)
    );
    return TCL_ERROR;
  }

  if( idx<0 ){
    Tcl_Obj *pRet = Tcl_NewObj();
    res = sqlite3_stmt_scanstatus_v2(
        pStmt, -1, SQLITE_SCANSTAT_NCYCLE, flags, (void*)&nCycle
    );
    sqlite3_stmt_scanstatus_v2(
        pStmt, idx, SQLITE_SCANSTAT_NCYCLE, flags, (void*)&nCycle);
    Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("nCycle", -1));
    Tcl_ListObjAppendElement(0, pRet, Tcl_NewWideIntObj(nCycle));
    Tcl_SetObjResult(interp, pRet);
  }else{
    res = sqlite3_stmt_scanstatus_v2(
        pStmt, idx, SQLITE_SCANSTAT_NLOOP, flags, (void*)&nLoop
    );
    if( res==0 ){
      Tcl_Obj *pRet = Tcl_NewObj();
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("nLoop", -1));
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewWideIntObj(nLoop));
      sqlite3_stmt_scanstatus_v2(
          pStmt, idx, SQLITE_SCANSTAT_NVISIT, flags, (void*)&nVisit);
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("nVisit", -1));
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewWideIntObj(nVisit));
      sqlite3_stmt_scanstatus_v2(
          pStmt, idx, SQLITE_SCANSTAT_EST, flags, (void*)&rEst);
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("nEst", -1));
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewDoubleObj(rEst));
      sqlite3_stmt_scanstatus_v2(
          pStmt, idx, SQLITE_SCANSTAT_NAME, flags, (void*)&zName);
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("zName", -1));
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj(zName, -1));
      sqlite3_stmt_scanstatus_v2(
          pStmt, idx, SQLITE_SCANSTAT_EXPLAIN, flags, (void*)&zExplain);
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("zExplain", -1));
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj(zExplain, -1));
      sqlite3_stmt_scanstatus_v2(
          pStmt, idx, SQLITE_SCANSTAT_SELECTID, flags, (void*)&iSelectId);
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("iSelectId", -1));
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewIntObj(iSelectId));
      sqlite3_stmt_scanstatus_v2(
          pStmt, idx, SQLITE_SCANSTAT_PARENTID, flags, (void*)&iParentId);
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("iParentId", -1));
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewIntObj(iParentId));
      sqlite3_stmt_scanstatus_v2(
          pStmt, idx, SQLITE_SCANSTAT_NCYCLE, flags, (void*)&nCycle);
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("nCycle", -1));
      Tcl_ListObjAppendElement(0, pRet, Tcl_NewWideIntObj(nCycle));

      if( bDebug ){
        int ii;
        ScanStatus *pScan = &((Vdbe*)pStmt)->aScan[idx];
        Tcl_Obj *pRange = Tcl_NewObj();
        Tcl_Obj *pCsr = Tcl_NewObj();

        Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("debug_loop", -1));
        Tcl_ListObjAppendElement(0, pRet, Tcl_NewIntObj(pScan->addrLoop));
        Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("debug_visit", -1));
        Tcl_ListObjAppendElement(0, pRet, Tcl_NewIntObj(pScan->addrVisit));
        Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("debug_explain",-1));
        Tcl_ListObjAppendElement(0, pRet, Tcl_NewIntObj(pScan->addrExplain));
        for(ii=0; ii<ArraySize(pScan->aAddrRange)/2; ii++){
          int iStart = pScan->aAddrRange[ii*2];
          int iEnd = pScan->aAddrRange[ii*2+1];
          if( iStart>0 ){
            Tcl_ListObjAppendElement(0, pRange, Tcl_NewIntObj(iStart));
            Tcl_ListObjAppendElement(0, pRange, Tcl_NewIntObj(iEnd));
          }else if( iStart<0 ){
            Tcl_ListObjAppendElement(0, pCsr, Tcl_NewIntObj(iEnd));
          }
        }

        Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("debug_range", -1));
        Tcl_ListObjAppendElement(0, pRet, pRange);
        Tcl_ListObjAppendElement(0, pRet, Tcl_NewStringObj("debug_csr", -1));
        Tcl_ListObjAppendElement(0, pRet, pCsr);
      }

      Tcl_SetObjResult(interp, pRet);
    }else{
      Tcl_ResetResult(interp);
    }
  }
  return TCL_OK;
}

/*
** Usage:  sqlite3_stmt_scanstatus_reset  STMT
*/
static int SQLITE_TCLAPI test_stmt_scanstatus_reset(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;            /* First argument */
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT");
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  sqlite3_stmt_scanstatus_reset(pStmt);
  return TCL_OK;
}
#endif

#ifdef SQLITE_ENABLE_SQLLOG
/*
** Usage:  sqlite3_config_sqllog
**
** Zero the SQLITE_CONFIG_SQLLOG configuration
*/
static int SQLITE_TCLAPI test_config_sqllog(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  if( objc!=1 ){
    Tcl_WrongNumArgs(interp, 1, objv, "");
    return TCL_ERROR;
  }
  sqlite3_config(SQLITE_CONFIG_SQLLOG, 0, 0);
  return TCL_OK;
}
#endif

/*
** Usage:  sqlite3_config_sorterref
**
** Set the SQLITE_CONFIG_SORTERREF_SIZE configuration option
*/
static int SQLITE_TCLAPI test_config_sorterref(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int iVal;
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "NBYTE");
    return TCL_ERROR;
  }
  if( Tcl_GetIntFromObj(interp, objv[1], &iVal) ) return TCL_ERROR;
  sqlite3_config(SQLITE_CONFIG_SORTERREF_SIZE, iVal);
  return TCL_OK;
}

/*
** Usage: vfs_current_time_int64
**
** Return the value returned by the default VFS's xCurrentTimeInt64 method.
*/
static int SQLITE_TCLAPI vfsCurrentTimeInt64(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  i64 t;
  sqlite3_vfs *pVfs = sqlite3_vfs_find(0);
  if( objc!=1 ){
    Tcl_WrongNumArgs(interp, 1, objv, "");
    return TCL_ERROR;
  }
  pVfs->xCurrentTimeInt64(pVfs, &t);
  Tcl_SetObjResult(interp, Tcl_NewWideIntObj(t));
  return TCL_OK;
}

#ifndef SQLITE_OMIT_VIRTUALTABLE
/*
** Usage: create_null_module DB NAME
*/
static int SQLITE_TCLAPI test_create_null_module(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  char *zName;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB DBNAME");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zName = Tcl_GetString(objv[2]);

  sqlite3_create_module(db, zName, 0, 0);
  return TCL_OK;
}
#endif /* SQLITE_OMIT_VIRTUALTABLE */

#ifdef SQLITE_ENABLE_SNAPSHOT
/*
** Usage: sqlite3_snapshot_get DB DBNAME
*/
static int SQLITE_TCLAPI test_snapshot_get(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int rc;
  sqlite3 *db;
  char *zName;
  sqlite3_snapshot *pSnapshot = 0;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB DBNAME");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zName = Tcl_GetString(objv[2]);

  rc = sqlite3_snapshot_get(db, zName, &pSnapshot);
  if( rc!=SQLITE_OK ){
    Tcl_SetObjResult(interp, Tcl_NewStringObj(sqlite3ErrName(rc), -1));
    return TCL_ERROR;
  }else{
    char zBuf[100];
    if( sqlite3TestMakePointerStr(interp, zBuf, pSnapshot) ) return TCL_ERROR;
    Tcl_SetObjResult(interp, Tcl_NewStringObj(zBuf, -1));
  }
  return TCL_OK;
}
#endif /* SQLITE_ENABLE_SNAPSHOT */

#ifdef SQLITE_ENABLE_SNAPSHOT
/*
** Usage: sqlite3_snapshot_recover DB DBNAME
*/
static int SQLITE_TCLAPI test_snapshot_recover(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int rc;
  sqlite3 *db;
  char *zName;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB DBNAME");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zName = Tcl_GetString(objv[2]);

  rc = sqlite3_snapshot_recover(db, zName);
  if( rc!=SQLITE_OK ){
    Tcl_SetObjResult(interp, Tcl_NewStringObj(sqlite3ErrName(rc), -1));
    return TCL_ERROR;
  }else{
    Tcl_ResetResult(interp);
  }
  return TCL_OK;
}
#endif /* SQLITE_ENABLE_SNAPSHOT */

#ifdef SQLITE_ENABLE_SNAPSHOT
/*
** Usage: sqlite3_snapshot_open DB DBNAME SNAPSHOT
*/
static int SQLITE_TCLAPI test_snapshot_open(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int rc;
  sqlite3 *db;
  char *zName;
  sqlite3_snapshot *pSnapshot;

  if( objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB DBNAME SNAPSHOT");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zName = Tcl_GetString(objv[2]);
  pSnapshot = (sqlite3_snapshot*)sqlite3TestTextToPtr(Tcl_GetString(objv[3]));

  rc = sqlite3_snapshot_open(db, zName, pSnapshot);
  if( rc!=SQLITE_OK ){
    Tcl_SetObjResult(interp, Tcl_NewStringObj(sqlite3ErrName(rc), -1));
    return TCL_ERROR;
  }else{
    Tcl_ResetResult(interp);
  }
  return TCL_OK;
}
#endif /* SQLITE_ENABLE_SNAPSHOT */

#ifdef SQLITE_ENABLE_SNAPSHOT
/*
** Usage: sqlite3_snapshot_free SNAPSHOT
*/
static int SQLITE_TCLAPI test_snapshot_free(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_snapshot *pSnapshot;
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "SNAPSHOT");
    return TCL_ERROR;
  }
  pSnapshot = (sqlite3_snapshot*)sqlite3TestTextToPtr(Tcl_GetString(objv[1]));
  sqlite3_snapshot_free(pSnapshot);
  return TCL_OK;
}
#endif /* SQLITE_ENABLE_SNAPSHOT */

#ifdef SQLITE_ENABLE_SNAPSHOT
/*
** Usage: sqlite3_snapshot_cmp SNAPSHOT1 SNAPSHOT2
*/
static int SQLITE_TCLAPI test_snapshot_cmp(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int res;
  sqlite3_snapshot *p1;
  sqlite3_snapshot *p2;
  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "SNAPSHOT1 SNAPSHOT2");
    return TCL_ERROR;
  }
  p1 = (sqlite3_snapshot*)sqlite3TestTextToPtr(Tcl_GetString(objv[1]));
  p2 = (sqlite3_snapshot*)sqlite3TestTextToPtr(Tcl_GetString(objv[2]));
  res = sqlite3_snapshot_cmp(p1, p2);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(res));
  return TCL_OK;
}
#endif /* SQLITE_ENABLE_SNAPSHOT */

#ifdef SQLITE_ENABLE_SNAPSHOT
/*
** Usage: sqlite3_snapshot_get_blob DB DBNAME
*/
static int SQLITE_TCLAPI test_snapshot_get_blob(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int rc;
  sqlite3 *db;
  char *zName;
  sqlite3_snapshot *pSnapshot = 0;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB DBNAME");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zName = Tcl_GetString(objv[2]);

  rc = sqlite3_snapshot_get(db, zName, &pSnapshot);
  if( rc!=SQLITE_OK ){
    Tcl_SetObjResult(interp, Tcl_NewStringObj(sqlite3ErrName(rc), -1));
    return TCL_ERROR;
  }else{
    Tcl_SetObjResult(interp, 
        Tcl_NewByteArrayObj((unsigned char*)pSnapshot, sizeof(sqlite3_snapshot))
    );
    sqlite3_snapshot_free(pSnapshot);
  }
  return TCL_OK;
}
#endif /* SQLITE_ENABLE_SNAPSHOT */

#ifdef SQLITE_ENABLE_SNAPSHOT
  /*
  ** Usage: sqlite3_snapshot_open_blob DB DBNAME SNAPSHOT
*/
static int SQLITE_TCLAPI test_snapshot_open_blob(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int rc;
  sqlite3 *db;
  char *zName;
  unsigned char *pBlob;
  Tcl_Size nBlob;

  if( objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB DBNAME SNAPSHOT");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zName = Tcl_GetString(objv[2]);
  pBlob = Tcl_GetByteArrayFromObj(objv[3], &nBlob);
  if( nBlob!=sizeof(sqlite3_snapshot) ){
    Tcl_AppendResult(interp, "bad SNAPSHOT", 0);
    return TCL_ERROR;
  }
  rc = sqlite3_snapshot_open(db, zName, (sqlite3_snapshot*)pBlob);
  if( rc!=SQLITE_OK ){
    Tcl_SetObjResult(interp, Tcl_NewStringObj(sqlite3ErrName(rc), -1));
    return TCL_ERROR;
  }
  return TCL_OK;
}
#endif /* SQLITE_ENABLE_SNAPSHOT */

#ifdef SQLITE_ENABLE_SNAPSHOT
/*
** Usage: sqlite3_snapshot_cmp_blob SNAPSHOT1 SNAPSHOT2
*/
static int SQLITE_TCLAPI test_snapshot_cmp_blob(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int res;
  unsigned char *p1;
  unsigned char *p2;
  Tcl_Size n1;
  Tcl_Size n2;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "SNAPSHOT1 SNAPSHOT2");
    return TCL_ERROR;
  }

  p1 = Tcl_GetByteArrayFromObj(objv[1], &n1);
  p2 = Tcl_GetByteArrayFromObj(objv[2], &n2);

  if( n1!=sizeof(sqlite3_snapshot) || n1!=n2 ){
    Tcl_AppendResult(interp, "bad SNAPSHOT", 0);
    return TCL_ERROR;
  }

  res = sqlite3_snapshot_cmp((sqlite3_snapshot*)p1, (sqlite3_snapshot*)p2);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(res));
  return TCL_OK;
}
#endif /* SQLITE_ENABLE_SNAPSHOT */

/*
** Usage: sqlite3_delete_database FILENAME
*/
int sqlite3_delete_database(const char*);   /* in test_delete.c */
static int SQLITE_TCLAPI test_delete_database(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int rc;
  const char *zFile;
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "FILE");
    return TCL_ERROR;
  }
  zFile = (const char*)Tcl_GetString(objv[1]);
  rc = sqlite3_delete_database(zFile);

  Tcl_SetObjResult(interp, Tcl_NewStringObj(sqlite3ErrName(rc), -1));
  return TCL_OK;
}

/*
** Usage: atomic_batch_write PATH
*/
static int SQLITE_TCLAPI test_atomic_batch_write(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  char *zFile = 0;                /* Path to file to test */
  sqlite3 *db = 0;                /* Database handle */
  sqlite3_file *pFd = 0;          /* SQLite fd open on zFile */
  int bRes = 0;                   /* Integer result of this command */
  int dc = 0;                     /* Device-characteristics mask */
  int rc;                         /* sqlite3_open() return code */

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "PATH");
    return TCL_ERROR;
  }
  zFile = Tcl_GetString(objv[1]);

  rc = sqlite3_open(zFile, &db);
  if( rc!=SQLITE_OK ){
    Tcl_AppendResult(interp, sqlite3_errmsg(db), 0);
    sqlite3_close(db);
    return TCL_ERROR;
  }

  rc = sqlite3_file_control(db, "main", SQLITE_FCNTL_FILE_POINTER, (void*)&pFd);
  dc = pFd->pMethods->xDeviceCharacteristics(pFd);
  if( dc & SQLITE_IOCAP_BATCH_ATOMIC ){
    bRes = 1;
  }

  Tcl_SetObjResult(interp, Tcl_NewIntObj(bRes));
  sqlite3_close(db);
  return TCL_OK;
}

/*
** Usage:  sqlite3_next_stmt  DB  STMT
**
** Return the next statement in sequence after STMT.
*/
static int SQLITE_TCLAPI test_next_stmt(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  sqlite3 *db = 0;
  char zBuf[50];

  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB STMT", 0);
    return TCL_ERROR;
  }

  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  if( getStmtPointer(interp, Tcl_GetString(objv[2]), &pStmt) ) return TCL_ERROR;
  pStmt = sqlite3_next_stmt(db, pStmt);
  if( pStmt ){
    if( sqlite3TestMakePointerStr(interp, zBuf, pStmt) ) return TCL_ERROR;
    Tcl_AppendResult(interp, zBuf, 0);
  }
  return TCL_OK;
}

/*
** Usage:  sqlite3_stmt_readonly  STMT
**
** Return true if STMT is a NULL pointer or a pointer to a statement
** that is guaranteed to leave the database unmodified.
*/
static int SQLITE_TCLAPI test_stmt_readonly(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int rc;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  rc = sqlite3_stmt_readonly(pStmt);
  Tcl_SetObjResult(interp, Tcl_NewBooleanObj(rc));
  return TCL_OK;
}

/*
** Usage:  sqlite3_stmt_isexplain  STMT
**
** Return 1, 2, or 0 respectively if STMT is an EXPLAIN statement, an
** EXPLAIN QUERY PLAN statement or an ordinary statement or NULL pointer.
*/
static int SQLITE_TCLAPI test_stmt_isexplain(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int rc;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  rc = sqlite3_stmt_isexplain(pStmt);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(rc));
  return TCL_OK;
}

/*
** Usage:  sqlite3_stmt_explain  STMT  INT
**
** Set the explain to normal (0), EXPLAIN (1) or EXPLAIN QUERY PLAN (2).
*/
static int SQLITE_TCLAPI test_stmt_explain(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int eMode = 0;
  int rc;

  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT INT", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &eMode) ) return TCL_ERROR;
  rc = sqlite3_stmt_explain(pStmt, eMode);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(rc));
  return TCL_OK;
}

/*
** Usage:  sqlite3_stmt_busy  STMT
**
** Return true if STMT is a non-NULL pointer to a statement
** that has been stepped but not to completion.
*/
static int SQLITE_TCLAPI test_stmt_busy(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int rc;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  rc = sqlite3_stmt_busy(pStmt);
  Tcl_SetObjResult(interp, Tcl_NewBooleanObj(rc));
  return TCL_OK;
}

/*
** Usage:  uses_stmt_journal  STMT
**
** Return true if STMT uses a statement journal.
*/
static int SQLITE_TCLAPI uses_stmt_journal(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  sqlite3_stmt_readonly(pStmt);
  Tcl_SetObjResult(interp, Tcl_NewBooleanObj(((Vdbe *)pStmt)->usesStmtJournal));
  return TCL_OK;
}


/*
** Usage:  sqlite3_reset  STMT 
**
** Reset a statement handle.
*/
static int SQLITE_TCLAPI test_reset(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int rc;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " <STMT>", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;

  rc = sqlite3_reset(pStmt);
  if( pStmt && sqlite3TestErrCode(interp, StmtToDb(pStmt), rc) ){
    return TCL_ERROR;
  }
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), TCL_STATIC);
/*
  if( rc ){
    return TCL_ERROR;
  }
*/
  return TCL_OK;
}

/*
** Usage:  sqlite3_expired STMT 
**
** Return TRUE if a recompilation of the statement is recommended.
*/
static int SQLITE_TCLAPI test_expired(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifndef SQLITE_OMIT_DEPRECATED
  sqlite3_stmt *pStmt;
  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " <STMT>", 0);
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  Tcl_SetObjResult(interp, Tcl_NewBooleanObj(sqlite3_expired(pStmt)));
#endif
  return TCL_OK;
}

/*
** Usage:  sqlite3_transfer_bindings FROMSTMT TOSTMT
**
** Transfer all bindings from FROMSTMT over to TOSTMT
*/
static int SQLITE_TCLAPI test_transfer_bind(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifndef SQLITE_OMIT_DEPRECATED
  sqlite3_stmt *pStmt1, *pStmt2;
  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " FROM-STMT TO-STMT", 0);
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt1)) return TCL_ERROR;
  if( getStmtPointer(interp, Tcl_GetString(objv[2]), &pStmt2)) return TCL_ERROR;
  Tcl_SetObjResult(interp, 
     Tcl_NewIntObj(sqlite3_transfer_bindings(pStmt1,pStmt2)));
#endif
  return TCL_OK;
}

/*
** Usage:  sqlite3_changes DB
**
** Return the number of changes made to the database by the last SQL
** execution.
*/
static int SQLITE_TCLAPI test_changes(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
       Tcl_GetString(objv[0]), " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  Tcl_SetObjResult(interp, Tcl_NewIntObj(sqlite3_changes(db)));
  return TCL_OK;
}

/*
** This is the "static_bind_value" that variables are bound to when
** the FLAG option of sqlite3_bind is "static"
*/
static char *sqlite_static_bind_value = 0;
static int sqlite_static_bind_nbyte = 0;

/*
** Usage:  sqlite3_bind  VM  IDX  VALUE  FLAGS
**
** Sets the value of the IDX-th occurrence of "?" in the original SQL
** string.  VALUE is the new value.  If FLAGS=="null" then VALUE is
** ignored and the value is set to NULL.  If FLAGS=="static" then
** the value is set to the value of a static variable named
** "sqlite_static_bind_value".  If FLAGS=="normal" then a copy
** of the VALUE is made.  If FLAGS=="blob10" then a VALUE is ignored
** an a 10-byte blob "abc\000xyz\000pq" is inserted.
*/
static int SQLITE_TCLAPI test_bind(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  sqlite3_stmt *pStmt;
  int rc;
  int idx;
  if( argc!=5 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
       " VM IDX VALUE (null|static|normal)\"", 0);
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, argv[1], &pStmt) ) return TCL_ERROR;
  if( Tcl_GetInt(interp, argv[2], &idx) ) return TCL_ERROR;
  if( strcmp(argv[4],"null")==0 ){
    rc = sqlite3_bind_null(pStmt, idx);
  }else if( strcmp(argv[4],"static")==0 ){
    rc = sqlite3_bind_text(pStmt, idx, sqlite_static_bind_value, -1, 0);
  }else if( strcmp(argv[4],"static-nbytes")==0 ){
    rc = sqlite3_bind_text(pStmt, idx, sqlite_static_bind_value,
                                       sqlite_static_bind_nbyte, 0);
  }else if( strcmp(argv[4],"normal")==0 ){
    rc = sqlite3_bind_text(pStmt, idx, argv[3], -1, SQLITE_TRANSIENT);
  }else if( strcmp(argv[4],"blob10")==0 ){
    rc = sqlite3_bind_text(pStmt, idx, "abc\000xyz\000pq", 10, SQLITE_STATIC);
  }else{
    Tcl_AppendResult(interp, "4th argument should be "
        "\"null\" or \"static\" or \"normal\"", 0);
    return TCL_ERROR;
  }
  if( sqlite3TestErrCode(interp, StmtToDb(pStmt), rc) ) return TCL_ERROR;
  if( rc ){
    char zBuf[50];
    sqlite3_snprintf(sizeof(zBuf), zBuf, "(%d) ", rc);
    Tcl_AppendResult(interp, zBuf, sqlite3ErrStr(rc), 0);
    return TCL_ERROR;
  }
  return TCL_OK;
}

#ifndef SQLITE_OMIT_UTF16
/*
** Usage: add_test_collate <db ptr> <utf8> <utf16le> <utf16be>
**
** This function is used to test that SQLite selects the correct collation
** sequence callback when multiple versions (for different text encodings)
** are available.
**
** Calling this routine registers the collation sequence "test_collate"
** with database handle <db>. The second argument must be a list of three
** boolean values. If the first is true, then a version of test_collate is
** registered for UTF-8, if the second is true, a version is registered for
** UTF-16le, if the third is true, a UTF-16be version is available.
** Previous versions of test_collate are deleted.
**
** The collation sequence test_collate is implemented by calling the
** following TCL script:
**
**   "test_collate <enc> <lhs> <rhs>"
**
** The <lhs> and <rhs> are the two values being compared, encoded in UTF-8.
** The <enc> parameter is the encoding of the collation function that
** SQLite selected to call. The TCL test script implements the
** "test_collate" proc.
**
** Note that this will only work with one interpreter at a time, as the
** interp pointer to use when evaluating the TCL script is stored in
** pTestCollateInterp.
*/
static Tcl_Interp* pTestCollateInterp;
static int test_collate_func(
  void *pCtx, 
  int nA, const void *zA,
  int nB, const void *zB
){
  Tcl_Interp *i = pTestCollateInterp;
  int encin = SQLITE_PTR_TO_INT(pCtx);
  int res;
  int n;

  sqlite3_value *pVal;
  Tcl_Obj *pX;

  pX = Tcl_NewStringObj("test_collate", -1);
  Tcl_IncrRefCount(pX);

  switch( encin ){
    case SQLITE_UTF8:
      Tcl_ListObjAppendElement(i,pX,Tcl_NewStringObj("UTF-8",-1));
      break;
    case SQLITE_UTF16LE:
      Tcl_ListObjAppendElement(i,pX,Tcl_NewStringObj("UTF-16LE",-1));
      break;
    case SQLITE_UTF16BE:
      Tcl_ListObjAppendElement(i,pX,Tcl_NewStringObj("UTF-16BE",-1));
      break;
    default:
      assert(0);
  }

  sqlite3BeginBenignMalloc();
  pVal = sqlite3ValueNew(0);
  if( pVal ){
    sqlite3ValueSetStr(pVal, nA, zA, encin, SQLITE_STATIC);
    n = sqlite3_value_bytes(pVal);
    Tcl_ListObjAppendElement(i,pX,
        Tcl_NewStringObj((char*)sqlite3_value_text(pVal),n));
    sqlite3ValueSetStr(pVal, nB, zB, encin, SQLITE_STATIC);
    n = sqlite3_value_bytes(pVal);
    Tcl_ListObjAppendElement(i,pX,
        Tcl_NewStringObj((char*)sqlite3_value_text(pVal),n));
    sqlite3ValueFree(pVal);
  }
  sqlite3EndBenignMalloc();

  Tcl_EvalObjEx(i, pX, 0);
  Tcl_DecrRefCount(pX);
  Tcl_GetIntFromObj(i, Tcl_GetObjResult(i), &res);
  return res;
}
static int SQLITE_TCLAPI test_collate(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  int val;
  sqlite3_value *pVal;
  int rc;

  if( objc!=5 ) goto bad_args;
  pTestCollateInterp = interp;
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;

  if( TCL_OK!=Tcl_GetBooleanFromObj(interp, objv[2], &val) ) return TCL_ERROR;
  rc = sqlite3_create_collation(db, "test_collate", SQLITE_UTF8, 
          (void *)SQLITE_UTF8, val?test_collate_func:0);
  if( rc==SQLITE_OK ){
    const void *zUtf16;
    if( TCL_OK!=Tcl_GetBooleanFromObj(interp, objv[3], &val) ) return TCL_ERROR;
    rc = sqlite3_create_collation(db, "test_collate", SQLITE_UTF16LE, 
            (void *)SQLITE_UTF16LE, val?test_collate_func:0);
    if( TCL_OK!=Tcl_GetBooleanFromObj(interp, objv[4], &val) ) return TCL_ERROR;

#if 0
    if( sqlite3_iMallocFail>0 ){
      sqlite3_iMallocFail++;
    }
#endif
    sqlite3_mutex_enter(db->mutex);
    pVal = sqlite3ValueNew(db);
    sqlite3ValueSetStr(pVal, -1, "test_collate", SQLITE_UTF8, SQLITE_STATIC);
    zUtf16 = sqlite3ValueText(pVal, SQLITE_UTF16NATIVE);
    if( db->mallocFailed ){
      rc = SQLITE_NOMEM;
    }else{
      rc = sqlite3_create_collation16(db, zUtf16, SQLITE_UTF16BE, 
          (void *)SQLITE_UTF16BE, val?test_collate_func:0);
    }
    sqlite3ValueFree(pVal);
    sqlite3_mutex_leave(db->mutex);
  }
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  
  if( rc!=SQLITE_OK ){
    Tcl_AppendResult(interp, sqlite3ErrName(rc), 0);
    return TCL_ERROR;
  }
  return TCL_OK;

bad_args:
  Tcl_AppendResult(interp, "wrong # args: should be \"",
      Tcl_GetStringFromObj(objv[0], 0), " <DB> <utf8> <utf16le> <utf16be>", 0);
  return TCL_ERROR;
}

/*
** Usage: add_test_utf16bin_collate <db ptr>
**
** Add a utf-16 collation sequence named "utf16bin" to the database
** handle. This collation sequence compares arguments in the same way as the
** built-in collation "binary".
*/
static int test_utf16bin_collate_func(
  void *pCtx, 
  int nA, const void *zA,
  int nB, const void *zB
){
  int nCmp = (nA>nB ? nB : nA);
  int res = memcmp(zA, zB, nCmp);
  if( res==0 ) res = nA - nB;
  return res;
}
static int SQLITE_TCLAPI test_utf16bin_collate(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  int rc;

  if( objc!=2 ) goto bad_args;
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;

  rc = sqlite3_create_collation(db, "utf16bin", SQLITE_UTF16, 0, 
      test_utf16bin_collate_func
  );
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  return TCL_OK;

bad_args:
  Tcl_WrongNumArgs(interp, 1, objv, "DB");
  return TCL_ERROR;
}

/*
** When the collation needed callback is invoked, record the name of 
** the requested collating function here.  The recorded name is linked
** to a TCL variable and used to make sure that the requested collation
** name is correct.
*/
static char zNeededCollation[200];
static char *pzNeededCollation = zNeededCollation;


/*
** Called when a collating sequence is needed.  Registered using
** sqlite3_collation_needed16().
*/
static void test_collate_needed_cb(
  void *pCtx, 
  sqlite3 *db,
  int eTextRep,
  const void *pName
){
  int enc = ENC(db);
  int i;
  char *z;
  for(z = (char*)pName, i=0; *z || z[1]; z++){
    if( *z ) zNeededCollation[i++] = *z;
  }
  zNeededCollation[i] = 0;
  sqlite3_create_collation(
      db, "test_collate", ENC(db), SQLITE_INT_TO_PTR(enc), test_collate_func);
}

/*
** Usage: add_test_collate_needed DB
*/
static int SQLITE_TCLAPI test_collate_needed(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  int rc;

  if( objc!=2 ) goto bad_args;
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  rc = sqlite3_collation_needed16(db, 0, test_collate_needed_cb);
  zNeededCollation[0] = 0;
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  return TCL_OK;

bad_args:
  Tcl_WrongNumArgs(interp, 1, objv, "DB");
  return TCL_ERROR;
}

/*
** tclcmd:   add_alignment_test_collations  DB
**
** Add two new collating sequences to the database DB
**
**     utf16_aligned
**     utf16_unaligned
**
** Both collating sequences use the same sort order as BINARY.
** The only difference is that the utf16_aligned collating
** sequence is declared with the SQLITE_UTF16_ALIGNED flag.
** Both collating functions increment the unaligned utf16 counter
** whenever they see a string that begins on an odd byte boundary.
*/
static int unaligned_string_counter = 0;
static int alignmentCollFunc(
  void *NotUsed,
  int nKey1, const void *pKey1,
  int nKey2, const void *pKey2
){
  int rc, n;
  n = nKey1<nKey2 ? nKey1 : nKey2;
  if( nKey1>0 && 1==(1&(SQLITE_PTR_TO_INT(pKey1))) ) unaligned_string_counter++;
  if( nKey2>0 && 1==(1&(SQLITE_PTR_TO_INT(pKey2))) ) unaligned_string_counter++;
  rc = memcmp(pKey1, pKey2, n);
  if( rc==0 ){
    rc = nKey1 - nKey2;
  }
  return rc;
}
static int SQLITE_TCLAPI add_alignment_test_collations(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  if( objc>=2 ){
    if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
    sqlite3_create_collation(db, "utf16_unaligned", SQLITE_UTF16, 
        0, alignmentCollFunc);
    sqlite3_create_collation(db, "utf16_aligned", SQLITE_UTF16_ALIGNED, 
        0, alignmentCollFunc);
  }
  return SQLITE_OK;
}
#endif /* !defined(SQLITE_OMIT_UTF16) */

/*
** Usage: add_test_function <db ptr> <utf8> <utf16le> <utf16be>
**
** This function is used to test that SQLite selects the correct user
** function callback when multiple versions (for different text encodings)
** are available.
**
** Calling this routine registers up to three versions of the user function
** "test_function" with database handle <db>.  If the second argument is
** true, then a version of test_function is registered for UTF-8, if the
** third is true, a version is registered for UTF-16le, if the fourth is
** true, a UTF-16be version is available.  Previous versions of
** test_function are deleted.
**
** The user function is implemented by calling the following TCL script:
**
**   "test_function <enc> <arg>"
**
** Where <enc> is one of UTF-8, UTF-16LE or UTF16BE, and <arg> is the
** single argument passed to the SQL function. The value returned by
** the TCL script is used as the return value of the SQL function. It
** is passed to SQLite using UTF-16BE for a UTF-8 test_function(), UTF-8
** for a UTF-16LE test_function(), and UTF-16LE for an implementation that
** prefers UTF-16BE.
*/
#ifndef SQLITE_OMIT_UTF16
static void test_function_utf8(
  sqlite3_context *pCtx, 
  int nArg,
  sqlite3_value **argv
){
  Tcl_Interp *interp;
  Tcl_Obj *pX;
  sqlite3_value *pVal;
  interp = (Tcl_Interp *)sqlite3_user_data(pCtx);
  pX = Tcl_NewStringObj("test_function", -1);
  Tcl_IncrRefCount(pX);
  Tcl_ListObjAppendElement(interp, pX, Tcl_NewStringObj("UTF-8", -1));
  Tcl_ListObjAppendElement(interp, pX, 
      Tcl_NewStringObj((char*)sqlite3_value_text(argv[0]), -1));
  Tcl_EvalObjEx(interp, pX, 0);
  Tcl_DecrRefCount(pX);
  sqlite3_result_text(pCtx, Tcl_GetStringResult(interp), -1, SQLITE_TRANSIENT);
  pVal = sqlite3ValueNew(0);
  sqlite3ValueSetStr(pVal, -1, Tcl_GetStringResult(interp), 
      SQLITE_UTF8, SQLITE_STATIC);
  sqlite3_result_text16be(pCtx, sqlite3_value_text16be(pVal),
      -1, SQLITE_TRANSIENT);
  sqlite3ValueFree(pVal);
}
static void test_function_utf16le(
  sqlite3_context *pCtx, 
  int nArg,
  sqlite3_value **argv
){
  Tcl_Interp *interp;
  Tcl_Obj *pX;
  sqlite3_value *pVal;
  interp = (Tcl_Interp *)sqlite3_user_data(pCtx);
  pX = Tcl_NewStringObj("test_function", -1);
  Tcl_IncrRefCount(pX);
  Tcl_ListObjAppendElement(interp, pX, Tcl_NewStringObj("UTF-16LE", -1));
  Tcl_ListObjAppendElement(interp, pX, 
      Tcl_NewStringObj((char*)sqlite3_value_text(argv[0]), -1));
  Tcl_EvalObjEx(interp, pX, 0);
  Tcl_DecrRefCount(pX);
  pVal = sqlite3ValueNew(0);
  sqlite3ValueSetStr(pVal, -1, Tcl_GetStringResult(interp), 
      SQLITE_UTF8, SQLITE_STATIC);
  sqlite3_result_text(pCtx,(char*)sqlite3_value_text(pVal),-1,SQLITE_TRANSIENT);
  sqlite3ValueFree(pVal);
}
static void test_function_utf16be(
  sqlite3_context *pCtx, 
  int nArg,
  sqlite3_value **argv
){
  Tcl_Interp *interp;
  Tcl_Obj *pX;
  sqlite3_value *pVal;
  interp = (Tcl_Interp *)sqlite3_user_data(pCtx);
  pX = Tcl_NewStringObj("test_function", -1);
  Tcl_IncrRefCount(pX);
  Tcl_ListObjAppendElement(interp, pX, Tcl_NewStringObj("UTF-16BE", -1));
  Tcl_ListObjAppendElement(interp, pX, 
      Tcl_NewStringObj((char*)sqlite3_value_text(argv[0]), -1));
  Tcl_EvalObjEx(interp, pX, 0);
  Tcl_DecrRefCount(pX);
  pVal = sqlite3ValueNew(0);
  sqlite3ValueSetStr(pVal, -1, Tcl_GetStringResult(interp), 
      SQLITE_UTF8, SQLITE_STATIC);
  sqlite3_result_text16(pCtx, sqlite3_value_text16le(pVal),
      -1, SQLITE_TRANSIENT);
  sqlite3_result_text16be(pCtx, sqlite3_value_text16le(pVal),
      -1, SQLITE_TRANSIENT);
  sqlite3_result_text16le(pCtx, sqlite3_value_text16le(pVal),
      -1, SQLITE_TRANSIENT);
  sqlite3ValueFree(pVal);
}
#endif /* SQLITE_OMIT_UTF16 */
static int SQLITE_TCLAPI test_function(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifndef SQLITE_OMIT_UTF16
  sqlite3 *db;
  int val;

  if( objc!=5 ) goto bad_args;
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;

  if( TCL_OK!=Tcl_GetBooleanFromObj(interp, objv[2], &val) ) return TCL_ERROR;
  if( val ){
    sqlite3_create_function(db, "test_function", 1, SQLITE_UTF8, 
        interp, test_function_utf8, 0, 0);
  }
  if( TCL_OK!=Tcl_GetBooleanFromObj(interp, objv[3], &val) ) return TCL_ERROR;
  if( val ){
    sqlite3_create_function(db, "test_function", 1, SQLITE_UTF16LE, 
        interp, test_function_utf16le, 0, 0);
  }
  if( TCL_OK!=Tcl_GetBooleanFromObj(interp, objv[4], &val) ) return TCL_ERROR;
  if( val ){
    sqlite3_create_function(db, "test_function", 1, SQLITE_UTF16BE, 
        interp, test_function_utf16be, 0, 0);
  }

  return TCL_OK;
bad_args:
  Tcl_AppendResult(interp, "wrong # args: should be \"",
      Tcl_GetStringFromObj(objv[0], 0), " <DB> <utf8> <utf16le> <utf16be>", 0);
#endif /* SQLITE_OMIT_UTF16 */
  return TCL_ERROR;
}

/*
** Usage:         sqlite3_test_errstr <err code>
**
** Test that the English language string equivalents for sqlite error codes
** are sane. The parameter is an integer representing an sqlite error code.
** The result is a list of two elements, the string representation of the
** error code and the English language explanation.
*/
static int SQLITE_TCLAPI test_errstr(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  char *zCode;
  int i;
  if( objc!=1 ){
    Tcl_WrongNumArgs(interp, 1, objv, "<error code>");
  }

  zCode = Tcl_GetString(objv[1]);
  for(i=0; i<200; i++){
    if( 0==strcmp(t1ErrorName(i), zCode) ) break;
  }
  Tcl_SetResult(interp, (char *)sqlite3ErrStr(i), 0);
  return TCL_OK;
}

/*
** Usage:    breakpoint
**
** This routine exists for one purpose - to provide a place to put a
** breakpoint with GDB that can be triggered using TCL code.  The use
** for this is when a particular test fails on (say) the 1485th iteration.
** In the TCL test script, we can add code like this:
**
**     if {$i==1485} breakpoint
**
** Then run testfixture in the debugger and wait for the breakpoint to
** fire.  Then additional breakpoints can be set to trace down the bug.
*/
static int SQLITE_TCLAPI test_breakpoint(
  void *NotUsed,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int argc,              /* Number of arguments */
  char **argv            /* Text of each argument */
){
  return TCL_OK;         /* Do nothing */
}

/*
** Usage:   sqlite3_bind_zeroblob  STMT IDX N
**
** Test the sqlite3_bind_zeroblob interface.  STMT is a prepared statement.
** IDX is the index of a wildcard in the prepared statement.  This command
** binds a N-byte zero-filled BLOB to the wildcard.
*/
static int SQLITE_TCLAPI test_bind_zeroblob(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int idx;
  int n;
  int rc;

  if( objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT IDX N");
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &idx) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[3], &n) ) return TCL_ERROR;

  rc = sqlite3_bind_zeroblob(pStmt, idx, n);
  if( sqlite3TestErrCode(interp, StmtToDb(pStmt), rc) ) return TCL_ERROR;
  if( rc!=SQLITE_OK ){
    return TCL_ERROR;
  }

  return TCL_OK;
}

/*
** Usage:   sqlite3_bind_zeroblob64  STMT IDX N
**
** Test the sqlite3_bind_zeroblob64 interface.  STMT is a prepared statement.
** IDX is the index of a wildcard in the prepared statement.  This command
** binds a N-byte zero-filled BLOB to the wildcard.
*/
static int SQLITE_TCLAPI test_bind_zeroblob64(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int idx;
  Tcl_WideInt n;
  int rc;

  if( objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT IDX N");
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &idx) ) return TCL_ERROR;
  if( Tcl_GetWideIntFromObj(interp, objv[3], &n) ) return TCL_ERROR;

  rc = sqlite3_bind_zeroblob64(pStmt, idx, n);
  if( sqlite3TestErrCode(interp, StmtToDb(pStmt), rc) ) return TCL_ERROR;
  if( rc!=SQLITE_OK ){
    Tcl_AppendResult(interp, sqlite3ErrName(rc), 0);
    return TCL_ERROR;
  }

  return TCL_OK;
}

/*
** Usage:   sqlite3_bind_int  STMT N VALUE
**
** Test the sqlite3_bind_int interface.  STMT is a prepared statement.
** N is the index of a wildcard in the prepared statement.  This command
** binds a 32-bit integer VALUE to that wildcard.
*/
static int SQLITE_TCLAPI test_bind_int(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int idx;
  int value;
  int rc;

  if( objc!=4 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT N VALUE", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &idx) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[3], &value) ) return TCL_ERROR;

  rc = sqlite3_bind_int(pStmt, idx, value);
  if( sqlite3TestErrCode(interp, StmtToDb(pStmt), rc) ) return TCL_ERROR;
  if( rc!=SQLITE_OK ){
    return TCL_ERROR;
  }

  return TCL_OK;
}


/*
** Usage:   intarray_addr  INT  ...
**
** Return the address of a C-language array of 32-bit integers.
**
** Space to hold the array is obtained from malloc().  Call this procedure once
** with no arguments in order to release memory.  Each call to this procedure
** overwrites the previous array.
*/
static int SQLITE_TCLAPI test_intarray_addr(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int i;
  static int *p = 0;

  sqlite3_free(p);
  p = 0;
  if( objc>1 ){
    p = sqlite3_malloc( sizeof(p[0])*(objc-1) );
    if( p==0 ) return TCL_ERROR;
    for(i=0; i<objc-1; i++){
      if( Tcl_GetIntFromObj(interp, objv[1+i], &p[i]) ){
        sqlite3_free(p);
        p = 0;
        return TCL_ERROR;
      }
    }
  }  
  Tcl_SetObjResult(interp, Tcl_NewWideIntObj((uptr)p));
  return TCL_OK;
}
/*
** Usage:   intarray_addr  INT  ...
**
** Return the address of a C-language array of 32-bit integers.
**
** Space to hold the array is obtained from malloc().  Call this procedure once
** with no arguments in order to release memory.  Each call to this procedure
** overwrites the previous array.
*/
static int SQLITE_TCLAPI test_int64array_addr(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int i;
  static sqlite3_int64 *p = 0;

  sqlite3_free(p);
  p = 0;
  if( objc>1 ){
    p = sqlite3_malloc( sizeof(p[0])*(objc-1) );
    if( p==0 ) return TCL_ERROR;
    for(i=0; i<objc-1; i++){
      Tcl_WideInt v;
      if( Tcl_GetWideIntFromObj(interp, objv[1+i], &v) ){
        sqlite3_free(p);
        p = 0;
        return TCL_ERROR;
      }
      p[i] = v;
    }
  }  
  Tcl_SetObjResult(interp, Tcl_NewWideIntObj((uptr)p));
  return TCL_OK;
}
/*
** Usage:   doublearray_addr  INT  ...
**
** Return the address of a C-language array of doubles.
**
** Space to hold the array is obtained from malloc().  Call this procedure once
** with no arguments in order to release memory.  Each call to this procedure
** overwrites the previous array.
*/
static int SQLITE_TCLAPI test_doublearray_addr(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int i;
  static double *p = 0;

  sqlite3_free(p);
  p = 0;
  if( objc>1 ){
    p = sqlite3_malloc( sizeof(p[0])*(objc-1) );
    if( p==0 ) return TCL_ERROR;
    for(i=0; i<objc-1; i++){
      if( Tcl_GetDoubleFromObj(interp, objv[1+i], &p[i]) ){
        sqlite3_free(p);
        p = 0;
        return TCL_ERROR;
      }
    }
  }  
  Tcl_SetObjResult(interp, Tcl_NewWideIntObj((uptr)p));
  return TCL_OK;
}
/*
** Usage:   textarray_addr  TEXT ...
**
** Return the address of a C-language array of strings.
**
** Space to hold the array is obtained from malloc().  Call this procedure once
** with no arguments in order to release memory.  Each call to this procedure
** overwrites the previous array.
*/
static int SQLITE_TCLAPI test_textarray_addr(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int i;
  static int n = 0;
  static char **p = 0;

  for(i=0; i<n; i++) sqlite3_free(p[i]);
  sqlite3_free(p);
  p = 0;
  if( objc>1 ){
    p = sqlite3_malloc( sizeof(p[0])*(objc-1) );
    if( p==0 ) return TCL_ERROR;
    for(i=0; i<objc-1; i++){
      p[i] = sqlite3_mprintf("%s", Tcl_GetString(objv[1+i]));
    }
  }
  n = objc-1;
  Tcl_SetObjResult(interp, Tcl_NewWideIntObj((uptr)p));
  return TCL_OK;
}


/*
** Usage:   sqlite3_bind_int64  STMT N VALUE
**
** Test the sqlite3_bind_int64 interface.  STMT is a prepared statement.
** N is the index of a wildcard in the prepared statement.  This command
** binds a 64-bit integer VALUE to that wildcard.
*/
static int SQLITE_TCLAPI test_bind_int64(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int idx;
  Tcl_WideInt value;
  int rc;

  if( objc!=4 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT N VALUE", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &idx) ) return TCL_ERROR;
  if( Tcl_GetWideIntFromObj(interp, objv[3], &value) ) return TCL_ERROR;

  rc = sqlite3_bind_int64(pStmt, idx, value);
  if( sqlite3TestErrCode(interp, StmtToDb(pStmt), rc) ) return TCL_ERROR;
  if( rc!=SQLITE_OK ){
    return TCL_ERROR;
  }

  return TCL_OK;
}


/*
** Usage:   sqlite3_bind_double  STMT N VALUE
**
** Test the sqlite3_bind_double interface.  STMT is a prepared statement.
** N is the index of a wildcard in the prepared statement.  This command
** binds a 64-bit integer VALUE to that wildcard.
*/
static int SQLITE_TCLAPI test_bind_double(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int idx;
  double value = 0;
  int rc;
  const char *zVal;
  int i;
  static const struct {
    const char *zName;     /* Name of the special floating point value */
    unsigned int iUpper;   /* Upper 32 bits */
    unsigned int iLower;   /* Lower 32 bits */
  } aSpecialFp[] = {
    {  "NaN",      0x7fffffff, 0xffffffff },
    {  "SNaN",     0x7ff7ffff, 0xffffffff },
    {  "-NaN",     0xffffffff, 0xffffffff },
    {  "-SNaN",    0xfff7ffff, 0xffffffff },
    {  "+Inf",     0x7ff00000, 0x00000000 },
    {  "-Inf",     0xfff00000, 0x00000000 },
    {  "Epsilon",  0x00000000, 0x00000001 },
    {  "-Epsilon", 0x80000000, 0x00000001 },
    {  "NaN0",     0x7ff80000, 0x00000000 },
    {  "-NaN0",    0xfff80000, 0x00000000 },
  };

  if( objc!=4 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT N VALUE", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &idx) ) return TCL_ERROR;

  /* Intercept the string "NaN" and generate a NaN value for it.
  ** All other strings are passed through to Tcl_GetDoubleFromObj().
  ** Tcl_GetDoubleFromObj() should understand "NaN" but some versions
  ** contain a bug.
  */
  zVal = Tcl_GetString(objv[3]);
  for(i=0; i<sizeof(aSpecialFp)/sizeof(aSpecialFp[0]); i++){
    if( strcmp(aSpecialFp[i].zName, zVal)==0 ){
      sqlite3_uint64 x;
      x = aSpecialFp[i].iUpper;
      x <<= 32;
      x |= aSpecialFp[i].iLower;
      assert( sizeof(value)==8 );
      assert( sizeof(x)==8 );
      memcpy(&value, &x, 8);
      break;
    }
  }
  if( i>=sizeof(aSpecialFp)/sizeof(aSpecialFp[0]) &&
         Tcl_GetDoubleFromObj(interp, objv[3], &value) ){
    return TCL_ERROR;
  }
  rc = sqlite3_bind_double(pStmt, idx, value);
  if( sqlite3TestErrCode(interp, StmtToDb(pStmt), rc) ) return TCL_ERROR;
  if( rc!=SQLITE_OK ){
    return TCL_ERROR;
  }

  return TCL_OK;
}

/*
** Usage:   sqlite3_bind_null  STMT N
**
** Test the sqlite3_bind_null interface.  STMT is a prepared statement.
** N is the index of a wildcard in the prepared statement.  This command
** binds a NULL to the wildcard.
*/
static int SQLITE_TCLAPI test_bind_null(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int idx;
  int rc;

  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT N", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &idx) ) return TCL_ERROR;

  rc = sqlite3_bind_null(pStmt, idx);
  if( sqlite3TestErrCode(interp, StmtToDb(pStmt), rc) ) return TCL_ERROR;
  if( rc!=SQLITE_OK ){
    return TCL_ERROR;
  }

  return TCL_OK;
}

/*
** Usage:   sqlite3_bind_text  STMT N STRING BYTES
**
** Test the sqlite3_bind_text interface.  STMT is a prepared statement.
** N is the index of a wildcard in the prepared statement.  This command
** binds a UTF-8 string STRING to the wildcard.  The string is BYTES bytes
** long.
*/
static int SQLITE_TCLAPI test_bind_text(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int idx;
  Tcl_Size trueLength = 0;
  int bytes;
  char *value;
  int rc;
  char *toFree = 0;

  if( objc!=5 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT N VALUE BYTES", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &idx) ) return TCL_ERROR;
  value = (char*)Tcl_GetByteArrayFromObj(objv[3], &trueLength);
  if( Tcl_GetIntFromObj(interp, objv[4], &bytes) ) return TCL_ERROR;
  if( bytes<0 ){
    toFree = malloc( trueLength + 1 );
    if( toFree==0 ){
      Tcl_AppendResult(interp, "out of memory", (void*)0);
      return TCL_ERROR;
    }
    memcpy(toFree, value, trueLength);
    toFree[trueLength] = 0;
    value = toFree;
  }
  rc = sqlite3_bind_text(pStmt, idx, value, bytes, SQLITE_TRANSIENT);
  free(toFree);
  if( sqlite3TestErrCode(interp, StmtToDb(pStmt), rc) ) return TCL_ERROR;
  if( rc!=SQLITE_OK ){
    Tcl_AppendResult(interp, sqlite3ErrName(rc), (void*)0);
    return TCL_ERROR;
  }

  return TCL_OK;
}

/*
** Usage:   sqlite3_bind_text16 ?-static? STMT N STRING BYTES
**
** Test the sqlite3_bind_text16 interface.  STMT is a prepared statement.
** N is the index of a wildcard in the prepared statement.  This command
** binds a UTF-16 string STRING to the wildcard.  The string is BYTES bytes
** long.
*/
static int SQLITE_TCLAPI test_bind_text16(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifndef SQLITE_OMIT_UTF16
  sqlite3_stmt *pStmt;
  int idx;
  int bytes;
  char *value;
  char *toFree = 0;
  int rc;
  Tcl_Size trueLength = 0;

  void (*xDel)(void*) = (objc==6?SQLITE_STATIC:SQLITE_TRANSIENT);
  Tcl_Obj *oStmt    = objv[objc-4];
  Tcl_Obj *oN       = objv[objc-3];
  Tcl_Obj *oString  = objv[objc-2];
  Tcl_Obj *oBytes   = objv[objc-1];

  if( objc!=5 && objc!=6){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT N VALUE BYTES", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(oStmt), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, oN, &idx) ) return TCL_ERROR;
  value = (char*)Tcl_GetByteArrayFromObj(oString, &trueLength);
  if( Tcl_GetIntFromObj(interp, oBytes, &bytes) ) return TCL_ERROR;
  if( bytes<0 && xDel==SQLITE_TRANSIENT ){
    toFree = malloc( trueLength + 3 );
    if( toFree==0 ){
      Tcl_AppendResult(interp, "out of memory", (void*)0);
      return TCL_ERROR;
    }
    memcpy(toFree, value, trueLength);
    memset(toFree+trueLength, 0, 3);
    value = toFree;
  }
  rc = sqlite3_bind_text16(pStmt, idx, (void *)value, bytes, xDel);
  free(toFree);
  if( sqlite3TestErrCode(interp, StmtToDb(pStmt), rc) ) return TCL_ERROR;
  if( rc!=SQLITE_OK ){
    Tcl_AppendResult(interp, sqlite3ErrName(rc), 0);
    return TCL_ERROR;
  }

#endif /* SQLITE_OMIT_UTF16 */
  return TCL_OK;
}

/*
** Usage:   sqlite3_bind_blob ?-static? STMT N DATA BYTES
**
** Test the sqlite3_bind_blob interface.  STMT is a prepared statement.
** N is the index of a wildcard in the prepared statement.  This command
** binds a BLOB to the wildcard.  The BLOB is BYTES bytes in size.
*/
static int SQLITE_TCLAPI test_bind_blob(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  Tcl_Size len;
  int idx;
  int bytes;
  char *value;
  int rc;
  sqlite3_destructor_type xDestructor = SQLITE_TRANSIENT;

  if( objc!=5 && objc!=6 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " STMT N DATA BYTES", 0);
    return TCL_ERROR;
  }

  if( objc==6 ){
    xDestructor = SQLITE_STATIC;
    objv++;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &idx) ) return TCL_ERROR;

  value = (char*)Tcl_GetByteArrayFromObj(objv[3], &len);
  if( Tcl_GetIntFromObj(interp, objv[4], &bytes) ) return TCL_ERROR;

  if( bytes>len ){
    char zBuf[200];
    sqlite3_snprintf(sizeof(zBuf), zBuf,
                     "cannot use %d blob bytes, have %d", bytes, (int)len);
    Tcl_AppendResult(interp, zBuf, (char*)0);
    return TCL_ERROR;
  }

  rc = sqlite3_bind_blob(pStmt, idx, value, bytes, xDestructor);
  if( sqlite3TestErrCode(interp, StmtToDb(pStmt), rc) ) return TCL_ERROR;
  if( rc!=SQLITE_OK ){
    return TCL_ERROR;
  }

  return TCL_OK;
}

/*
** Usage:   sqlite3_bind_value_from_preupdate STMT N NEW|OLD IDX
**
** Test the sqlite3_bind_value interface using sqlite3_value objects
** obtained from either sqlite3_preupdate_new() (if arg[3]=="new") or
** sqlite3_preupdate_old() if (arg[3]=="old"). IDX is the index to
** pass to the sqlite3_preupdate_xxx() function.
*/
static int SQLITE_TCLAPI test_bind_value_from_preupdate(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int idx;
  int bidx;
#ifdef SQLITE_ENABLE_PREUPDATE_HOOK
  const char *z3 = 0;
  sqlite3 *db = 0;
  sqlite3_value *pVal = 0;
#endif

  if( objc!=5 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT N NEW|OLD IDX");
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &idx) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[4], &bidx) ) return TCL_ERROR;

#ifdef SQLITE_ENABLE_PREUPDATE_HOOK
  z3 = Tcl_GetString(objv[3]);
  db = sqlite3_db_handle(pStmt);
  if( z3[0]=='n' ){
    sqlite3_preupdate_new(db, bidx, &pVal);
  }else if( z3[0]=='o' ){
    sqlite3_preupdate_old(db, bidx, &pVal);
  }else{
    Tcl_AppendResult(interp, "expected new or old, got: ", z3, (char*)0);
    return TCL_ERROR;
  }
  sqlite3_bind_value(pStmt, idx, pVal);
#endif

  return TCL_OK;
}

/*
** Usage:   sqlite3_bind_value_from_select STMT N SELECT
**
** Test the sqlite3_bind_value interface.  STMT is a prepared statement.
** N is the index of a wildcard in the prepared statement. 
*/
static int SQLITE_TCLAPI test_bind_value_from_select(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  sqlite3_stmt *pStmt2;
  int idx;
  const char *zSql = 0;
  sqlite3 *db = 0;
  int rc = SQLITE_OK;

  if( objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT N SELECT");
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &idx) ) return TCL_ERROR;
  zSql = Tcl_GetString(objv[3]);
  db = sqlite3_db_handle(pStmt);

  rc = sqlite3_prepare_v2(db, zSql, -1, &pStmt2, 0);
  if( rc!=SQLITE_OK ){
    Tcl_AppendResult(interp, "error in SQL: ", sqlite3_errmsg(db), (char*)0);
    return TCL_ERROR;
  }
  if( sqlite3_step(pStmt2)==SQLITE_ROW ){
    sqlite3_value *pVal = sqlite3_column_value(pStmt2, 0);
    sqlite3_bind_value(pStmt, idx, pVal);
  }
  rc = sqlite3_finalize(pStmt2);
  if( rc!=SQLITE_OK ){
    Tcl_AppendResult(interp, 
        "error runnning SQL: ", sqlite3_errmsg(db), (char*)0
    );
    return TCL_ERROR;
  }

  return TCL_OK;
}

#ifdef _WIN32
  struct iovec {
    void *iov_base;
    size_t iov_len;
  };
#else
# include <sys/uio.h>
#endif

#ifndef SQLITE_OMIT_VIRTUALTABLE
/*
** sqlite3_carray_bind [options...] STMT NAME VALUE ...
**
** Options:
**    -transient
**    -static
**    -int32
**    -int64
**    -double
**    -text
**    -blob
**
** Each call clears static data.  Called with no options does nothing
** but clear static data.
*/
static int SQLITE_TCLAPI test_carray_bind(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int eType = 0;   /* CARRAY_INT32 */
  int nData = 0;
  void *aData = 0;
  int isTransient = 0;
  int isStatic = 0;
  int idx;
  int i, j;
  int rc;
  void (*xDel)(void*) = sqlite3_free;
  static void *aStaticData = 0;
  static int nStaticData = 0;
  static int eStaticType = 0;
  extern int sqlite3_carray_bind(
    sqlite3_stmt *pStmt,
    int i,
    void *aData,
    int nData,
    int mFlags,
    void (*xDestroy)(void*)
  );
  
  if( aStaticData ){
    /* Always clear preexisting static data on every call */
    if( eStaticType==3 ){
      for(i=0; i<nStaticData; i++){
        sqlite3_free(((char**)aStaticData)[i]);
      }
    }
    if( eStaticType==4 ){
      for(i=0; i<nStaticData; i++){
        sqlite3_free(((struct iovec*)aStaticData)[i].iov_base);
      }
    } 
    sqlite3_free(aStaticData);
    aStaticData = 0;
    nStaticData = 0;
    eStaticType = 0;
  }
  if( objc==1 ) return TCL_OK;

  for(i=1; i<objc && Tcl_GetString(objv[i])[0]=='-'; i++){
    const char *z = Tcl_GetString(objv[i]);
    if( strcmp(z, "-transient")==0 ){
      isTransient = 1;
      xDel = SQLITE_TRANSIENT;
    }else
    if( strcmp(z, "-static")==0 ){
      isStatic = 1;
      xDel = SQLITE_STATIC;
    }else
    if( strcmp(z, "-int32")==0 ){
      eType = 0;  /* CARRAY_INT32 */
    }else
    if( strcmp(z, "-int64")==0 ){
      eType = 1;  /* CARRAY_INT64 */
    }else
    if( strcmp(z, "-double")==0 ){
      eType = 2;  /* CARRAY_DOUBLE */
    }else
    if( strcmp(z, "-text")==0 ){
      eType = 3;  /* CARRAY_TEXT */
    }else
    if( strcmp(z, "-blob")==0 ){
      eType = 4;  /* CARRAY_BLOB */
    }else
    if( strcmp(z, "--")==0 ){
      break;
    }else
    {
      Tcl_AppendResult(interp, "unknown option: ", z, (char*)0);
      return TCL_ERROR;
    }
  }
  if( eType==3 && !isStatic && !isTransient ){
    Tcl_AppendResult(interp, "text data must be either -static or -transient",
                     (char*)0);
    return TCL_ERROR;
  }
  if( eType==4 && !isStatic && !isTransient ){
    Tcl_AppendResult(interp, "blob data must be either -static or -transient",
                     (char*)0);
    return TCL_ERROR;
  }
  if( isStatic && isTransient ){
    Tcl_AppendResult(interp, "cannot be both -static and -transient",
                     (char*)0);
    return TCL_ERROR;
  }
  if( objc-i < 2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "[OPTIONS] STMT IDX VALUE ...");
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, Tcl_GetString(objv[i]), &pStmt) ) return TCL_ERROR;
  i++;
  if( Tcl_GetIntFromObj(interp, objv[i], &idx) ) return TCL_ERROR;
  i++;
  nData = objc - i;
  switch( eType + 5*(nData<=0) ){
    case 0: { /* INT32 */
      int *a = sqlite3_malloc( sizeof(int)*nData );
      if( a==0 ){ rc = SQLITE_NOMEM; goto carray_bind_done; }
      for(j=0; j<nData; j++){
        int v;
        if( Tcl_GetIntFromObj(interp, objv[i+j], &v) ){
          sqlite3_free(a);
          return TCL_ERROR;
        }
        a[j] = v;
      }
      aData = a;
      break;
    }
    case 1: { /* INT64 */
      sqlite3_int64 *a = sqlite3_malloc( sizeof(sqlite3_int64)*nData );
      if( a==0 ){ rc = SQLITE_NOMEM; goto carray_bind_done; }
      for(j=0; j<nData; j++){
        Tcl_WideInt v;
        if( Tcl_GetWideIntFromObj(interp, objv[i+j], &v) ){
          sqlite3_free(a);
          return TCL_ERROR;
        }
        a[j] = v;
      }
      aData = a;
      break;
    }
    case 2: { /* DOUBLE */
      double *a = sqlite3_malloc( sizeof(double)*nData );
      if( a==0 ){ rc = SQLITE_NOMEM; goto carray_bind_done; }
      for(j=0; j<nData; j++){
        double v;
        if( Tcl_GetDoubleFromObj(interp, objv[i+j], &v) ){
          sqlite3_free(a);
          return TCL_ERROR;
        }
        a[j] = v;
      }
      aData = a;
      break;
    }
    case 3: { /* TEXT */
      char **a = sqlite3_malloc( sizeof(char*)*nData );
      if( a==0 ){ rc = SQLITE_NOMEM; goto carray_bind_done; }
      for(j=0; j<nData; j++){
        const char *v = Tcl_GetString(objv[i+j]);
        a[j] = sqlite3_mprintf("%s", v);
      }
      aData = a;
      break;
    }
    case 4: { /* BLOB */
      struct iovec *a = sqlite3_malloc( sizeof(struct iovec)*nData );
      if( a==0 ){ rc = SQLITE_NOMEM; goto carray_bind_done; }
      for(j=0; j<nData; j++){
        Tcl_Size n = 0;
        unsigned char *v = Tcl_GetByteArrayFromObj(objv[i+i], &n);
        a[j].iov_len = (size_t)n;
        a[j].iov_base = sqlite3_malloc64( n );
        if( a[j].iov_base==0 ){
          a[j].iov_len = 0;
        }else{
          memcpy(a[j].iov_base, v, n);
        }
      }
      aData = a;
      break;
    }
    case 5: { /* nData==0 */
      aData = "";
      xDel = SQLITE_STATIC;
      isTransient = 0;
      isStatic = 0;
      break;
    }
  }
  if( isStatic ){
    aStaticData = aData;
    nStaticData = nData;
    eStaticType = eType;
  }
  rc = sqlite3_carray_bind(pStmt, idx, aData, nData, eType, xDel);
  if( isTransient ){
    if( eType==3 ){
      for(i=0; i<nData; i++) sqlite3_free(((char**)aData)[i]);
    }
    if( eType==4 ){
      for(i=0; i<nData; i++) sqlite3_free(((struct iovec*)aData)[i].iov_base);
    }
    sqlite3_free(aData);
  }
carray_bind_done:
  if( rc ){
    Tcl_AppendResult(interp, sqlite3_errstr(rc), (char*)0);
    return TCL_ERROR;
  }
  return TCL_OK;
}
#endif /* SQLITE_OMIT_VIRTUALTABLE */

/*
** Usage:   sqlite3_bind_parameter_count  STMT
**
** Return the number of wildcards in the given statement.
*/
static int SQLITE_TCLAPI test_bind_parameter_count(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT");
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  Tcl_SetObjResult(interp, Tcl_NewIntObj(sqlite3_bind_parameter_count(pStmt)));
  return TCL_OK;
}

/*
** Usage:   sqlite3_bind_parameter_name  STMT  N
**
** Return the name of the Nth wildcard.  The first wildcard is 1.
** An empty string is returned if N is out of range or if the wildcard
** is nameless.
*/
static int SQLITE_TCLAPI test_bind_parameter_name(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int i;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT N");
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &i) ) return TCL_ERROR;
  Tcl_SetObjResult(interp, 
     Tcl_NewStringObj(sqlite3_bind_parameter_name(pStmt,i),-1)
  );
  return TCL_OK;
}

/*
** Usage:   sqlite3_bind_parameter_index  STMT  NAME
**
** Return the index of the wildcard called NAME.  Return 0 if there is
** no such wildcard.
*/
static int SQLITE_TCLAPI test_bind_parameter_index(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT NAME");
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  Tcl_SetObjResult(interp, 
     Tcl_NewIntObj(
       sqlite3_bind_parameter_index(pStmt,Tcl_GetString(objv[2]))
     )
  );
  return TCL_OK;
}

/*
** Usage:   sqlite3_clear_bindings STMT
**
*/
static int SQLITE_TCLAPI test_clear_bindings(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT");
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  Tcl_SetObjResult(interp, Tcl_NewIntObj(sqlite3_clear_bindings(pStmt)));
  return TCL_OK;
}

/*
** Usage:   sqlite3_sleep MILLISECONDS
*/
static int SQLITE_TCLAPI test_sleep(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int ms;

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "MILLISECONDS");
    return TCL_ERROR;
  }
  if( Tcl_GetIntFromObj(interp, objv[1], &ms) ){
    return TCL_ERROR;
  }
  Tcl_SetObjResult(interp, Tcl_NewIntObj(sqlite3_sleep(ms)));
  return TCL_OK;
}

/*
** Usage: sqlite3_extended_errcode DB
**
** Return the string representation of the most recent sqlite3_* API
** error code. e.g. "SQLITE_ERROR".
*/
static int SQLITE_TCLAPI test_ex_errcode(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  int rc;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  rc = sqlite3_extended_errcode(db);
  Tcl_AppendResult(interp, (char *)t1ErrorName(rc), 0);
  return TCL_OK;
}


/*
** Usage: sqlite3_errcode DB
**
** Return the string representation of the most recent sqlite3_* API
** error code. e.g. "SQLITE_ERROR".
*/
static int SQLITE_TCLAPI test_errcode(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  int rc;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  rc = sqlite3_errcode(db);
  Tcl_AppendResult(interp, (char *)t1ErrorName(rc), 0);
  return TCL_OK;
}

/*
** Usage:   sqlite3_errmsg DB
**
** Returns the UTF-8 representation of the error message string for the
** most recent sqlite3_* API call.
*/
static int SQLITE_TCLAPI test_errmsg(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  const char *zErr;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;

  zErr = sqlite3_errmsg(db);
  Tcl_SetObjResult(interp, Tcl_NewStringObj(zErr, -1));
  return TCL_OK;
}


/*
** Usage:   sqlite3_error_offset DB
**
** Return the byte offset into the input UTF8 SQL for the most recent
** error, or -1 of the error does not refer to a specific token.
*/
static int SQLITE_TCLAPI test_error_offset(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  int iByteOffset;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;

  iByteOffset = sqlite3_error_offset(db);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(iByteOffset));
  return TCL_OK;
}

/*
** Usage:   test_errmsg16 DB
**
** Returns the UTF-16 representation of the error message string for the
** most recent sqlite3_* API call. This is a byte array object at the TCL 
** level, and it includes the 0x00 0x00 terminator bytes at the end of the
** UTF-16 string.
*/
static int SQLITE_TCLAPI test_errmsg16(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifndef SQLITE_OMIT_UTF16
  sqlite3 *db;
  const void *zErr;
  const char *z;
  int bytes = 0;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;

  zErr = sqlite3_errmsg16(db);
  if( zErr ){
    z = zErr;
    for(bytes=0; z[bytes] || z[bytes+1]; bytes+=2){}
  }
  Tcl_SetObjResult(interp, Tcl_NewByteArrayObj(zErr, bytes));
#endif /* SQLITE_OMIT_UTF16 */
  return TCL_OK;
}

/*
** Usage: sqlite3_prepare DB sql bytes ?tailvar?
**
** Compile up to <bytes> bytes of the supplied SQL string <sql> using
** database handle <DB>. The parameter <tailval> is the name of a global
** variable that is set to the unused portion of <sql> (if any). A
** STMT handle is returned.
*/
static int SQLITE_TCLAPI test_prepare(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  const char *zSql;
  int bytes;
  const char *zTail = 0;
  sqlite3_stmt *pStmt = 0;
  char zBuf[50];
  int rc;

  if( objc!=5 && objc!=4 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " DB sql bytes ?tailvar?", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zSql = Tcl_GetString(objv[2]);
  if( Tcl_GetIntFromObj(interp, objv[3], &bytes) ) return TCL_ERROR;

  rc = sqlite3_prepare(db, zSql, bytes, &pStmt, objc>=5 ? &zTail : 0);
  Tcl_ResetResult(interp);
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  if( zTail && objc>=5 ){
    if( bytes>=0 ){
      bytes = bytes - (int)(zTail-zSql);
    }
    if( (int)strlen(zTail)<bytes ){
      bytes = (int)strlen(zTail);
    }
    Tcl_ObjSetVar2(interp, objv[4], 0, Tcl_NewStringObj(zTail, bytes), 0);
  }
  if( rc!=SQLITE_OK ){
    assert( pStmt==0 );
    sqlite3_snprintf(sizeof(zBuf), zBuf, "(%d) ", rc);
    Tcl_AppendResult(interp, zBuf, sqlite3_errmsg(db), 0);
    return TCL_ERROR;
  }

  if( pStmt ){
    if( sqlite3TestMakePointerStr(interp, zBuf, pStmt) ) return TCL_ERROR;
    Tcl_AppendResult(interp, zBuf, 0);
  }
  return TCL_OK;
}

/*
** Usage: sqlite3_prepare_v2 DB sql bytes ?tailvar?
**
** Compile up to <bytes> bytes of the supplied SQL string <sql> using
** database handle <DB>. The parameter <tailval> is the name of a global
** variable that is set to the unused portion of <sql> (if any). A
** STMT handle is returned.
*/
static int SQLITE_TCLAPI test_prepare_v2(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  const char *zSql;
  char *zCopy = 0;                /* malloc() copy of zSql */
  int bytes;
  const char *zTail = 0;
  const char **pzTail;
  sqlite3_stmt *pStmt = 0;
  char zBuf[50];
  int rc;

  if( objc!=5 && objc!=4 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " DB sql bytes tailvar", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zSql = Tcl_GetString(objv[2]);
  if( Tcl_GetIntFromObj(interp, objv[3], &bytes) ) return TCL_ERROR;

  /* Instead of using zSql directly, make a copy into a buffer obtained
  ** directly from malloc(). The idea is to make it easier for valgrind
  ** to spot buffer overreads.  */
  if( bytes>=0 ){
    zCopy = malloc(bytes);
    memcpy(zCopy, zSql, bytes);
  }else{
    int n = (int)strlen(zSql) + 1;
    zCopy = malloc(n);
    memcpy(zCopy, zSql, n);
  }
  pzTail = objc>=5 ? &zTail : 0;
  rc = sqlite3_prepare_v2(db, zCopy, bytes, &pStmt, pzTail);
  if( objc>=5 ){
    zTail = &zSql[(zTail - zCopy)];
  }
  free(zCopy);

  assert(rc==SQLITE_OK || pStmt==0);
  Tcl_ResetResult(interp);
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  if( rc==SQLITE_OK && objc>=5 && zTail ){
    if( bytes>=0 ){
      bytes = bytes - (int)(zTail-zSql);
    }
    Tcl_ObjSetVar2(interp, objv[4], 0, Tcl_NewStringObj(zTail, bytes), 0);
  }
  if( rc!=SQLITE_OK ){
    assert( pStmt==0 );
    sqlite3_snprintf(sizeof(zBuf), zBuf, "(%d) ", rc);
    Tcl_AppendResult(interp, zBuf, sqlite3_errmsg(db), 0);
    return TCL_ERROR;
  }

  if( pStmt ){
    if( sqlite3TestMakePointerStr(interp, zBuf, pStmt) ) return TCL_ERROR;
    Tcl_AppendResult(interp, zBuf, 0);
  }
  return TCL_OK;
}

/*
** Usage: sqlite3_prepare_v3 DB sql bytes flags ?tailvar?
**
** Compile up to <bytes> bytes of the supplied SQL string <sql> using
** database handle <DB> and flags <flags>. The parameter <tailval> is
** the name of a global variable that is set to the unused portion of
** <sql> (if any). A STMT handle is returned.
*/
static int SQLITE_TCLAPI test_prepare_v3(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  const char *zSql;
  char *zCopy = 0;                /* malloc() copy of zSql */
  int bytes, flags;
  const char *zTail = 0;
  const char **pzTail;
  sqlite3_stmt *pStmt = 0;
  char zBuf[50];
  int rc;

  if( objc!=6 && objc!=5 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " DB sql bytes flags tailvar", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zSql = Tcl_GetString(objv[2]);
  if( Tcl_GetIntFromObj(interp, objv[3], &bytes) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[4], &flags) ) return TCL_ERROR;

  /* Instead of using zSql directly, make a copy into a buffer obtained
  ** directly from malloc(). The idea is to make it easier for valgrind
  ** to spot buffer overreads.  */
  if( bytes>=0 ){
    zCopy = malloc(bytes);
    memcpy(zCopy, zSql, bytes);
  }else{
    int n = (int)strlen(zSql) + 1;
    zCopy = malloc(n);
    memcpy(zCopy, zSql, n);
  }
  pzTail = objc>=6 ? &zTail : 0;
  rc = sqlite3_prepare_v3(db, zCopy, bytes, (unsigned int)flags,&pStmt,pzTail);
  zTail = &zSql[(zTail - zCopy)];
  free(zCopy);

  assert(rc==SQLITE_OK || pStmt==0);
  Tcl_ResetResult(interp);
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  if( rc==SQLITE_OK && zTail && objc>=6 ){
    if( bytes>=0 ){
      bytes = bytes - (int)(zTail-zSql);
    }
    Tcl_ObjSetVar2(interp, objv[5], 0, Tcl_NewStringObj(zTail, bytes), 0);
  }
  if( rc!=SQLITE_OK ){
    assert( pStmt==0 );
    sqlite3_snprintf(sizeof(zBuf), zBuf, "(%d) ", rc);
    Tcl_AppendResult(interp, zBuf, sqlite3_errmsg(db), 0);
    return TCL_ERROR;
  }

  if( pStmt ){
    if( sqlite3TestMakePointerStr(interp, zBuf, pStmt) ) return TCL_ERROR;
    Tcl_AppendResult(interp, zBuf, 0);
  }
  return TCL_OK;
}

/*
** Usage: sqlite3_prepare_tkt3134 DB
**
** Generate a prepared statement for a zero-byte string as a test
** for ticket #3134.  The string should be preceded by a zero byte.
*/
static int SQLITE_TCLAPI test_prepare_tkt3134(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  static const char zSql[] = "\000SELECT 1";
  sqlite3_stmt *pStmt = 0;
  char zBuf[50];
  int rc;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " DB sql bytes tailvar", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  rc = sqlite3_prepare_v2(db, &zSql[1], 0, &pStmt, 0);
  assert(rc==SQLITE_OK || pStmt==0);
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  if( rc!=SQLITE_OK ){
    assert( pStmt==0 );
    sqlite3_snprintf(sizeof(zBuf), zBuf, "(%d) ", rc);
    Tcl_AppendResult(interp, zBuf, sqlite3_errmsg(db), 0);
    return TCL_ERROR;
  }

  if( pStmt ){
    if( sqlite3TestMakePointerStr(interp, zBuf, pStmt) ) return TCL_ERROR;
    Tcl_AppendResult(interp, zBuf, 0);
  }
  return TCL_OK;
}

/*
** Usage: sqlite3_prepare16 DB sql bytes tailvar
**
** Compile up to <bytes> bytes of the supplied SQL string <sql> using
** database handle <DB>. The parameter <tailval> is the name of a global
** variable that is set to the unused portion of <sql> (if any). A
** STMT handle is returned.
*/
static int SQLITE_TCLAPI test_prepare16(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifndef SQLITE_OMIT_UTF16
  sqlite3 *db;
  const void *zSql;
  const void *zTail = 0;
  Tcl_Obj *pTail = 0;
  sqlite3_stmt *pStmt = 0;
  char zBuf[50]; 
  int rc;
  int bytes;                /* The integer specified as arg 3 */
  Tcl_Size objlen;          /* The byte-array length of arg 2 */

  if( objc!=5 && objc!=4 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " DB sql bytes ?tailvar?", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zSql = Tcl_GetByteArrayFromObj(objv[2], &objlen);
  if( Tcl_GetIntFromObj(interp, objv[3], &bytes) ) return TCL_ERROR;

  rc = sqlite3_prepare16(db, zSql, bytes, &pStmt, objc>=5 ? &zTail : 0);
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  if( rc ){
    return TCL_ERROR;
  }

  if( objc>=5 ){
    if( zTail ){
      objlen = objlen - (int)((u8 *)zTail-(u8 *)zSql);
    }else{
      objlen = 0;
    }
    pTail = Tcl_NewByteArrayObj((u8 *)zTail, objlen);
    Tcl_IncrRefCount(pTail);
    Tcl_ObjSetVar2(interp, objv[4], 0, pTail, 0);
    Tcl_DecrRefCount(pTail);
  }

  if( pStmt ){
    if( sqlite3TestMakePointerStr(interp, zBuf, pStmt) ) return TCL_ERROR;
  }
  Tcl_AppendResult(interp, zBuf, 0);
#endif /* SQLITE_OMIT_UTF16 */
  return TCL_OK;
}

/*
** Usage: sqlite3_prepare16_v2 DB sql bytes ?tailvar?
**
** Compile up to <bytes> bytes of the supplied SQL string <sql> using
** database handle <DB>. The parameter <tailval> is the name of a global
** variable that is set to the unused portion of <sql> (if any). A
** STMT handle is returned.
*/
static int SQLITE_TCLAPI test_prepare16_v2(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifndef SQLITE_OMIT_UTF16
  sqlite3 *db;
  const void *zSql;
  const void *zTail = 0;
  Tcl_Obj *pTail = 0;
  sqlite3_stmt *pStmt = 0;
  char zBuf[50]; 
  int rc;
  int bytes;                /* The integer specified as arg 3 */
  Tcl_Size objlen;          /* The byte-array length of arg 2 */

  if( objc!=5 && objc!=4 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " DB sql bytes ?tailvar?", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zSql = Tcl_GetByteArrayFromObj(objv[2], &objlen);
  if( Tcl_GetIntFromObj(interp, objv[3], &bytes) ) return TCL_ERROR;

  rc = sqlite3_prepare16_v2(db, zSql, bytes, &pStmt, objc>=5 ? &zTail : 0);
  if( sqlite3TestErrCode(interp, db, rc) ) return TCL_ERROR;
  if( rc ){
    return TCL_ERROR;
  }

  if( objc>=5 ){
    if( zTail ){
      objlen = objlen - (int)((u8 *)zTail-(u8 *)zSql);
    }else{
      objlen = 0;
    }
    pTail = Tcl_NewByteArrayObj((u8 *)zTail, objlen);
    Tcl_IncrRefCount(pTail);
    Tcl_ObjSetVar2(interp, objv[4], 0, pTail, 0);
    Tcl_DecrRefCount(pTail);
  }

  if( pStmt ){
    if( sqlite3TestMakePointerStr(interp, zBuf, pStmt) ) return TCL_ERROR;
  }
  Tcl_AppendResult(interp, zBuf, 0);
#endif /* SQLITE_OMIT_UTF16 */
  return TCL_OK;
}

/*
** Usage: sqlite3_open filename ?options-list?
*/
static int SQLITE_TCLAPI test_open(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  const char *zFilename;
  sqlite3 *db;
  char zBuf[100];

  if( objc!=3 && objc!=2 && objc!=1 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " filename options-list", 0);
    return TCL_ERROR;
  }

  zFilename = objc>1 ? Tcl_GetString(objv[1]) : 0;
  sqlite3_open(zFilename, &db);
  
  if( sqlite3TestMakePointerStr(interp, zBuf, db) ) return TCL_ERROR;
  Tcl_AppendResult(interp, zBuf, 0);
  return TCL_OK;
}

/*
** Usage: sqlite3_open_v2 FILENAME FLAGS VFS
*/
static int SQLITE_TCLAPI test_open_v2(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  const char *zFilename;
  const char *zVfs;
  int flags = 0;
  sqlite3 *db;
  int rc;
  char zBuf[100];

  Tcl_Size nFlag;
  Tcl_Obj **apFlag;
  Tcl_Size i;

  if( objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "FILENAME FLAGS VFS");
    return TCL_ERROR;
  }
  zFilename = Tcl_GetString(objv[1]);
  zVfs = Tcl_GetString(objv[3]);
  if( zVfs[0]==0x00 ) zVfs = 0;

  rc = Tcl_ListObjGetElements(interp, objv[2], &nFlag, &apFlag);
  if( rc!=TCL_OK ) return rc;
  for(i=0; i<nFlag; i++){
    int iFlag;
    struct OpenFlag {
      const char *zFlag;
      int flag;
    } aFlag[] = {
      { "SQLITE_OPEN_READONLY", SQLITE_OPEN_READONLY },
      { "SQLITE_OPEN_READWRITE", SQLITE_OPEN_READWRITE },
      { "SQLITE_OPEN_CREATE", SQLITE_OPEN_CREATE },
      { "SQLITE_OPEN_DELETEONCLOSE", SQLITE_OPEN_DELETEONCLOSE },
      { "SQLITE_OPEN_EXCLUSIVE", SQLITE_OPEN_EXCLUSIVE },
      { "SQLITE_OPEN_AUTOPROXY", SQLITE_OPEN_AUTOPROXY },
      { "SQLITE_OPEN_MAIN_DB", SQLITE_OPEN_MAIN_DB },
      { "SQLITE_OPEN_TEMP_DB", SQLITE_OPEN_TEMP_DB },
      { "SQLITE_OPEN_TRANSIENT_DB", SQLITE_OPEN_TRANSIENT_DB },
      { "SQLITE_OPEN_MAIN_JOURNAL", SQLITE_OPEN_MAIN_JOURNAL },
      { "SQLITE_OPEN_TEMP_JOURNAL", SQLITE_OPEN_TEMP_JOURNAL },
      { "SQLITE_OPEN_SUBJOURNAL", SQLITE_OPEN_SUBJOURNAL },
      { "SQLITE_OPEN_SUPER_JOURNAL", SQLITE_OPEN_SUPER_JOURNAL },
      { "SQLITE_OPEN_NOMUTEX", SQLITE_OPEN_NOMUTEX },
      { "SQLITE_OPEN_FULLMUTEX", SQLITE_OPEN_FULLMUTEX },
      { "SQLITE_OPEN_SHAREDCACHE", SQLITE_OPEN_SHAREDCACHE },
      { "SQLITE_OPEN_PRIVATECACHE", SQLITE_OPEN_PRIVATECACHE },
      { "SQLITE_OPEN_WAL", SQLITE_OPEN_WAL },
      { "SQLITE_OPEN_URI", SQLITE_OPEN_URI },
      { "SQLITE_OPEN_EXRESCODE", SQLITE_OPEN_EXRESCODE },
      { 0, 0 }
    };
    rc = Tcl_GetIndexFromObjStruct(interp, apFlag[i], aFlag, sizeof(aFlag[0]), 
        "flag", 0, &iFlag
    );
    if( rc!=TCL_OK ) return rc;
    flags |= aFlag[iFlag].flag;
  }

  rc = sqlite3_open_v2(zFilename, &db, flags, zVfs);
  if( sqlite3TestMakePointerStr(interp, zBuf, db) ) return TCL_ERROR;
  Tcl_AppendResult(interp, zBuf, 0);
  return TCL_OK;
}

/*
** Usage: sqlite3_open16 filename options
*/
static int SQLITE_TCLAPI test_open16(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifndef SQLITE_OMIT_UTF16
  const void *zFilename;
  sqlite3 *db;
  char zBuf[100];

  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " filename options-list", 0);
    return TCL_ERROR;
  }

  zFilename = Tcl_GetByteArrayFromObj(objv[1], 0);
  sqlite3_open16(zFilename, &db);
  
  if( sqlite3TestMakePointerStr(interp, zBuf, db) ) return TCL_ERROR;
  Tcl_AppendResult(interp, zBuf, 0);
#endif /* SQLITE_OMIT_UTF16 */
  return TCL_OK;
}

/*
** Usage: sqlite3_complete16 <UTF-16 string>
**
** Return 1 if the supplied argument is a complete SQL statement, or zero
** otherwise.
*/
static int SQLITE_TCLAPI test_complete16(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#if !defined(SQLITE_OMIT_COMPLETE) && !defined(SQLITE_OMIT_UTF16)
  char *zBuf;

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "<utf-16 sql>");
    return TCL_ERROR;
  }

  zBuf = (char*)Tcl_GetByteArrayFromObj(objv[1], 0);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(sqlite3_complete16(zBuf)));
#endif /* SQLITE_OMIT_COMPLETE && SQLITE_OMIT_UTF16 */
  return TCL_OK;
}

/*
** Usage: sqlite3_normalize SQL
**
** Return the normalized value for an SQL statement.
*/
static int SQLITE_TCLAPI test_normalize(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  char *zSql;
  char *zNorm;
  extern char *sqlite3_normalize(const char*);

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "SQL");
    return TCL_ERROR;
  }

  zSql = (char*)Tcl_GetString(objv[1]);
  zNorm = sqlite3_normalize(zSql);
  if( zNorm ){
    Tcl_SetObjResult(interp, Tcl_NewStringObj(zNorm, -1));
    sqlite3_free(zNorm);
  }
  return TCL_OK;
}

/*
** Usage: sqlite3_step STMT
**
** Advance the statement to the next row.
*/
static int SQLITE_TCLAPI test_step(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int rc;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " STMT", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  rc = sqlite3_step(pStmt);

  /* if( rc!=SQLITE_DONE && rc!=SQLITE_ROW ) return TCL_ERROR; */
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), 0);
  return TCL_OK;
}

static int SQLITE_TCLAPI test_sql(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT");
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  Tcl_SetResult(interp, (char *)sqlite3_sql(pStmt), TCL_VOLATILE);
  return TCL_OK;
}
static int SQLITE_TCLAPI test_ex_sql(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  char *z;

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT");
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  z = sqlite3_expanded_sql(pStmt);
  Tcl_SetResult(interp, z, TCL_VOLATILE);
  sqlite3_free(z);
  return TCL_OK;
}
#ifdef SQLITE_ENABLE_NORMALIZE
static int SQLITE_TCLAPI test_norm_sql(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT");
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  Tcl_SetResult(interp, (char *)sqlite3_normalized_sql(pStmt), TCL_VOLATILE);
  return TCL_OK;
}
#endif /* SQLITE_ENABLE_NORMALIZE */

/*
** Usage: sqlite3_column_count STMT 
**
** Return the number of columns returned by the sql statement STMT.
*/
static int SQLITE_TCLAPI test_column_count(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " STMT column", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;

  Tcl_SetObjResult(interp, Tcl_NewIntObj(sqlite3_column_count(pStmt)));
  return TCL_OK;
}

/*
** Usage: sqlite3_column_type STMT column
**
** Return the type of the data in column 'column' of the current row.
*/
static int SQLITE_TCLAPI test_column_type(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int col;
  int tp;

  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " STMT column", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &col) ) return TCL_ERROR;

  tp = sqlite3_column_type(pStmt, col);
  switch( tp ){
    case SQLITE_INTEGER: 
      Tcl_SetResult(interp, "INTEGER", TCL_STATIC); 
      break;
    case SQLITE_NULL:
      Tcl_SetResult(interp, "NULL", TCL_STATIC); 
      break;
    case SQLITE_FLOAT:
      Tcl_SetResult(interp, "FLOAT", TCL_STATIC); 
      break;
    case SQLITE_TEXT:
      Tcl_SetResult(interp, "TEXT", TCL_STATIC); 
      break;
    case SQLITE_BLOB:
      Tcl_SetResult(interp, "BLOB", TCL_STATIC); 
      break;
    default:
      assert(0);
  }

  return TCL_OK;
}

/*
** Usage: sqlite3_column_int64 STMT column
**
** Return the data in column 'column' of the current row cast as an
** wide (64-bit) integer.
*/
static int SQLITE_TCLAPI test_column_int64(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int col;
  i64 iVal;

  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " STMT column", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &col) ) return TCL_ERROR;

  iVal = sqlite3_column_int64(pStmt, col);
  Tcl_SetObjResult(interp, Tcl_NewWideIntObj(iVal));
  return TCL_OK;
}

/*
** Usage: sqlite3_column_blob STMT column
*/
static int SQLITE_TCLAPI test_column_blob(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int col;

  int len;
  const void *pBlob;

  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " STMT column", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &col) ) return TCL_ERROR;

  len = sqlite3_column_bytes(pStmt, col);
  pBlob = sqlite3_column_blob(pStmt, col);
  Tcl_SetObjResult(interp, Tcl_NewByteArrayObj(pBlob, len));
  return TCL_OK;
}

/*
** Usage: sqlite3_column_double STMT column
**
** Return the data in column 'column' of the current row cast as a double.
*/
static int SQLITE_TCLAPI test_column_double(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int col;
  double rVal;

  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " STMT column", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &col) ) return TCL_ERROR;

  rVal = sqlite3_column_double(pStmt, col);
  Tcl_SetObjResult(interp, Tcl_NewDoubleObj(rVal));
  return TCL_OK;
}

/*
** Usage: sqlite3_data_count STMT 
**
** Return the number of columns returned by the sql statement STMT.
*/
static int SQLITE_TCLAPI test_data_count(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " STMT column", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;

  Tcl_SetObjResult(interp, Tcl_NewIntObj(sqlite3_data_count(pStmt)));
  return TCL_OK;
}

/*
** Usage: sqlite3_column_text STMT column
**
** Usage: sqlite3_column_decltype STMT column
**
** Usage: sqlite3_column_name STMT column
*/
static int SQLITE_TCLAPI test_stmt_utf8(
  void * clientData,        /* Pointer to SQLite API function to be invoke */
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int col;
  const char *(*xFunc)(sqlite3_stmt*, int);
  const unsigned char *(*xFuncU)(sqlite3_stmt*, int);
  const char *zRet;

  xFunc = (const char *(*)(sqlite3_stmt*, int))clientData;
  xFuncU = (const unsigned char*(*)(sqlite3_stmt*,int))xFunc;
  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " STMT column", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &col) ) return TCL_ERROR;
  if( xFunc==sqlite3_column_name || xFunc==sqlite3_column_decltype ){
    zRet = xFunc(pStmt, col);
  }else{
    zRet = (const char*)xFuncU(pStmt, col);
  }
  if( zRet ){
    Tcl_SetResult(interp, (char *)zRet, 0);
  }
  return TCL_OK;
}

static int SQLITE_TCLAPI test_global_recover(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifndef SQLITE_OMIT_DEPRECATED
  int rc;
  if( objc!=1 ){
    Tcl_WrongNumArgs(interp, 1, objv, "");
    return TCL_ERROR;
  }
  rc = sqlite3_global_recover();
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), TCL_STATIC);
#endif
  return TCL_OK;
}

/*
** Usage: sqlite3_column_text STMT column
**
** Usage: sqlite3_column_decltype STMT column
**
** Usage: sqlite3_column_name STMT column
*/
static int SQLITE_TCLAPI test_stmt_utf16(
  void * clientData,     /* Pointer to SQLite API function to be invoked */
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifndef SQLITE_OMIT_UTF16
  sqlite3_stmt *pStmt;
  int col;
  Tcl_Obj *pRet;
  const void *zName16;
  const void *(*xFunc)(sqlite3_stmt*, int);

  xFunc = (const void *(*)(sqlite3_stmt*, int))clientData;
  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " STMT column", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &col) ) return TCL_ERROR;

  zName16 = xFunc(pStmt, col);
  if( zName16 ){
    int n;
    const char *z = zName16;
    for(n=0; z[n] || z[n+1]; n+=2){}
    pRet = Tcl_NewByteArrayObj(zName16, n+2);
    Tcl_SetObjResult(interp, pRet);
  }
#endif /* SQLITE_OMIT_UTF16 */

  return TCL_OK;
}

/*
** Usage: sqlite3_column_int STMT column
**
** Usage: sqlite3_column_bytes STMT column
**
** Usage: sqlite3_column_bytes16 STMT column
**
*/
static int SQLITE_TCLAPI test_stmt_int(
  void * clientData,    /* Pointer to SQLite API function to be invoked */
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_stmt *pStmt;
  int col;
  int (*xFunc)(sqlite3_stmt*, int);

  xFunc = (int (*)(sqlite3_stmt*, int))clientData;
  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", 
       Tcl_GetString(objv[0]), " STMT column", 0);
    return TCL_ERROR;
  }

  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[2], &col) ) return TCL_ERROR;

  Tcl_SetObjResult(interp, Tcl_NewIntObj(xFunc(pStmt, col)));
  return TCL_OK;
}

/*
** Usage:  sqlite3_interrupt  DB 
**
** Trigger an interrupt on DB
*/
static int SQLITE_TCLAPI test_interrupt(
  void * clientData,
  Tcl_Interp *interp,
  int argc,
  char **argv
){
  sqlite3 *db;
  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  sqlite3_interrupt(db);
  return TCL_OK;
}

/*
** Usage:  sqlite3_is_interrupted  DB 
**
** return true if an interrupt is current in effect on DB
*/
static int SQLITE_TCLAPI test_is_interrupted(
  void * clientData,
  Tcl_Interp *interp,
  int argc,
  char **argv
){
  sqlite3 *db;
  int rc;
  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  rc = sqlite3_is_interrupted(db);
  Tcl_AppendResult(interp, rc ? "1" : "0", (void*)0);
  return TCL_OK;
}

/*
** Usage: sqlite_delete_function DB function-name
**
** Delete the user function 'function-name' from database handle DB. It
** is assumed that the user function was created as UTF8, any number of
** arguments (the way the TCL interface does it).
*/
static int SQLITE_TCLAPI delete_function(
  void * clientData,
  Tcl_Interp *interp,
  int argc,
  char **argv
){
  int rc;
  sqlite3 *db;
  if( argc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
        " DB function-name", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  rc = sqlite3_create_function(db, argv[2], -1, SQLITE_UTF8, 0, 0, 0, 0);
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), TCL_STATIC);
  return TCL_OK;
}

/*
** Usage: sqlite_delete_collation DB collation-name
**
** Delete the collation sequence 'collation-name' from database handle 
** DB. It is assumed that the collation sequence was created as UTF8 (the 
** way the TCL interface does it).
*/
static int SQLITE_TCLAPI delete_collation(
  void * clientData,
  Tcl_Interp *interp,
  int argc,
  char **argv
){
  int rc;
  sqlite3 *db;
  if( argc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
        " DB function-name", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  rc = sqlite3_create_collation(db, argv[2], SQLITE_UTF8, 0, 0);
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), TCL_STATIC);
  return TCL_OK;
}

/*
** Usage: sqlite3_get_autocommit DB
**
** Return true if the database DB is currently in auto-commit mode.
** Return false if not.
*/
static int SQLITE_TCLAPI get_autocommit(
  void * clientData,
  Tcl_Interp *interp,
  int argc,
  char **argv
){
  char zBuf[30];
  sqlite3 *db;
  if( argc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
        " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  sqlite3_snprintf(sizeof(zBuf), zBuf, "%d", sqlite3_get_autocommit(db));
  Tcl_AppendResult(interp, zBuf, 0);
  return TCL_OK;
}

/*
** Usage: sqlite3_busy_timeout DB MS
**
** Set the busy timeout.  This is more easily done using the timeout
** method of the TCL interface.  But we need a way to test the case
** where it returns SQLITE_MISUSE.
*/
static int SQLITE_TCLAPI test_busy_timeout(
  void * clientData,
  Tcl_Interp *interp,
  int argc,
  char **argv
){
  int rc, ms;
  sqlite3 *db;
  if( argc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"", argv[0], 
        " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, argv[1], &db) ) return TCL_ERROR;
  if( Tcl_GetInt(interp, argv[2], &ms) ) return TCL_ERROR;
  rc = sqlite3_busy_timeout(db, ms);
  Tcl_AppendResult(interp, sqlite3ErrName(rc), 0);
  return TCL_OK;
}

/*
** Usage:  tcl_variable_type VARIABLENAME
**
** Return the name of the internal representation for the
** value of the given variable.
*/
static int SQLITE_TCLAPI tcl_variable_type(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  Tcl_Obj *pVar;
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "VARIABLE");
    return TCL_ERROR;
  }
  pVar = Tcl_GetVar2Ex(interp, Tcl_GetString(objv[1]), 0, TCL_LEAVE_ERR_MSG);
  if( pVar==0 ) return TCL_ERROR;
  if( pVar->typePtr ){
    Tcl_SetObjResult(interp, Tcl_NewStringObj(pVar->typePtr->name, -1));
  }
  return TCL_OK;
}

#include <ctype.h>

/*
** Usage:  fpnum_compare STRING1 STRING2
**
** Compare two strings.  Return true if the strings are the same and
** false if they differ.
**
** For this comparison, the strings are analyzed as a sequenced of
** whitespace separated tokens.  The whitespace is ignored.  Only the
** tokens are compared.  Comparison rules:
**
**   A.  Tokens that are not floating-point numbers must match exactly.
**
**   B.  Floating point number must have exactly the same digits before
**       the decimal point.
**
**   C.  Digits must match after the decimal point up to 15 digits,
**       taking rounding into consideration.
**
**   D.  An exponent on a floating point of the form "e+NN" will
**       match "e+N" if NN==N.  Likewise for the negative exponent.
**
** This routine is used for comparing results that might involve floating
** point values.  Tcl9.0 and Tcl8.6 differ in the number of significant
** digits that they show, so there is no way to write a portable test result
** without this routine.
**
** This routine is only called after [string compare] fails, which is seldom,
** so performance is not a pressing concern.  Better to get the correct answer
** slowly.
*/
static int SQLITE_TCLAPI fpnum_compare(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  const unsigned char *zA;
  const unsigned char *zB;
  int i, j;
  int nDigit;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STRING1 STRING2");
    return TCL_ERROR;
  }
  zA = (const unsigned char*)Tcl_GetString(objv[1]);
  zB = (const unsigned char*)Tcl_GetString(objv[2]);
  i = j = 0;
  while( 1 ){
    /* Skip whitespace before and after tokens */
    while( isspace(zA[i]) ){ i++; }
    while( isspace(zB[j]) ){ j++; }

    if( zA[i]!=zB[j] ) break;                /* First character must match */
    if( zA[i]=='-' && isdigit(zA[i+1]) ){ i++; j++; }  /* Skip initial '-' */
    if( !isdigit(zA[i]) ){
      /* Not a number.  Must match exactly */
      while( !isspace(zA[i]) && zA[i] && zA[i]==zB[j] ){ i++; j++; }
      if( zA[i]!=zB[j] ) break;
      if( isspace(zA[i]) ) continue;
      break;
    }

    /* At this point we know we are dealing with a number zA[i] and zB[j]
    ** are both digits (leading "-" have been skipped).  See if they are
    ** the same number.  Start by matching digits before the decimal
    ** point, which must all be the same. */
    nDigit = 0;
    while( zA[i]==zB[j] && isdigit(zA[i]) ){ i++; j++; nDigit++; }
    if( zA[i]!=zB[j] ) break;
    if( zA[i]==0 ) break;
    if( zA[i]=='.' && zB[j]=='.' ){
      /* Count more matching digits after the decimal point */
      i++;
      j++;
      while( zA[i]==zB[j] && isdigit(zA[i]) ){ i++; j++; nDigit++; }
      if( zA[i]==0 ){
        while( zB[j]=='0' || (isdigit(zB[j]) && nDigit>=15) ){ j++; nDigit++; }
        break;
      }
      if( zB[j]==0 ){
        while( zA[i]=='0' || (isdigit(zA[i]) && nDigit>=15) ){ i++; nDigit++; }
        break;
      }
      if( isspace(zA[i]) && isspace(zB[j]) ) continue;

      if( isdigit(zA[i]) && isdigit(zB[j]) ){
        /* A and B are both digits, but different digits */
        if( zA[i]==zB[j]+1 && !isdigit(zA[i+1]) && isdigit(zB[j+1]) ){
          /* Is A a rounded up version of B? */
          j++;
          while( zB[j]=='9' ){ j++; nDigit++; }
          if( nDigit<14 && (!isdigit(zB[j]) || zB[j]<5) ) break;
          while( isdigit(zB[j]) ){ j++; }
          i++;
        }else if( zB[j]==zA[i]+1 && !isdigit(zB[j+1]) && isdigit(zA[i+1]) ){
          /* Is B a rounded up version of A? */
          i++;
          while( zA[i]=='9' ){ i++; nDigit++; }
          if( nDigit<14 && (!isdigit(zA[i]) || zA[i]<5) ) break;
          while( isdigit(zA[i]) ){ i++; }
          j++;
        }else{
          break;
        }
      }else if( !isdigit(zA[i]) && isdigit(zB[j]) ){
        while( zB[j]=='0' ){ j++; nDigit++; }
        if( nDigit<15 ) break;
        while( isdigit(zB[j]) ){ j++; }
      }else if( !isdigit(zB[j]) && isdigit(zA[i]) ){
        while( zA[i]=='0' ){ i++; nDigit++; }
        if( nDigit<15 ) break;
        while( isdigit(zA[i]) ){ i++; }
      }else{
        break;
      }
    }
    if( zA[i]=='e' && zB[j]=='e' ){
      i++;
      j++;
      if( (zA[i]=='+' || zA[i]=='-') && zB[j]==zA[i] ){  i++;  j++; }
      if( zA[i]!=zB[j] ){
        if( zA[i]=='0' && zA[i+1]==zB[j] ){ i++; }
        if( zB[j]=='0' && zB[j+1]==zA[i] ){ j++; }
      }
      while( zA[i]==zB[j] && isdigit(zA[i]) ){ i++; j++; }
      if( zA[i]!=zB[j] ) break;
      if( zA[i]==0 ) break;
      continue;
    }
  }
  while( isspace(zA[i]) ){ i++; }
  while( isspace(zB[j]) ){ j++; }  
  Tcl_SetObjResult(interp, Tcl_NewIntObj(zA[i]==0 && zB[j]==0));
  return TCL_OK;
}

/*
** Usage:  sqlite3_release_memory ?N?
**
** Attempt to release memory currently held but not actually required.
** The integer N is the number of bytes we are trying to release.  The 
** return value is the amount of memory actually released.
*/
static int SQLITE_TCLAPI test_release_memory(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#if defined(SQLITE_ENABLE_MEMORY_MANAGEMENT) && !defined(SQLITE_OMIT_DISKIO)
  int N;
  int amt;
  if( objc!=1 && objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "?N?");
    return TCL_ERROR;
  }
  if( objc==2 ){
    if( Tcl_GetIntFromObj(interp, objv[1], &N) ) return TCL_ERROR;
  }else{
    N = -1;
  }
  amt = sqlite3_release_memory(N);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(amt));
#endif
  return TCL_OK;
}


/*
** Usage:  sqlite3_db_release_memory DB
**
** Attempt to release memory currently held by database DB.  Return the
** result code (which in the current implementation is always zero).
*/
static int SQLITE_TCLAPI test_db_release_memory(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  int rc;
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  rc = sqlite3_db_release_memory(db);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(rc));
  return TCL_OK;
}

/*
** Usage:  sqlite3_db_cacheflush DB
**
** Attempt to flush any dirty pages to disk.
*/
static int SQLITE_TCLAPI test_db_cacheflush(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  int rc;
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  rc = sqlite3_db_cacheflush(db);
  if( rc ){
    Tcl_SetResult(interp, (char *)sqlite3ErrStr(rc), TCL_STATIC);
    return TCL_ERROR;
  }

  Tcl_ResetResult(interp);
  return TCL_OK;
}

/*
** Usage:  sqlite3_system_errno DB
**
** Return the low-level system errno value.
*/
static int SQLITE_TCLAPI test_system_errno(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  int iErrno;
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  iErrno = sqlite3_system_errno(db);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(iErrno));
  return TCL_OK;
}

/*
** Usage:  sqlite3_db_filename DB DBNAME
**
** Return the name of a file associated with a database.
*/
static int SQLITE_TCLAPI test_db_filename(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  const char *zDbName;
  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB DBNAME");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zDbName = Tcl_GetString(objv[2]);
  Tcl_AppendResult(interp, sqlite3_db_filename(db, zDbName), (void*)0);
  return TCL_OK;
}

/*
** Usage:  sqlite3_db_readonly DB DBNAME
**
** Return 1 or 0 if DBNAME is readonly or not.  Return -1 if DBNAME does
** not exist.
*/
static int SQLITE_TCLAPI test_db_readonly(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  const char *zDbName;
  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB DBNAME");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zDbName = Tcl_GetString(objv[2]);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(sqlite3_db_readonly(db, zDbName)));
  return TCL_OK;
}

/*
** Usage:  sqlite3_soft_heap_limit ?N?
**
** Query or set the soft heap limit for the current thread.  The
** limit is only changed if the N is present.  The previous limit
** is returned.
*/
static int SQLITE_TCLAPI test_soft_heap_limit(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_int64 amt;
  Tcl_WideInt N = -1;
  if( objc!=1 && objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "?N?");
    return TCL_ERROR;
  }
  if( objc==2 ){
    if( Tcl_GetWideIntFromObj(interp, objv[1], &N) ) return TCL_ERROR;
  }
  amt = sqlite3_soft_heap_limit64(N);
  Tcl_SetObjResult(interp, Tcl_NewWideIntObj(amt));
  return TCL_OK;
}

/*
** Usage:  sqlite3_hard_heap_limit ?N?
**
** Query or set the hard heap limit for the current thread.  The
** limit is only changed if the N is present.  The previous limit
** is returned.
*/
static int SQLITE_TCLAPI test_hard_heap_limit(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3_int64 amt;
  Tcl_WideInt N = -1;
  if( objc!=1 && objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "?N?");
    return TCL_ERROR;
  }
  if( objc==2 ){
    if( Tcl_GetWideIntFromObj(interp, objv[1], &N) ) return TCL_ERROR;
  }
  amt = sqlite3_hard_heap_limit64(N);
  Tcl_SetObjResult(interp, Tcl_NewWideIntObj(amt));
  return TCL_OK;
}

/*
** Usage:   sqlite3_thread_cleanup
**
** Call the sqlite3_thread_cleanup API.
*/
static int SQLITE_TCLAPI test_thread_cleanup(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifndef SQLITE_OMIT_DEPRECATED
  sqlite3_thread_cleanup();
#endif
  return TCL_OK;
}

/*
** Usage:   sqlite3_pager_refcounts  DB
**
** Return a list of numbers which are the PagerRefcount for all
** pagers on each database connection.
*/
static int SQLITE_TCLAPI test_pager_refcounts(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  int i;
  int v, *a;
  Tcl_Obj *pResult;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  pResult = Tcl_NewObj();
  for(i=0; i<db->nDb; i++){
    if( db->aDb[i].pBt==0 ){
      v = -1;
    }else{
      sqlite3_mutex_enter(db->mutex);
      a = sqlite3PagerStats(sqlite3BtreePager(db->aDb[i].pBt));
      v = a[0];
      sqlite3_mutex_leave(db->mutex);
    }
    Tcl_ListObjAppendElement(0, pResult, Tcl_NewIntObj(v));
  }
  Tcl_SetObjResult(interp, pResult);
  return TCL_OK;
}


/*
** tclcmd:   working_64bit_int
**
** Some TCL builds (ex: cygwin) do not support 64-bit integers.  This
** leads to a number of test failures.  The present command checks the
** TCL build to see whether or not it supports 64-bit integers.  It
** returns TRUE if it does and FALSE if not.
**
** This command is used to warn users that their TCL build is defective
** and that the errors they are seeing in the test scripts might be
** a result of their defective TCL rather than problems in SQLite.
*/
static int SQLITE_TCLAPI working_64bit_int(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  Tcl_Obj *pTestObj;
  int working = 0;

  pTestObj = Tcl_NewWideIntObj(1000000*(i64)1234567890);
  working = strcmp(Tcl_GetString(pTestObj), "1234567890000000")==0;
  Tcl_DecrRefCount(pTestObj);
  Tcl_SetObjResult(interp, Tcl_NewBooleanObj(working));
  return TCL_OK;
}


/*
** tclcmd:   vfs_unlink_test
**
** This TCL command unregisters the primary VFS and then registers
** it back again.  This is used to test the ability to register a
** VFS when none are previously registered, and the ability to 
** unregister the only available VFS.  Ticket #2738
*/
static int SQLITE_TCLAPI vfs_unlink_test(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int i;
  sqlite3_vfs *pMain;
  sqlite3_vfs *apVfs[20];
  sqlite3_vfs one, two;

  sqlite3_vfs_unregister(0);   /* Unregister of NULL is harmless */
  one.zName = "__one";
  two.zName = "__two";

  /* Calling sqlite3_vfs_register with 2nd argument of 0 does not
  ** change the default VFS
  */
  pMain = sqlite3_vfs_find(0);
  sqlite3_vfs_register(&one, 0);
  assert( pMain==0 || pMain==sqlite3_vfs_find(0) );
  sqlite3_vfs_register(&two, 0);
  assert( pMain==0 || pMain==sqlite3_vfs_find(0) );

  /* We can find a VFS by its name */
  assert( sqlite3_vfs_find("__one")==&one );
  assert( sqlite3_vfs_find("__two")==&two );

  /* Calling sqlite_vfs_register with non-zero second parameter changes the
  ** default VFS, even if the 1st parameter is an existing VFS that is
  ** previously registered as the non-default.
  */
  sqlite3_vfs_register(&one, 1);
  assert( sqlite3_vfs_find("__one")==&one );
  assert( sqlite3_vfs_find("__two")==&two );
  assert( sqlite3_vfs_find(0)==&one );
  sqlite3_vfs_register(&two, 1);
  assert( sqlite3_vfs_find("__one")==&one );
  assert( sqlite3_vfs_find("__two")==&two );
  assert( sqlite3_vfs_find(0)==&two );
  if( pMain ){
    sqlite3_vfs_register(pMain, 1);
    assert( sqlite3_vfs_find("__one")==&one );
    assert( sqlite3_vfs_find("__two")==&two );
    assert( sqlite3_vfs_find(0)==pMain );
  }
  
  /* Unlink the default VFS.  Repeat until there are no more VFSes
  ** registered.
  */
  for(i=0; i<sizeof(apVfs)/sizeof(apVfs[0]); i++){
    apVfs[i] = sqlite3_vfs_find(0);
    if( apVfs[i] ){
      assert( apVfs[i]==sqlite3_vfs_find(apVfs[i]->zName) );
      sqlite3_vfs_unregister(apVfs[i]);
      assert( 0==sqlite3_vfs_find(apVfs[i]->zName) );
    }
  }
  assert( 0==sqlite3_vfs_find(0) );
  
  /* Register the main VFS as non-default (will be made default, since
  ** it'll be the only one in existence).
  */
  sqlite3_vfs_register(pMain, 0);
  assert( sqlite3_vfs_find(0)==pMain );
  
  /* Un-register the main VFS again to restore an empty VFS list */
  sqlite3_vfs_unregister(pMain);
  assert( 0==sqlite3_vfs_find(0) );

  /* Relink all VFSes in reverse order. */  
  for(i=sizeof(apVfs)/sizeof(apVfs[0])-1; i>=0; i--){
    if( apVfs[i] ){
      sqlite3_vfs_register(apVfs[i], 1);
      assert( apVfs[i]==sqlite3_vfs_find(0) );
      assert( apVfs[i]==sqlite3_vfs_find(apVfs[i]->zName) );
    }
  }

  /* Unregister out sample VFSes. */
  sqlite3_vfs_unregister(&one);
  sqlite3_vfs_unregister(&two);

  /* Unregistering a VFS that is not currently registered is harmless */
  sqlite3_vfs_unregister(&one);
  sqlite3_vfs_unregister(&two);
  assert( sqlite3_vfs_find("__one")==0 );
  assert( sqlite3_vfs_find("__two")==0 );

  /* We should be left with the original default VFS back as the
  ** original */
  assert( sqlite3_vfs_find(0)==pMain );

  return TCL_OK;
}

/*
** tclcmd:   vfs_initfail_test
**
** This TCL command attempts to vfs_find and vfs_register when the
** sqlite3_initialize() interface is failing.  All calls should fail.
*/
static int SQLITE_TCLAPI vfs_initfail_test(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3_vfs one;
  one.zName = "__one";

  if( sqlite3_vfs_find(0) ) return TCL_ERROR;
  sqlite3_vfs_register(&one, 0);
  if( sqlite3_vfs_find(0) ) return TCL_ERROR;
  sqlite3_vfs_register(&one, 1);
  if( sqlite3_vfs_find(0) ) return TCL_ERROR;
  return TCL_OK;
}

/*
** Saved VFSes
*/
static sqlite3_vfs *apVfs[20];
static int nVfs = 0;

/*
** tclcmd:   vfs_unregister_all
**
** Unregister all VFSes.
*/
static int SQLITE_TCLAPI vfs_unregister_all(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int i;
  for(i=0; i<ArraySize(apVfs); i++){
    apVfs[i] = sqlite3_vfs_find(0);
    if( apVfs[i]==0 ) break;
    sqlite3_vfs_unregister(apVfs[i]);
  }
  nVfs = i;
  return TCL_OK;
}
/*
** tclcmd:   vfs_reregister_all
**
** Restore all VFSes that were removed using vfs_unregister_all. Taking
** care to put the linked list back together in the same order as it was
** in before vfs_unregister_all was invoked.
*/
static int SQLITE_TCLAPI vfs_reregister_all(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int i;
  for(i=nVfs-1; i>=0; i--){
    sqlite3_vfs_register(apVfs[i], 1);
  }
  return TCL_OK;
}


/*
** tclcmd:   file_control_test DB
**
** This TCL command runs the sqlite3_file_control interface and
** verifies correct operation of the same.
*/
static int SQLITE_TCLAPI file_control_test(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int iArg = 0;
  sqlite3 *db;
  int rc;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  rc = sqlite3_file_control(db, 0, 0, &iArg);
  assert( rc==SQLITE_NOTFOUND );
  rc = sqlite3_file_control(db, "notadatabase", SQLITE_FCNTL_LOCKSTATE, &iArg);
  assert( rc==SQLITE_ERROR );
  rc = sqlite3_file_control(db, "main", -1, &iArg);
  assert( rc==SQLITE_NOTFOUND );
  rc = sqlite3_file_control(db, "temp", -1, &iArg);
  assert( rc==SQLITE_NOTFOUND || rc==SQLITE_ERROR );

  return TCL_OK;
}


/*
** tclcmd:   file_control_lasterrno_test DB
**
** This TCL command runs the sqlite3_file_control interface and
** verifies correct operation of the SQLITE_LAST_ERRNO verb.
*/
static int SQLITE_TCLAPI file_control_lasterrno_test(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int iArg = 0;
  sqlite3 *db;
  int rc;

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
    return TCL_ERROR;
  }
  rc = sqlite3_file_control(db, NULL, SQLITE_LAST_ERRNO, &iArg);
  if( rc ){ 
    Tcl_SetObjResult(interp, Tcl_NewIntObj(rc)); 
    return TCL_ERROR; 
  }
  if( iArg!=0 ) {
    Tcl_AppendResult(interp, "Unexpected non-zero errno: ",
                     Tcl_GetStringFromObj(Tcl_NewIntObj(iArg), 0), " ", 0);
    return TCL_ERROR;
  }
  return TCL_OK;  
}

/*
** tclcmd:   file_control_data_version DB DBNAME
**
** This TCL command runs the sqlite3_file_control with the
** SQLITE_FCNTL_DATA_VERSION opcode, returning the result.
*/
static int SQLITE_TCLAPI file_control_data_version(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  unsigned int iVers;             /* data version */
  char *zDb;                      /* Db name ("main", "temp" etc.) */
  sqlite3 *db;                    /* Database handle */
  int rc;                         /* file_control() return code */
  char zBuf[100];

  if( objc!=3 && objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB [DBNAME]");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
   return TCL_ERROR;
  }
  zDb = objc==3 ? Tcl_GetString(objv[2]) : NULL;

  rc = sqlite3_file_control(db, zDb, SQLITE_FCNTL_DATA_VERSION, (void *)&iVers);
  if( rc ){
    Tcl_SetResult(interp, (char *)sqlite3ErrName(rc), TCL_STATIC);
    return TCL_ERROR;
  }else{
    sqlite3_snprintf(sizeof(zBuf),zBuf,"%u",iVers);
    Tcl_SetResult(interp, (char *)zBuf, TCL_VOLATILE);
    return TCL_OK;
  }
}

/*
** tclcmd:   file_control_chunksize_test DB DBNAME SIZE
**
** This TCL command runs the sqlite3_file_control interface and
** verifies correct operation of the SQLITE_GET_LOCKPROXYFILE and
** SQLITE_SET_LOCKPROXYFILE verbs.
*/
static int SQLITE_TCLAPI file_control_chunksize_test(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int nSize;                      /* New chunk size */
  char *zDb;                      /* Db name ("main", "temp" etc.) */
  sqlite3 *db;                    /* Database handle */
  int rc;                         /* file_control() return code */

  if( objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB DBNAME SIZE");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) 
   || Tcl_GetIntFromObj(interp, objv[3], &nSize)
  ){
   return TCL_ERROR;
  }
  zDb = Tcl_GetString(objv[2]);
  if( zDb[0]=='\0' ) zDb = NULL;

  rc = sqlite3_file_control(db, zDb, SQLITE_FCNTL_CHUNK_SIZE, (void *)&nSize);
  if( rc ){
    Tcl_SetResult(interp, (char *)sqlite3ErrName(rc), TCL_STATIC);
    return TCL_ERROR;
  }
  return TCL_OK;
}

/*
** tclcmd:   file_control_sizehint_test DB DBNAME SIZE
**
** This TCL command runs the sqlite3_file_control interface 
** with SQLITE_FCNTL_SIZE_HINT
*/
static int SQLITE_TCLAPI file_control_sizehint_test(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  Tcl_WideInt nSize;              /* Hinted size */
  char *zDb;                      /* Db name ("main", "temp" etc.) */
  sqlite3 *db;                    /* Database handle */
  int rc;                         /* file_control() return code */

  if( objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB DBNAME SIZE");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) 
   || Tcl_GetWideIntFromObj(interp, objv[3], &nSize)
  ){
   return TCL_ERROR;
  }
  zDb = Tcl_GetString(objv[2]);
  if( zDb[0]=='\0' ) zDb = NULL;

  rc = sqlite3_file_control(db, zDb, SQLITE_FCNTL_SIZE_HINT, (void *)&nSize);
  if( rc ){
    Tcl_SetResult(interp, (char *)sqlite3ErrName(rc), TCL_STATIC);
    return TCL_ERROR;
  }
  return TCL_OK;
}

/*
** tclcmd:   file_control_lockproxy_test DB PWD
**
** This TCL command runs the sqlite3_file_control interface and
** verifies correct operation of the SQLITE_GET_LOCKPROXYFILE and
** SQLITE_SET_LOCKPROXYFILE verbs.
*/
static int SQLITE_TCLAPI file_control_lockproxy_test(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  
  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
                     Tcl_GetStringFromObj(objv[0], 0), " DB PWD", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
   return TCL_ERROR;
  }
  
#if !defined(SQLITE_ENABLE_LOCKING_STYLE)
#  if defined(__APPLE__)
#    define SQLITE_ENABLE_LOCKING_STYLE 1
#  else
#    define SQLITE_ENABLE_LOCKING_STYLE 0
#  endif
#endif
#if SQLITE_ENABLE_LOCKING_STYLE && defined(__APPLE__)
  {
    char *testPath;
    int rc;
    Tcl_Size nPwd;
    const char *zPwd;
    char proxyPath[400];
    
    zPwd = Tcl_GetStringFromObj(objv[2], &nPwd);
    if( sizeof(proxyPath)<nPwd+20 ){
      Tcl_AppendResult(interp, "PWD too big", (void*)0);
      return TCL_ERROR;
    }
    sqlite3_snprintf(sizeof(proxyPath), proxyPath, "%s/test.proxy", zPwd);
    rc = sqlite3_file_control(db, NULL, SQLITE_SET_LOCKPROXYFILE, proxyPath);
    if( rc ){
      Tcl_SetObjResult(interp, Tcl_NewIntObj(rc)); 
      return TCL_ERROR;
    }
    rc = sqlite3_file_control(db, NULL, SQLITE_GET_LOCKPROXYFILE, &testPath);
    if( strncmp(proxyPath,testPath,11) ){
      Tcl_AppendResult(interp, "Lock proxy file did not match the "
                               "previously assigned value", 0);
      return TCL_ERROR;
    }
    if( rc ){
      Tcl_SetObjResult(interp, Tcl_NewIntObj(rc));
      return TCL_ERROR;
    }
    rc = sqlite3_file_control(db, NULL, SQLITE_SET_LOCKPROXYFILE, proxyPath);
    if( rc ){
      Tcl_SetObjResult(interp, Tcl_NewIntObj(rc));
      return TCL_ERROR;
    }
  }
#endif
  return TCL_OK;  
}

#if SQLITE_OS_WIN
/*
** tclcmd:   file_control_win32_av_retry DB  NRETRY  DELAY
**
** This TCL command runs the sqlite3_file_control interface with
** the SQLITE_FCNTL_WIN32_AV_RETRY opcode.
*/
static int SQLITE_TCLAPI file_control_win32_av_retry(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  int rc;
  int a[2];
  char z[100];

  if( objc!=4 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB NRETRY DELAY", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
    return TCL_ERROR;
  }
  if( Tcl_GetIntFromObj(interp, objv[2], &a[0]) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[3], &a[1]) ) return TCL_ERROR;
  rc = sqlite3_file_control(db, NULL, SQLITE_FCNTL_WIN32_AV_RETRY, (void*)a);
  sqlite3_snprintf(sizeof(z), z, "%d %d %d", rc, a[0], a[1]);
  Tcl_AppendResult(interp, z, (char*)0);
  return TCL_OK;  
}

/*
** tclcmd:   file_control_win32_get_handle DB
**
** This TCL command runs the sqlite3_file_control interface with
** the SQLITE_FCNTL_WIN32_GET_HANDLE opcode.
*/
static int file_control_win32_get_handle(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  int rc;
  HANDLE hFile = NULL;
  char z[100];

  if( objc!=2 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
    return TCL_ERROR;
  }
  rc = sqlite3_file_control(db, NULL, SQLITE_FCNTL_WIN32_GET_HANDLE,
                            (void*)&hFile);
  sqlite3_snprintf(sizeof(z), z, "%d %p", rc, (void*)hFile);
  Tcl_AppendResult(interp, z, (char*)0);
  return TCL_OK;
}

/*
** tclcmd:   file_control_win32_set_handle DB HANDLE
**
** This TCL command runs the sqlite3_file_control interface with
** the SQLITE_FCNTL_WIN32_SET_HANDLE opcode.
*/
static int SQLITE_TCLAPI file_control_win32_set_handle(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  int rc;
  HANDLE hFile = NULL;
  char z[100];

  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB HANDLE", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
    return TCL_ERROR;
  }
  if( getWin32Handle(interp, Tcl_GetString(objv[2]), &hFile) ){
    return TCL_ERROR;
  }
  rc = sqlite3_file_control(db, NULL, SQLITE_FCNTL_WIN32_SET_HANDLE,
                            (void*)&hFile);
  sqlite3_snprintf(sizeof(z), z, "%d %p", rc, (void*)hFile);
  Tcl_AppendResult(interp, z, (char*)0);
  return TCL_OK;  
}
#endif

/*
** tclcmd:   file_control_persist_wal DB PERSIST-FLAG
**
** This TCL command runs the sqlite3_file_control interface with
** the SQLITE_FCNTL_PERSIST_WAL opcode.
*/
static int SQLITE_TCLAPI file_control_persist_wal(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  int rc;
  int bPersist;
  char z[100];

  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB FLAG", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
    return TCL_ERROR;
  }
  if( Tcl_GetIntFromObj(interp, objv[2], &bPersist) ) return TCL_ERROR;
  rc = sqlite3_file_control(db, NULL, SQLITE_FCNTL_PERSIST_WAL, (void*)&bPersist);
  sqlite3_snprintf(sizeof(z), z, "%d %d", rc, bPersist);
  Tcl_AppendResult(interp, z, (char*)0);
  return TCL_OK;  
}

/*
** tclcmd:   file_control_powersafe_overwrite DB PSOW-FLAG
**
** This TCL command runs the sqlite3_file_control interface with
** the SQLITE_FCNTL_POWERSAFE_OVERWRITE opcode.
*/
static int SQLITE_TCLAPI file_control_powersafe_overwrite(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  int rc;
  int b;
  char z[100];

  if( objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB FLAG", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
    return TCL_ERROR;
  }
  if( Tcl_GetIntFromObj(interp, objv[2], &b) ) return TCL_ERROR;
  rc = sqlite3_file_control(db,NULL,SQLITE_FCNTL_POWERSAFE_OVERWRITE,(void*)&b);
  sqlite3_snprintf(sizeof(z), z, "%d %d", rc, b);
  Tcl_AppendResult(interp, z, (char*)0);
  return TCL_OK;  
}


/*
** tclcmd:   file_control_vfsname DB ?AUXDB?
**
** Return a string that describes the stack of VFSes.
*/
static int SQLITE_TCLAPI file_control_vfsname(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  const char *zDbName = "main";
  char *zVfsName = 0;

  if( objc!=2 && objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB ?AUXDB?", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
    return TCL_ERROR;
  }
  if( objc==3 ){
    zDbName = Tcl_GetString(objv[2]);
  }
  sqlite3_file_control(db, zDbName, SQLITE_FCNTL_VFSNAME,(void*)&zVfsName);
  Tcl_AppendResult(interp, zVfsName, (char*)0);
  sqlite3_free(zVfsName);
  return TCL_OK;  
}

/*
** tclcmd:   file_control_reservebytes DB N
*/
static int SQLITE_TCLAPI file_control_reservebytes(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  const char *zDbName = "main";
  int n = 0;
  int rc;

  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB N");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db)
   || Tcl_GetIntFromObj(interp, objv[2], &n) 
  ){
    return TCL_ERROR;
  }

  rc = sqlite3_file_control(db, zDbName, SQLITE_FCNTL_RESERVE_BYTES, (void*)&n);
  Tcl_SetObjResult(interp, Tcl_NewStringObj(sqlite3ErrName(rc), -1));
  return TCL_OK;  
}


/*
** tclcmd:   file_control_tempfilename DB ?AUXDB?
**
** Return a string that is a temporary filename
*/
static int SQLITE_TCLAPI file_control_tempfilename(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  const char *zDbName = "main";
  char *zTName = 0;

  if( objc!=2 && objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB ?AUXDB?", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
    return TCL_ERROR;
  }
  if( objc==3 ){
    zDbName = Tcl_GetString(objv[2]);
  }
  sqlite3_file_control(db, zDbName, SQLITE_FCNTL_TEMPFILENAME, (void*)&zTName);
  Tcl_AppendResult(interp, zTName, (char*)0);
  sqlite3_free(zTName);
  return TCL_OK;  
}

/*
** tclcmd:   file_control_external_reader DB ?AUXDB?
**
** Return a string that is a temporary filename
*/
static int SQLITE_TCLAPI file_control_external_reader(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  const char *zName = "main";
  int iRes = 0;
  int rc = SQLITE_OK;

  if( objc!=2 && objc!=3 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB ?AUXDB?", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
    return TCL_ERROR;
  }
  if( objc==3 ){
    zName = Tcl_GetString(objv[2]);
  }
  rc = sqlite3_file_control(db, zName, SQLITE_FCNTL_EXTERNAL_READER, &iRes);
  if( rc!=SQLITE_OK ){
    Tcl_SetResult(interp, (char *)t1ErrorName(rc), TCL_STATIC);
    return TCL_ERROR;
  }
  Tcl_SetObjResult(interp, Tcl_NewIntObj(iRes));
  return TCL_OK;
}


/*
** tclcmd:   sqlite3_vfs_list
**
**   Return a tcl list containing the names of all registered vfs's.
*/
static int SQLITE_TCLAPI vfs_list(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3_vfs *pVfs;
  Tcl_Obj *pRet = Tcl_NewObj();
  if( objc!=1 ){
    Tcl_WrongNumArgs(interp, 1, objv, "");
    return TCL_ERROR;
  }
  for(pVfs=sqlite3_vfs_find(0); pVfs; pVfs=pVfs->pNext){
    Tcl_ListObjAppendElement(interp, pRet, Tcl_NewStringObj(pVfs->zName, -1));
  }
  Tcl_SetObjResult(interp, pRet);
  return TCL_OK;  
}

/*
** tclcmd:   sqlite3_limit DB ID VALUE
**
** This TCL command runs the sqlite3_limit interface and
** verifies correct operation of the same.
*/
static int SQLITE_TCLAPI test_limit(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  int rc;
  static const struct {
     char *zName;
     int id;
  } aId[] = {
    { "SQLITE_LIMIT_LENGTH",              SQLITE_LIMIT_LENGTH               },
    { "SQLITE_LIMIT_SQL_LENGTH",          SQLITE_LIMIT_SQL_LENGTH           },
    { "SQLITE_LIMIT_COLUMN",              SQLITE_LIMIT_COLUMN               },
    { "SQLITE_LIMIT_EXPR_DEPTH",          SQLITE_LIMIT_EXPR_DEPTH           },
    { "SQLITE_LIMIT_COMPOUND_SELECT",     SQLITE_LIMIT_COMPOUND_SELECT      },
    { "SQLITE_LIMIT_VDBE_OP",             SQLITE_LIMIT_VDBE_OP              },
    { "SQLITE_LIMIT_FUNCTION_ARG",        SQLITE_LIMIT_FUNCTION_ARG         },
    { "SQLITE_LIMIT_ATTACHED",            SQLITE_LIMIT_ATTACHED             },
    { "SQLITE_LIMIT_LIKE_PATTERN_LENGTH", SQLITE_LIMIT_LIKE_PATTERN_LENGTH  },
    { "SQLITE_LIMIT_VARIABLE_NUMBER",     SQLITE_LIMIT_VARIABLE_NUMBER      },
    { "SQLITE_LIMIT_TRIGGER_DEPTH",       SQLITE_LIMIT_TRIGGER_DEPTH        },
    { "SQLITE_LIMIT_WORKER_THREADS",      SQLITE_LIMIT_WORKER_THREADS       },
    
    /* Out of range test cases */
    { "SQLITE_LIMIT_TOOSMALL",            -1,                               },
    { "SQLITE_LIMIT_TOOBIG",              SQLITE_LIMIT_WORKER_THREADS+1     },
  };
  int i, id = 0;
  int val;
  const char *zId;

  if( objc!=4 ){
    Tcl_AppendResult(interp, "wrong # args: should be \"",
        Tcl_GetStringFromObj(objv[0], 0), " DB ID VALUE", 0);
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zId = Tcl_GetString(objv[2]);
  for(i=0; i<sizeof(aId)/sizeof(aId[0]); i++){
    if( strcmp(zId, aId[i].zName)==0 ){
      id = aId[i].id;
      break;
    }
  }
  if( i>=sizeof(aId)/sizeof(aId[0]) ){
    Tcl_AppendResult(interp, "unknown limit type: ", zId, (char*)0);
    return TCL_ERROR;
  }
  if( Tcl_GetIntFromObj(interp, objv[3], &val) ) return TCL_ERROR;
  rc = sqlite3_limit(db, id, val);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(rc));
  return TCL_OK;  
}

/*
** tclcmd:  save_prng_state
**
** Save the state of the pseudo-random number generator.
** At the same time, verify that sqlite3_test_control works even when
** called with an out-of-range opcode.
*/
static int SQLITE_TCLAPI save_prng_state(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int rc = sqlite3_test_control(9999);
  assert( rc==0 );
  rc = sqlite3_test_control(-1);
  assert( rc==0 );
  sqlite3_test_control(SQLITE_TESTCTRL_PRNG_SAVE);
  return TCL_OK;
}
/*
** tclcmd:  restore_prng_state
*/
static int SQLITE_TCLAPI restore_prng_state(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3_test_control(SQLITE_TESTCTRL_PRNG_RESTORE);
  return TCL_OK;
}
/*
** tclcmd:  reset_prng_state
*/
static int SQLITE_TCLAPI reset_prng_state(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3_randomness(0,0);
  return TCL_OK;
}
/*
** tclcmd:  prng_seed INT ?DB?
**
** Set up the SQLITE_TESTCTRL_PRNG_SEED pragma with parameter INT and DB.
** INT is an integer.  DB is a database connection, or a NULL pointer if
** omitted.
**
** When INT!=0 and DB!=0, set the PRNG seed to the value of the schema
** cookie for DB, or to INT if the schema cookie happens to be zero.
**
** When INT!=0 and DB==0, set the PRNG seed to just INT.
**
** If INT==0 and DB==0 then use the default procedure of calling the
** xRandomness method on the default VFS to get the PRNG seed.
*/
static int SQLITE_TCLAPI prng_seed(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int i = 0;
  sqlite3 *db = 0;
  if( objc!=2 && objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "SEED ?DB?");
    return TCL_ERROR;
  }
  if( Tcl_GetIntFromObj(interp,objv[1],&i) ) return TCL_ERROR;
  if( objc==3 && getDbPointer(interp, Tcl_GetString(objv[2]), &db) ){
    return TCL_ERROR;
  }
  sqlite3_test_control(SQLITE_TESTCTRL_PRNG_SEED, i, db);
  return TCL_OK;
}

/*
** tclcmd:  extra_schema_checks BOOLEAN
**
** Enable or disable schema checks when parsing the sqlite_schema file.
** This is always enabled in production, but it is sometimes useful to
** disable the checks in order to make some internal error states reachable
** for testing.
*/
static int SQLITE_TCLAPI extra_schema_checks(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int i = 0;
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "BOOLEAN");
    return TCL_ERROR;
  }
  if( Tcl_GetBooleanFromObj(interp,objv[1],&i) ) return TCL_ERROR;
  sqlite3_test_control(SQLITE_TESTCTRL_EXTRA_SCHEMA_CHECKS, i);
  return TCL_OK;
}

/*
** tclcmd:  database_may_be_corrupt
**
** Indicate that database files might be corrupt. In other words, set the normal
** state of operation.
*/
static int SQLITE_TCLAPI database_may_be_corrupt(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3_test_control(SQLITE_TESTCTRL_NEVER_CORRUPT, 0);
  return TCL_OK;
}
/*
** tclcmd:  database_never_corrupt
**
** Indicate that database files are always well-formed. This enables
** extra assert() statements that test conditions that are always true
** for well-formed databases.
*/
static int SQLITE_TCLAPI database_never_corrupt(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3_test_control(SQLITE_TESTCTRL_NEVER_CORRUPT, 1);
  return TCL_OK;
}

/*
** tclcmd:  pcache_stats
*/
static int SQLITE_TCLAPI test_pcache_stats(
  ClientData clientData, /* Pointer to sqlite3_enable_XXX function */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  int nMin;
  int nMax;
  int nCurrent;
  int nRecyclable;
  Tcl_Obj *pRet;

  sqlite3PcacheStats(&nCurrent, &nMax, &nMin, &nRecyclable);

  pRet = Tcl_NewObj();
  Tcl_ListObjAppendElement(interp, pRet, Tcl_NewStringObj("current", -1));
  Tcl_ListObjAppendElement(interp, pRet, Tcl_NewIntObj(nCurrent));
  Tcl_ListObjAppendElement(interp, pRet, Tcl_NewStringObj("max", -1));
  Tcl_ListObjAppendElement(interp, pRet, Tcl_NewIntObj(nMax));
  Tcl_ListObjAppendElement(interp, pRet, Tcl_NewStringObj("min", -1));
  Tcl_ListObjAppendElement(interp, pRet, Tcl_NewIntObj(nMin));
  Tcl_ListObjAppendElement(interp, pRet, Tcl_NewStringObj("recyclable", -1));
  Tcl_ListObjAppendElement(interp, pRet, Tcl_NewIntObj(nRecyclable));

  Tcl_SetObjResult(interp, pRet);

  return TCL_OK;
}

#ifdef SQLITE_ENABLE_UNLOCK_NOTIFY
static void test_unlock_notify_cb(void **aArg, int nArg){
  int ii;
  for(ii=0; ii<nArg; ii++){
    Tcl_EvalEx((Tcl_Interp *)aArg[ii], "unlock_notify", -1, TCL_EVAL_GLOBAL);
  }
}
#endif /* SQLITE_ENABLE_UNLOCK_NOTIFY */

/*
** tclcmd:  sqlite3_unlock_notify db
*/
#ifdef SQLITE_ENABLE_UNLOCK_NOTIFY
static int SQLITE_TCLAPI test_unlock_notify(
  ClientData clientData, /* Unused */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  int rc;

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB");
    return TCL_ERROR;
  }

  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
    return TCL_ERROR;
  }
  rc = sqlite3_unlock_notify(db, test_unlock_notify_cb, (void *)interp);
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), TCL_STATIC);
  return TCL_OK;
}
#endif

/*
** tclcmd:  sqlite3_wal_checkpoint db ?NAME?
*/
static int SQLITE_TCLAPI test_wal_checkpoint(
  ClientData clientData, /* Unused */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  char *zDb = 0;
  sqlite3 *db;
  int rc;

  if( objc!=3 && objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB ?NAME?");
    return TCL_ERROR;
  }

  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ){
    return TCL_ERROR;
  }
  if( objc==3 ){
    zDb = Tcl_GetString(objv[2]);
  }
  rc = sqlite3_wal_checkpoint(db, zDb);
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), TCL_STATIC);
  return TCL_OK;
}

/*
** tclcmd:  sqlite3_wal_checkpoint_v2 db MODE ?NAME?
**
** This command calls the wal_checkpoint_v2() function with the specified
** mode argument (passive, full or restart). If present, the database name
** NAME is passed as the second argument to wal_checkpoint_v2(). If it the
** NAME argument is not present, a NULL pointer is passed instead.
**
** If wal_checkpoint_v2() returns any value other than SQLITE_BUSY or
** SQLITE_OK, then this command returns TCL_ERROR. The Tcl result is set
** to the error message obtained from sqlite3_errmsg().
**
** Otherwise, this command returns a list of three integers. The first integer
** is 1 if SQLITE_BUSY was returned, or 0 otherwise. The following two integers
** are the values returned via the output parameters by wal_checkpoint_v2() -
** the number of frames in the log and the number of frames in the log
** that have been checkpointed.
*/
static int SQLITE_TCLAPI test_wal_checkpoint_v2(
  ClientData clientData, /* Unused */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  char *zDb = 0;
  sqlite3 *db;
  int rc;

  int eMode;
  int nLog = -555;
  int nCkpt = -555;
  Tcl_Obj *pRet;

  const char * aMode[] = { "passive", "full", "restart", "truncate", 0 };
  assert( SQLITE_CHECKPOINT_PASSIVE==0 );
  assert( SQLITE_CHECKPOINT_FULL==1 );
  assert( SQLITE_CHECKPOINT_RESTART==2 );
  assert( SQLITE_CHECKPOINT_TRUNCATE==3 );

  if( objc!=3 && objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB MODE ?NAME?");
    return TCL_ERROR;
  }

  if( objc==4 ){
    zDb = Tcl_GetString(objv[3]);
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) || (
      TCL_OK!=Tcl_GetIntFromObj(0, objv[2], &eMode)
   && TCL_OK!=Tcl_GetIndexFromObj(interp, objv[2], aMode, "mode", 0, &eMode) 
  )){
    return TCL_ERROR;
  }

  rc = sqlite3_wal_checkpoint_v2(db, zDb, eMode, &nLog, &nCkpt);
  if( rc!=SQLITE_OK && rc!=SQLITE_BUSY ){
    const char *zErrCode = sqlite3ErrName(rc);
    Tcl_ResetResult(interp);
    Tcl_AppendResult(interp, zErrCode, " - ", (char *)sqlite3_errmsg(db), 0);
    return TCL_ERROR;
  }

  pRet = Tcl_NewObj();
  Tcl_ListObjAppendElement(interp, pRet, Tcl_NewIntObj(rc==SQLITE_BUSY?1:0));
  Tcl_ListObjAppendElement(interp, pRet, Tcl_NewIntObj(nLog));
  Tcl_ListObjAppendElement(interp, pRet, Tcl_NewIntObj(nCkpt));
  Tcl_SetObjResult(interp, pRet);

  return TCL_OK;
}

/*
** tclcmd:  sqlite3_wal_autocheckpoint db VALUE
*/
static int SQLITE_TCLAPI test_wal_autocheckpoint(
  ClientData clientData, /* Unused */
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  sqlite3 *db;
  int rc;
  int iVal;


  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB VALUE");
    return TCL_ERROR;
  }

  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) 
   || Tcl_GetIntFromObj(0, objv[2], &iVal)
  ){
    return TCL_ERROR;
  }

  rc = sqlite3_wal_autocheckpoint(db, iVal);
  Tcl_ResetResult(interp);
  if( rc!=SQLITE_OK ){
    const char *zErrCode = sqlite3ErrName(rc);
    Tcl_SetObjResult(interp, Tcl_NewStringObj(zErrCode, -1));
    return TCL_ERROR;
  }

  return TCL_OK;
}


/*
** tclcmd:  test_sqlite3_log ?SCRIPT?
**
** Caution:  If you register a log callback, you must deregister it (by
** invoking test_sqlite3_log with no arguments) prior to closing the
** Tcl interpreter or else a memory error will occur.
*/
static struct LogCallback {
  Tcl_Interp *pInterp;
  Tcl_Obj *pObj;
} logcallback = {0, 0};
static void xLogcallback(void *unused, int err, const char *zMsg){
  Tcl_Obj *pNew = Tcl_DuplicateObj(logcallback.pObj);
  Tcl_IncrRefCount(pNew);
  Tcl_ListObjAppendElement(
      0, pNew, Tcl_NewStringObj(sqlite3ErrName(err), -1)
  );
  Tcl_ListObjAppendElement(0, pNew, Tcl_NewStringObj(zMsg, -1));
  Tcl_EvalObjEx(logcallback.pInterp, pNew, TCL_EVAL_GLOBAL|TCL_EVAL_DIRECT);
  Tcl_DecrRefCount(pNew);
}
static int SQLITE_TCLAPI test_sqlite3_log(
  ClientData clientData,
  Tcl_Interp *interp,    /* The TCL interpreter that invoked this command */
  int objc,              /* Number of arguments */
  Tcl_Obj *CONST objv[]  /* Command arguments */
){
  if( objc>2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "SCRIPT");
    return TCL_ERROR;
  }
  if( logcallback.pObj ){
    Tcl_DecrRefCount(logcallback.pObj);
    logcallback.pObj = 0;
    logcallback.pInterp = 0;
    sqlite3_config(SQLITE_CONFIG_LOG, (void*)0, (void*)0);
  }
  if( objc>1 && Tcl_GetString(objv[1])[0]!=0 ){
    logcallback.pObj = objv[1];
    Tcl_IncrRefCount(logcallback.pObj);
    logcallback.pInterp = interp;
    sqlite3_config(SQLITE_CONFIG_LOG, xLogcallback, (void*)0);
  }
  return TCL_OK;
}

/*
**     tcl_objproc COMMANDNAME ARGS...
**
** Run a TCL command using its objProc interface.  Throw an error if
** the command has no objProc interface.
*/
static int SQLITE_TCLAPI runAsObjProc(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  Tcl_CmdInfo cmdInfo;
  if( objc<2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "COMMAND ...");
    return TCL_ERROR;
  }
  if( !Tcl_GetCommandInfo(interp, Tcl_GetString(objv[1]), &cmdInfo) ){
    Tcl_AppendResult(interp, "command not found: ",
           Tcl_GetString(objv[1]), (char*)0);
    return TCL_ERROR;
  }
  if( cmdInfo.objProc==0 ){
    Tcl_AppendResult(interp, "command has no objProc: ",
           Tcl_GetString(objv[1]), (char*)0);
    return TCL_ERROR;
  }
  return cmdInfo.objProc(cmdInfo.objClientData, interp, objc-1, objv+1);
}

#ifndef SQLITE_OMIT_EXPLAIN
/*
** WARNING: The following function, printExplainQueryPlan() is an exact
** copy of example code from eqp.in (eqp.html). If this code is modified,
** then the documentation copy needs to be modified as well.
*/
/*
** Argument pStmt is a prepared SQL statement. This function compiles
** an EXPLAIN QUERY PLAN command to report on the prepared statement,
** and prints the report to stdout using printf().
*/
int printExplainQueryPlan(sqlite3_stmt *pStmt){
  const char *zSql;               /* Input SQL */
  char *zExplain;                 /* SQL with EXPLAIN QUERY PLAN prepended */
  sqlite3_stmt *pExplain;         /* Compiled EXPLAIN QUERY PLAN command */
  int rc;                         /* Return code from sqlite3_prepare_v2() */

  zSql = sqlite3_sql(pStmt);
  if( zSql==0 ) return SQLITE_ERROR;

  zExplain = sqlite3_mprintf("EXPLAIN QUERY PLAN %s", zSql);
  if( zExplain==0 ) return SQLITE_NOMEM;

  rc = sqlite3_prepare_v2(sqlite3_db_handle(pStmt), zExplain, -1, &pExplain, 0);
  sqlite3_free(zExplain);
  if( rc!=SQLITE_OK ) return rc;

  while( SQLITE_ROW==sqlite3_step(pExplain) ){
    int iSelectid = sqlite3_column_int(pExplain, 0);
    int iOrder = sqlite3_column_int(pExplain, 1);
    int iFrom = sqlite3_column_int(pExplain, 2);
    const char *zDetail = (const char *)sqlite3_column_text(pExplain, 3);

    printf("%d %d %d %s\n", iSelectid, iOrder, iFrom, zDetail);
  }

  return sqlite3_finalize(pExplain);
}

static int SQLITE_TCLAPI test_print_eqp(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int rc;
  sqlite3_stmt *pStmt;

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "STMT");
    return TCL_ERROR;
  }
  if( getStmtPointer(interp, Tcl_GetString(objv[1]), &pStmt) ) return TCL_ERROR;
  rc = printExplainQueryPlan(pStmt);
  /* This is needed on Windows so that a test case using this 
  ** function can open a read pipe and get the output of
  ** printExplainQueryPlan() immediately.
  */
  fflush(stdout);
  Tcl_SetResult(interp, (char *)t1ErrorName(rc), 0);
  return TCL_OK;
}
#endif /* SQLITE_OMIT_EXPLAIN */

#include <time.h>
/*
** This is an alternative localtime_r() implementation used for testing
** the 'localtime' and 'utc' modifiers of date-time functions.  Because
** the OS-supplied localtime_r() is locale-dependent, this alternative is
** provided as a stable test platform.
**
** Operation:
**
**     (1)  Localtime is 30 minutes earlier than (west of) UTC on
**          even days (counting from 1970-01-01)
**
**     (2)  Localtime is 30 minutes later than (east of) UTC on odd days.
**
**     (3)  The function fails for the specific date/time value
**          of 2000-05-29 14:16:00 in order to test the ability of
**          SQLite to deal with localtime_r() failures.
*/
static int testLocaltime(const void *aliasT, void *aliasTM){
  const time_t t = *(const time_t*)aliasT;
  struct tm *pTm = (struct tm *)aliasTM;
  time_t altT;
  sqlite3_int64 iJD;
  int Z, A, B, C, D, E, X1, S;

  if( (t/86400) & 1 ){
    altT = t + 1800;  /* 30 minutes later on odd days */
  }else{
    altT = t - 1800;  /* 30 minutes earlier on even days */
  }
  iJD = (sqlite3_int64)(altT + 210866760000);
  Z = (int)((iJD + 43200)/86400);
  A = (int)((Z - 1867216.25)/36524.25);
  A = Z + 1 + A - (A/4);
  B = A + 1524;
  C = (int)((B - 122.1)/365.25);
  D = (36525*(C&32767))/100;
  E = (int)((B-D)/30.6001);
  X1 = (int)(30.6001*E);
  pTm->tm_mday = B - D - X1;
  pTm->tm_mon = E<14 ? E-2 : E-14;
  pTm->tm_year = (pTm->tm_mon>1 ? C - 4716 : C - 4715) - 1900;
  S = (int)((iJD + 43200)%86400);
  pTm->tm_hour = S/3600;
  pTm->tm_min = (S/60)%60;
  pTm->tm_sec = S % 60;
  return t==959609760; /* Special case: 2000-05-29 14:16:00 fails */
}

/*
** TCLCMD:  strftime FORMAT UNIXTIMESTAMP
**
** Access to the C-library strftime() routine, so that its results
** can be compared against SQLite's internal strftime() SQL function
** implementation.
*/
static int SQLITE_TCLAPI strftime_cmd(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  Tcl_WideInt ts;
  time_t t;
  struct tm *pTm;
  const char *zFmt;
  size_t n;
  char zBuf[1000];
  if( objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "FORMAT UNIXTIMESTAMP");
    return TCL_ERROR;
  }
  if( Tcl_GetWideIntFromObj(interp,  objv[2], &ts) ) return TCL_ERROR;
  zFmt = Tcl_GetString(objv[1]);
  t = (time_t)ts;
  pTm = gmtime(&t);
  n = strftime(zBuf, sizeof(zBuf)-1, zFmt, pTm);
  if( n>=0 && n<sizeof(zBuf) ){
    zBuf[n] = 0;
    Tcl_SetResult(interp, zBuf, TCL_VOLATILE);
  }
  return TCL_OK;
}

/*
** .treetrace N
*/
static int SQLITE_TCLAPI test_treetrace(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  unsigned int v = 0;
  if( objc>=2 ){
    if( Tcl_GetIntFromObj(interp, objv[1], (int*)&v)==TCL_OK ){
      sqlite3_test_control(SQLITE_TESTCTRL_TRACEFLAGS, 1, &v);
    }
  }
  sqlite3_test_control(SQLITE_TESTCTRL_TRACEFLAGS, 0, &v);
  Tcl_SetObjResult(interp, Tcl_NewIntObj((int)v));
  return TCL_OK;
}

/*
** sqlite3_test_control VERB ARGS...
*/
static int SQLITE_TCLAPI test_test_control(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  struct Verb {
    const char *zName;
    int i;
  } aVerb[] = {
    { "SQLITE_TESTCTRL_LOCALTIME_FAULT",    SQLITE_TESTCTRL_LOCALTIME_FAULT }, 
    { "SQLITE_TESTCTRL_SORTER_MMAP",        SQLITE_TESTCTRL_SORTER_MMAP     }, 
    { "SQLITE_TESTCTRL_IMPOSTER",           SQLITE_TESTCTRL_IMPOSTER        },
    { "SQLITE_TESTCTRL_INTERNAL_FUNCTIONS", SQLITE_TESTCTRL_INTERNAL_FUNCTIONS},
    { "SQLITE_TESTCTRL_FK_NO_ACTION",       SQLITE_TESTCTRL_FK_NO_ACTION},
    { 0, 0 }
  };
  int iVerb;
  int iFlag;
  int rc;

  if( objc<2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "VERB ARGS...");
    return TCL_ERROR;
  }

  rc = Tcl_GetIndexFromObjStruct(
      interp, objv[1], aVerb, sizeof(aVerb[0]), "VERB", 0, &iVerb
  );
  if( rc!=TCL_OK ) return rc;

  iFlag = aVerb[iVerb].i;
  switch( iFlag ){
    case SQLITE_TESTCTRL_INTERNAL_FUNCTIONS: {
      sqlite3 *db = 0;
      if( objc!=3 ){
        Tcl_WrongNumArgs(interp, 2, objv, "DB");
        return TCL_ERROR;
      }
      if( getDbPointer(interp, Tcl_GetString(objv[2]), &db) ) return TCL_ERROR;
      sqlite3_test_control(SQLITE_TESTCTRL_INTERNAL_FUNCTIONS, db);
      break;
    }
    case SQLITE_TESTCTRL_LOCALTIME_FAULT: {
      int val;
      if( objc!=3 ){
        Tcl_WrongNumArgs(interp, 2, objv, "0|1|2");
        return TCL_ERROR;
      }
      if( Tcl_GetIntFromObj(interp, objv[2], &val) ) return TCL_ERROR;
      sqlite3_test_control(iFlag, val, testLocaltime);
      break;
    }

    case SQLITE_TESTCTRL_FK_NO_ACTION: {
      int val = 0;
      sqlite3 *db = 0;
      if( objc!=4 ){
        Tcl_WrongNumArgs(interp, 2, objv, "DB BOOLEAN");
        return TCL_ERROR;
      }
      if( getDbPointer(interp, Tcl_GetString(objv[2]), &db) ) return TCL_ERROR;
      if( Tcl_GetBooleanFromObj(interp, objv[3], &val) ) return TCL_ERROR;

      sqlite3_test_control(SQLITE_TESTCTRL_FK_NO_ACTION, db, val);
      break;
    }

    case SQLITE_TESTCTRL_SORTER_MMAP: {
      int val;
      sqlite3 *db;
      if( objc!=4 ){
        Tcl_WrongNumArgs(interp, 2, objv, "DB LIMIT");
        return TCL_ERROR;
      }
      if( getDbPointer(interp, Tcl_GetString(objv[2]), &db) ) return TCL_ERROR;
      if( Tcl_GetIntFromObj(interp, objv[3], &val) ) return TCL_ERROR;
      sqlite3_test_control(SQLITE_TESTCTRL_SORTER_MMAP, db, val);
      break;
    }

    case SQLITE_TESTCTRL_IMPOSTER: {
      int onOff, tnum;
      const char *zDbName;
      sqlite3 *db;
      if( objc!=6 ){
        Tcl_WrongNumArgs(interp, 2, objv, "DB dbName onOff tnum");
        return TCL_ERROR;
      }
      if( getDbPointer(interp, Tcl_GetString(objv[2]), &db) ) return TCL_ERROR;
      zDbName = Tcl_GetString(objv[3]);
      if( Tcl_GetIntFromObj(interp, objv[4], &onOff) ) return TCL_ERROR;
      if( Tcl_GetIntFromObj(interp, objv[5], &tnum) ) return TCL_ERROR;
      sqlite3_test_control(SQLITE_TESTCTRL_IMPOSTER, db, zDbName, onOff, tnum);
      break;
    }
  }

  Tcl_ResetResult(interp);
  return TCL_OK;
}

#if SQLITE_OS_UNIX
#include <sys/time.h>
#include <sys/resource.h>

static int SQLITE_TCLAPI test_getrusage(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  char buf[1024];
  struct rusage r;
  memset(&r, 0, sizeof(r));
  getrusage(RUSAGE_SELF, &r);

  sqlite3_snprintf(sizeof(buf), buf,
    "ru_utime=%d.%06d ru_stime=%d.%06d ru_minflt=%d ru_majflt=%d", 
    (int)r.ru_utime.tv_sec, (int)r.ru_utime.tv_usec, 
    (int)r.ru_stime.tv_sec, (int)r.ru_stime.tv_usec, 
    (int)r.ru_minflt, (int)r.ru_majflt
  );
  Tcl_SetObjResult(interp, Tcl_NewStringObj(buf, -1));
  return TCL_OK;
}
#endif

#if SQLITE_OS_WIN
/*
** Information passed from the main thread into the windows file locker
** background thread.
*/
struct win32FileLocker {
  char *evName;       /* Name of event to signal thread startup */
  HANDLE h;           /* Handle of the file to be locked */
  int delay1;         /* Delay before locking */
  int delay2;         /* Delay before unlocking */
  int ok;             /* Finished ok */
  int err;            /* True if an error occurs */
};
#endif


#if SQLITE_OS_WIN
#include <process.h>
/*
** The background thread that does file locking.
*/
static void SQLITE_CDECL win32_file_locker(void *pAppData){
  struct win32FileLocker *p = (struct win32FileLocker*)pAppData;
  if( p->evName ){
    HANDLE ev = OpenEvent(EVENT_MODIFY_STATE, FALSE, p->evName);
    if ( ev ){
      SetEvent(ev);
      CloseHandle(ev);
    }
  }
  if( p->delay1 ) Sleep(p->delay1);
  if( LockFile(p->h, 0, 0, 100000000, 0) ){
    Sleep(p->delay2);
    UnlockFile(p->h, 0, 0, 100000000, 0);
    p->ok = 1;
  }else{
    p->err = 1;
  }
  CloseHandle(p->h);
  p->h = 0;
  p->delay1 = 0;
  p->delay2 = 0;
}
#endif

#if SQLITE_OS_WIN
/*
**      lock_win32_file FILENAME DELAY1 DELAY2
**
** Get an exclusive manditory lock on file for DELAY2 milliseconds.
** Wait DELAY1 milliseconds before acquiring the lock.
*/
static int SQLITE_TCLAPI win32_file_lock(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  static struct win32FileLocker x = { "win32_file_lock", 0, 0, 0, 0, 0 };
  const char *zFilename;
  char zBuf[200];
  int retry = 0;
  HANDLE ev;
  DWORD wResult;
  
  if( objc!=4 && objc!=1 ){
    Tcl_WrongNumArgs(interp, 1, objv, "FILENAME DELAY1 DELAY2");
    return TCL_ERROR;
  }
  if( objc==1 ){
    sqlite3_snprintf(sizeof(zBuf), zBuf, "%d %d %d %d %d",
                     x.ok, x.err, x.delay1, x.delay2, x.h);
    Tcl_AppendResult(interp, zBuf, (char*)0);
    return TCL_OK;
  }
  while( x.h && retry<30 ){
    retry++;
    Sleep(100);
  }
  if( x.h ){
    Tcl_AppendResult(interp, "busy", (char*)0);
    return TCL_ERROR;
  }
  if( Tcl_GetIntFromObj(interp, objv[2], &x.delay1) ) return TCL_ERROR;
  if( Tcl_GetIntFromObj(interp, objv[3], &x.delay2) ) return TCL_ERROR;
  zFilename = Tcl_GetString(objv[1]);
  x.h = CreateFile(zFilename, GENERIC_READ|GENERIC_WRITE,
              FILE_SHARE_READ|FILE_SHARE_WRITE, 0, OPEN_ALWAYS,
              FILE_ATTRIBUTE_NORMAL, 0);
  if( !x.h ){
    Tcl_AppendResult(interp, "cannot open file: ", zFilename, (char*)0);
    return TCL_ERROR;
  }
  ev = CreateEvent(NULL, TRUE, FALSE, x.evName);
  if ( !ev ){
    Tcl_AppendResult(interp, "cannot create event: ", x.evName, (char*)0);
    return TCL_ERROR;
  }
  _beginthread(win32_file_locker, 0, (void*)&x);
  Sleep(0);
  if ( (wResult = WaitForSingleObject(ev, 10000))!=WAIT_OBJECT_0 ){
    sqlite3_snprintf(sizeof(zBuf), zBuf, "0x%x", wResult);
    Tcl_AppendResult(interp, "wait failed: ", zBuf, (char*)0);
    CloseHandle(ev);
    return TCL_ERROR;
  }
  CloseHandle(ev);
  return TCL_OK;
}
#endif


/*
**      optimization_control DB OPT BOOLEAN
**
** Enable or disable query optimizations using the sqlite3_test_control()
** interface.  Disable if BOOLEAN is false and enable if BOOLEAN is true.
** OPT is the name of the optimization to be disabled.  OPT can also be a
** list or optimizations names, in which case all optimizations named are
** enabled or disabled.
**
** Each invocation of this control overrides all prior invocations.  The
** changes are not cumulative.
*/
static int SQLITE_TCLAPI optimization_control(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int i;
  sqlite3 *db;
  const char *zOpt;
  int onoff;
  int mask = 0;
  int cnt = 0;
  static const struct {
    const char *zOptName;
    int mask;
  } aOpt[] = {
    { "all",                 SQLITE_AllOpts        },
    { "none",                0                     },
    { "query-flattener",     SQLITE_QueryFlattener },
    { "groupby-order",       SQLITE_GroupByOrder   },
    { "factor-constants",    SQLITE_FactorOutConst },
    { "distinct-opt",        SQLITE_DistinctOpt    },
    { "cover-idx-scan",      SQLITE_CoverIdxScan   },
    { "order-by-idx-join",   SQLITE_OrderByIdxJoin },
    { "order-by-subquery",   SQLITE_OrderBySubq    },
    { "transitive",          SQLITE_Transitive     },
    { "omit-noop-join",      SQLITE_OmitNoopJoin   },
    { "stat4",               SQLITE_Stat4          },
    { "skip-scan",           SQLITE_SkipScan       },
    { "push-down",           SQLITE_PushDown       },
    { "balanced-merge",      SQLITE_BalancedMerge  },
    { "propagate-const",     SQLITE_PropagateConst },
    { "one-pass",            SQLITE_OnePass        },
  };

  if( objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB OPT BOOLEAN");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  if( Tcl_GetBooleanFromObj(interp, objv[3], &onoff) ) return TCL_ERROR;
  zOpt = Tcl_GetString(objv[2]);
  for(i=0; i<sizeof(aOpt)/sizeof(aOpt[0]); i++){
    if( strstr(zOpt, aOpt[i].zOptName)!=0 ){
      mask |= aOpt[i].mask;
      cnt++;
    }
  }
  if( onoff ) mask = ~mask;
  if( cnt==0 ){
    Tcl_AppendResult(interp, "unknown optimization - should be one of:",
                     (char*)0);
    for(i=0; i<sizeof(aOpt)/sizeof(aOpt[0]); i++){
      Tcl_AppendResult(interp, " ", aOpt[i].zOptName, (char*)0);
    }
    return TCL_ERROR;
  }
  sqlite3_test_control(SQLITE_TESTCTRL_OPTIMIZATIONS, db, mask);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(mask));
  return TCL_OK;
}

/*
**     load_static_extension DB NAME ...
**
** Load one or more statically linked extensions.
*/
static int SQLITE_TCLAPI tclLoadStaticExtensionCmd(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  extern int sqlite3_amatch_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_appendvfs_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_basexx_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_carray_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_closure_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_csv_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_eval_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_explain_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_fileio_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_decimal_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_fuzzer_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_ieee_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_nextchar_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_percentile_init(sqlite3*,char**,const sqlite3_api_routines*);
#ifndef SQLITE_OMIT_VIRTUALTABLE
  extern int sqlite3_prefixes_init(sqlite3*,char**,const sqlite3_api_routines*);
#endif
  extern int sqlite3_qpvtab_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_randomjson_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_regexp_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_remember_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_series_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_spellfix_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_stmtrand_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_totype_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_wholenumber_init(sqlite3*,char**,const sqlite3_api_routines*);
  extern int sqlite3_unionvtab_init(sqlite3*,char**,const sqlite3_api_routines*);
#ifdef SQLITE_HAVE_ZLIB
  extern int sqlite3_zipfile_init(sqlite3*,char**,const sqlite3_api_routines*);
#endif
  static const struct {
    const char *zExtName;
    int (*pInit)(sqlite3*,char**,const sqlite3_api_routines*);
  } aExtension[] = {
    { "amatch",                sqlite3_amatch_init               },
    { "appendvfs",             sqlite3_appendvfs_init            },
    { "basexx",                sqlite3_basexx_init               },
    { "carray",                sqlite3_carray_init               },
    { "closure",               sqlite3_closure_init              },
    { "csv",                   sqlite3_csv_init                  },
    { "decimal",               sqlite3_decimal_init              },
    { "eval",                  sqlite3_eval_init                 },
    { "explain",               sqlite3_explain_init              },
    { "fileio",                sqlite3_fileio_init               },
    { "fuzzer",                sqlite3_fuzzer_init               },
    { "ieee754",               sqlite3_ieee_init                 },
    { "nextchar",              sqlite3_nextchar_init             },
    { "percentile",            sqlite3_percentile_init           },
#ifndef SQLITE_OMIT_VIRTUALTABLE
    { "prefixes",              sqlite3_prefixes_init             },
#endif
    { "qpvtab",                sqlite3_qpvtab_init               },
    { "randomjson",            sqlite3_randomjson_init           },
    { "regexp",                sqlite3_regexp_init               },
    { "remember",              sqlite3_remember_init             },
    { "series",                sqlite3_series_init               },
    { "spellfix",              sqlite3_spellfix_init             },
    { "stmtrand",              sqlite3_stmtrand_init             },
    { "totype",                sqlite3_totype_init               },
    { "unionvtab",             sqlite3_unionvtab_init            },
    { "wholenumber",           sqlite3_wholenumber_init          },
#ifdef SQLITE_HAVE_ZLIB
    { "zipfile",               sqlite3_zipfile_init              },
#endif
  };
  sqlite3 *db;
  const char *zName;
  int i, j, rc;
  char *zErrMsg = 0;
  if( objc<3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB NAME ...");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  for(j=2; j<objc; j++){
    zName = Tcl_GetString(objv[j]);
    for(i=0; i<ArraySize(aExtension); i++){
      if( strcmp(zName, aExtension[i].zExtName)==0 ) break;
    }
    if( i>=ArraySize(aExtension) ){
      Tcl_AppendResult(interp, "no such extension: ", zName, (char*)0);
      return TCL_ERROR;
    }
    if( aExtension[i].pInit ){
      rc = aExtension[i].pInit(db, &zErrMsg, 0);
    }else{
      rc = SQLITE_OK;
    }
    if( (rc!=SQLITE_OK && rc!=SQLITE_OK_LOAD_PERMANENTLY) || zErrMsg ){
      Tcl_AppendResult(interp, "initialization of ", zName, " failed: ", zErrMsg,
                       (char*)0);
      sqlite3_free(zErrMsg);
      return TCL_ERROR;
    }
  }
  return TCL_OK;
}

/*
**     sorter_test_fakeheap BOOL
**
*/
static int SQLITE_TCLAPI sorter_test_fakeheap(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int bArg;
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "BOOL");
    return TCL_ERROR;
  }

  if( Tcl_GetBooleanFromObj(interp, objv[1], &bArg) ){
    return TCL_ERROR;
  }

  if( bArg ){
    if( sqlite3GlobalConfig.pHeap==0 ){
      sqlite3GlobalConfig.pHeap = SQLITE_INT_TO_PTR(-1);
    }
  }else{
    if( sqlite3GlobalConfig.pHeap==SQLITE_INT_TO_PTR(-1) ){
      sqlite3GlobalConfig.pHeap = 0;
    }
  }

  Tcl_ResetResult(interp);
  return TCL_OK;
}

/*
**     sorter_test_sort4_helper DB SQL1 NSTEP SQL2
**
** Compile SQL statement $SQL1 and step it $NSTEP times. For each row, 
** check that the leftmost and rightmost columns returned are both integers,
** and that both contain the same value.
**
** Then execute statement $SQL2. Check that the statement returns the same
** set of integers in the same order as in the previous step (using $SQL1).
*/
static int SQLITE_TCLAPI sorter_test_sort4_helper(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  const char *zSql1;
  const char *zSql2;
  int nStep; 
  int iStep; 
  unsigned int iCksum1 = 0; 
  unsigned int iCksum2 = 0; 
  int rc;
  int iB;
  sqlite3 *db;
  sqlite3_stmt *pStmt;
  
  if( objc!=5 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB SQL1 NSTEP SQL2");
    return TCL_ERROR;
  }

  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zSql1 = Tcl_GetString(objv[2]);
  if( Tcl_GetIntFromObj(interp, objv[3], &nStep) ) return TCL_ERROR;
  zSql2 = Tcl_GetString(objv[4]);

  rc = sqlite3_prepare_v2(db, zSql1, -1, &pStmt, 0);
  if( rc!=SQLITE_OK ) goto sql_error;

  iB = sqlite3_column_count(pStmt)-1;
  for(iStep=0; iStep<nStep && SQLITE_ROW==sqlite3_step(pStmt); iStep++){
    int a = sqlite3_column_int(pStmt, 0);
    if( a!=sqlite3_column_int(pStmt, iB) ){
      Tcl_AppendResult(interp, "data error: (a!=b)", (void*)0);
      return TCL_ERROR;
    }

    iCksum1 += (iCksum1 << 3) + (unsigned int)a;
  }
  rc = sqlite3_finalize(pStmt);
  if( rc!=SQLITE_OK ) goto sql_error;

  rc = sqlite3_prepare_v2(db, zSql2, -1, &pStmt, 0);
  if( rc!=SQLITE_OK ) goto sql_error;
  for(iStep=0; SQLITE_ROW==sqlite3_step(pStmt); iStep++){
    int a = sqlite3_column_int(pStmt, 0);
    iCksum2 += (iCksum2 << 3) + (unsigned int)a;
  }
  rc = sqlite3_finalize(pStmt);
  if( rc!=SQLITE_OK ) goto sql_error;

  if( iCksum1!=iCksum2 ){
    Tcl_AppendResult(interp, "checksum mismatch", (void*)0);
    return TCL_ERROR;
  }

  return TCL_OK;
 sql_error:
  Tcl_AppendResult(interp, "sql error: ", sqlite3_errmsg(db),  (void*)0);
  return TCL_ERROR;
}



/*
** tclcmd:   register_dbstat_vtab DB
**
** Cause the dbstat virtual table to be available on the connection DB
*/
static int SQLITE_TCLAPI test_register_dbstat_vtab(
  void *clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
#ifdef SQLITE_OMIT_VIRTUALTABLE
  Tcl_AppendResult(interp, "dbstat not available because of "
                           "SQLITE_OMIT_VIRTUALTABLE", (void*)0);
  return TCL_ERROR;
#else
  struct SqliteDb { sqlite3 *db; };
  char *zDb;
  Tcl_CmdInfo cmdInfo;

  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB");
    return TCL_ERROR;
  }

  zDb = Tcl_GetString(objv[1]);
  if( Tcl_GetCommandInfo(interp, zDb, &cmdInfo) ){
    sqlite3* db = ((struct SqliteDb*)cmdInfo.objClientData)->db;
    sqlite3DbstatRegister(db);
  }
  return TCL_OK;
#endif /* SQLITE_OMIT_VIRTUALTABLE */
}

/*
** tclcmd:   sqlite3_db_config DB SETTING VALUE
**
** Invoke sqlite3_db_config() for one of the setting values.
*/
static int SQLITE_TCLAPI test_sqlite3_db_config(
  void *clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  static const struct {
    const char *zName;
    int eVal;
  } aSetting[] = {
    { "FKEY",               SQLITE_DBCONFIG_ENABLE_FKEY },
    { "TRIGGER",            SQLITE_DBCONFIG_ENABLE_TRIGGER },
    { "FTS3_TOKENIZER",     SQLITE_DBCONFIG_ENABLE_FTS3_TOKENIZER },
    { "LOAD_EXTENSION",     SQLITE_DBCONFIG_ENABLE_LOAD_EXTENSION },
    { "NO_CKPT_ON_CLOSE",   SQLITE_DBCONFIG_NO_CKPT_ON_CLOSE },
    { "QPSG",               SQLITE_DBCONFIG_ENABLE_QPSG },
    { "TRIGGER_EQP",        SQLITE_DBCONFIG_TRIGGER_EQP },
    { "RESET_DB",           SQLITE_DBCONFIG_RESET_DATABASE },
    { "DEFENSIVE",          SQLITE_DBCONFIG_DEFENSIVE },
    { "WRITABLE_SCHEMA",    SQLITE_DBCONFIG_WRITABLE_SCHEMA },
    { "LEGACY_ALTER_TABLE", SQLITE_DBCONFIG_LEGACY_ALTER_TABLE },
    { "DQS_DML",            SQLITE_DBCONFIG_DQS_DML },
    { "DQS_DDL",            SQLITE_DBCONFIG_DQS_DDL },
    { "LEGACY_FILE_FORMAT", SQLITE_DBCONFIG_LEGACY_FILE_FORMAT },
    { "STMT_SCANSTATUS",    SQLITE_DBCONFIG_STMT_SCANSTATUS },
  };
  int i;
  int v = 0;
  const char *zSetting;
  sqlite3 *db;

  if( objc!=4 && objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB SETTING [VALUE]");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zSetting = Tcl_GetString(objv[2]);
  if( sqlite3_strglob("SQLITE_*", zSetting)==0 ) zSetting += 7;
  if( sqlite3_strglob("DBCONFIG_*", zSetting)==0 ) zSetting += 9;
  if( sqlite3_strglob("ENABLE_*", zSetting)==0 ) zSetting += 7;
  for(i=0; i<ArraySize(aSetting); i++){
    if( strcmp(zSetting, aSetting[i].zName)==0 ) break;
  }
  if( i>=ArraySize(aSetting) ){
    Tcl_SetObjResult(interp,
      Tcl_NewStringObj("unknown sqlite3_db_config setting", -1));
    return TCL_ERROR;
  }
  if( objc==4 ){
    if( Tcl_GetIntFromObj(interp, objv[3], &v) ) return TCL_ERROR;
  }else{
    v = -1;
  }
  sqlite3_db_config(db, aSetting[i].eVal, v, &v);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(v));
  return TCL_OK;
}
/*
** tclcmd:   sqlite3_txn_state DB ?SCHEMA?
**
** Invoke sqlite3_txn_state(DB,SCHEMA) and return the
** numeric value that results.  Use NULL for SCHEMA if the 3 argument
** is omitted.
*/
static int SQLITE_TCLAPI test_sqlite3_txn_state(
  void *clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db;
  const char *zSchema;
  int iTxn;

  if( objc!=2 && objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB ?SCHEMA?");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zSchema = objc==3 ? Tcl_GetString(objv[2]) : 0;
  iTxn = sqlite3_txn_state(db, zSchema);
  Tcl_SetObjResult(interp, Tcl_NewIntObj(iTxn));
  return TCL_OK;
}

/*
** Change the name of the main database schema from "main" to "icecube".
*/
static int SQLITE_TCLAPI test_dbconfig_maindbname_icecube(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  int rc;
  sqlite3 *db;
  extern int getDbPointer(Tcl_Interp*, const char*, sqlite3**);
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB");
    return TCL_ERROR;
  }else{
    if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
    rc = sqlite3_db_config(db, SQLITE_DBCONFIG_MAINDBNAME, "icecube");
    Tcl_SetObjResult(interp, Tcl_NewIntObj(rc));
    return TCL_OK;
  }
}

/*
** Usage: sqlite3_mmap_warm DB DBNAME
*/
static int SQLITE_TCLAPI test_mmap_warm(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  extern int getDbPointer(Tcl_Interp*, const char*, sqlite3**);
  extern int sqlite3_mmap_warm(sqlite3 *db, const char *);

  if( objc!=2 && objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB ?DBNAME?");
    return TCL_ERROR;
  }else{
    int rc;
    sqlite3 *db;
    const char *zDb = 0;
    if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
    if( objc==3 ){
      zDb = Tcl_GetString(objv[2]);
    }
    rc = sqlite3_mmap_warm(db, zDb);
    Tcl_SetObjResult(interp, Tcl_NewStringObj(sqlite3ErrName(rc), -1));
    return TCL_OK;
  }
}

/*
** Usage:  test_write_db DB OFFSET DATA
**
** Obtain the sqlite3_file* object for the database file for the "main" db
** of handle DB. Then invoke its xWrite method to write data DATA to offset
** OFFSET.
*/
static int SQLITE_TCLAPI test_write_db(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  sqlite3 *db = 0;
  Tcl_WideInt iOff = 0;
  const unsigned char *aData = 0;
  Tcl_Size nData = 0;
  sqlite3_file *pFile = 0;
  int rc;

  if( objc!=4 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB OFFSET DATA");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  if( Tcl_GetWideIntFromObj(interp, objv[2], &iOff) ) return TCL_ERROR;
  aData = Tcl_GetByteArrayFromObj(objv[3], &nData);

  sqlite3_file_control(db, "main", SQLITE_FCNTL_FILE_POINTER, (void*)&pFile);
  rc = pFile->pMethods->xWrite(pFile, aData, (int)(nData&0x7fffffff), iOff);

  Tcl_SetResult(interp, (char *)sqlite3ErrName(rc), TCL_VOLATILE);
  return TCL_OK;
}

/*
** Usage:  sqlite3_register_cksumvfs
**
*/
static int SQLITE_TCLAPI test_register_cksumvfs(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  if( objc!=1 ){
    Tcl_WrongNumArgs(interp, 1, objv, "");
    return TCL_ERROR;
  }else{
    extern int sqlite3_register_cksumvfs(const char*);
    int rc = sqlite3_register_cksumvfs(0);
    Tcl_SetResult(interp, (char *)sqlite3ErrName(rc), TCL_VOLATILE);
  }
  return TCL_OK;
}

/*
** Usage:  sqlite3_unregister_cksumvfs
**
*/
static int SQLITE_TCLAPI test_unregister_cksumvfs(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  if( objc!=1 ){
    Tcl_WrongNumArgs(interp, 1, objv, "");
    return TCL_ERROR;
  }else{
    extern int sqlite3_unregister_cksumvfs(void);
    int rc = sqlite3_unregister_cksumvfs();
    Tcl_SetResult(interp, (char *)sqlite3ErrName(rc), TCL_VOLATILE);
  }
  return TCL_OK;
}

/*
** Usage:  decode_hexdb TEXT
**
** Example:   db deserialize [decode_hexdb $output_of_dbtotxt]
**
** This routine returns a byte-array for an SQLite database file that
** is constructed from a text input which is the output of the "dbtotxt"
** utility.
*/
static int SQLITE_TCLAPI test_decode_hexdb(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  const char *zIn = 0;
  unsigned char *a = 0;
  int n = 0;
  int i, iNext;
  int iOffset = 0;
  int j, k;
  int rc;
  unsigned int x[16];
  if( objc!=2 ){
    Tcl_WrongNumArgs(interp, 1, objv, "HEXDB");
    return TCL_ERROR;
  }
  zIn = Tcl_GetString(objv[1]);
  for(i=0; zIn[i]; i=iNext){
    for(iNext=i; zIn[iNext] && zIn[iNext]!='\n'; iNext++){}
    if( zIn[iNext]=='\n' ) iNext++;
    while( zIn[i]==' ' || zIn[i]=='\t' ){ i++; }
    if( a==0 ){
      int pgsz;
      rc = sscanf(zIn+i, "| size %d pagesize %d", &n, &pgsz);
      if( rc!=2 ) continue;
      if( pgsz<512 || pgsz>65536 || (pgsz&(pgsz-1))!=0 ){
        Tcl_AppendResult(interp, "bad 'pagesize' field", (void*)0);
        return TCL_ERROR;
      }
      n = (n+pgsz-1)&~(pgsz-1);  /* Round n up to the next multiple of pgsz */
      if( n<512 ){
        Tcl_AppendResult(interp, "bad 'size' field", (void*)0);
        return TCL_ERROR;
      }
      a = malloc( n );
      if( a==0 ){
        Tcl_AppendResult(interp, "out of memory", (void*)0);
        return TCL_ERROR;
      }
      memset(a, 0, n);
      continue;
    }
    rc = sscanf(zIn+i, "| page %d offset %d", &j, &k);
    if( rc==2 ){
      iOffset = k;
      continue;
    }
    rc = sscanf(zIn+i,"| %d: %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x",
                &j, &x[0], &x[1], &x[2], &x[3], &x[4], &x[5], &x[6], &x[7],
                &x[8], &x[9], &x[10], &x[11], &x[12], &x[13], &x[14], &x[15]);
    if( rc==17 ){
      k = iOffset+j;
      if( k+16<=n ){
        int ii;
        for(ii=0; ii<16; ii++) a[k+ii] = x[ii]&0xff;
      }
      continue;
    }
  }
  Tcl_SetObjResult(interp, Tcl_NewByteArrayObj(a, n));
  free(a);
  return TCL_OK;
}

/*
** Client data for the autovacuum_pages callback.
*/
struct AutovacPageData {
  Tcl_Interp *interp;
  char *zScript;
};
typedef struct AutovacPageData AutovacPageData;

/*
** Callback functions for sqlite3_autovacuum_pages
*/
static unsigned int test_autovacuum_pages_callback(
  void *pClientData,
  const char *zSchema,
  unsigned int nFilePages,
  unsigned int nFreePages,
  unsigned int nBytePerPage
){
  AutovacPageData *pData = (AutovacPageData*)pClientData;
  Tcl_DString str;
  unsigned int x;
  char zBuf[100];
  Tcl_DStringInit(&str);
  Tcl_DStringAppend(&str, pData->zScript, -1);
  Tcl_DStringAppendElement(&str, zSchema);
  sqlite3_snprintf(sizeof(zBuf), zBuf, "%u", nFilePages);
  Tcl_DStringAppendElement(&str, zBuf);
  sqlite3_snprintf(sizeof(zBuf), zBuf, "%u", nFreePages);
  Tcl_DStringAppendElement(&str, zBuf);
  sqlite3_snprintf(sizeof(zBuf), zBuf, "%u", nBytePerPage);
  Tcl_DStringAppendElement(&str, zBuf);
  Tcl_ResetResult(pData->interp);
  Tcl_Eval(pData->interp, Tcl_DStringValue(&str));
  Tcl_DStringFree(&str);
  x = nFreePages;
  (void)Tcl_GetIntFromObj(0, Tcl_GetObjResult(pData->interp), (int*)&x);
  return x;
}

/*
** Usage:  sqlite3_autovacuum_pages DB SCRIPT
**
** Add an autovacuum-pages callback to database connection DB.  The callback
** will invoke SCRIPT, after appending parameters.
**
** If SCRIPT is an empty string or is omitted, then the callback is
** cancelled.
*/
static int SQLITE_TCLAPI test_autovacuum_pages(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  AutovacPageData *pData;
  sqlite3 *db;
  int rc;
  const char *zScript;
  if( objc!=2 && objc!=3 ){
    Tcl_WrongNumArgs(interp, 1, objv, "DB ?SCRIPT?");
    return TCL_ERROR;
  }
  if( getDbPointer(interp, Tcl_GetString(objv[1]), &db) ) return TCL_ERROR;
  zScript = objc==3 ? Tcl_GetString(objv[2]) : 0;
  if( zScript ){
    size_t nScript = strlen(zScript);
    pData = sqlite3_malloc64( sizeof(*pData) + nScript + 1 );
    if( pData==0 ){
      Tcl_AppendResult(interp, "out of memory", (void*)0);
      return TCL_ERROR;
    }
    pData->interp = interp;
    pData->zScript = (char*)&pData[1];
    memcpy(pData->zScript, zScript, nScript+1);
    rc = sqlite3_autovacuum_pages(db,test_autovacuum_pages_callback,
                                  pData, sqlite3_free);
  }else{
    rc = sqlite3_autovacuum_pages(db, 0, 0, 0);
  }
  if( rc ){
    char zBuf[1000];
    sqlite3_snprintf(sizeof(zBuf), zBuf,
       "sqlite3_autovacuum_pages() returns %d", rc);
    Tcl_AppendResult(interp, zBuf, (void*)0);
    return TCL_ERROR;
  }
  return TCL_OK;
}

/*
** Usage:  number_of_cores
**
** Return a guess at the number of available cores available on the
** processor on which this process is running.
*/
static int SQLITE_TCLAPI guess_number_of_cores(
  void * clientData,
  Tcl_Interp *interp,
  int objc,
  Tcl_Obj *CONST objv[]
){
  unsigned int nCore = 1;
#if SQLITE_OS_WIN
  SYSTEM_INFO sysinfo;
  GetSystemInfo(&sysinfo);
  nCore = (unsigned int)sysinfo.dwNumberOfProcessors;
#elif defined(__APPLE__)
  int nm[2];
  size_t len = 4;
  nm[0] = CTL_HW; nm[1] = HW_AVAILCPU;
  sysctl(nm, 2, &nCore, &len, NULL, 0);
  if( nCore<1 ){
    nm[1] = HW_NCPU;
    sysctl(nm, 2, &nCore, &len, NULL, 0);
  }
#else
  nCore = sysconf(_SC_NPROCESSORS_ONLN);
#endif
  if( nCore<=0 ) nCore = 1;
  Tcl_SetObjResult(interp, Tcl_NewIntObj((int)nCore));
  return SQLITE_OK;
}


/*
** Register commands with the TCL interpreter.
*/
int Sqlitetest1_Init(Tcl_Interp *interp){
  extern int sqlite3_search_count;
  extern int sqlite3_found_count;
  extern int sqlite3_interrupt_count;
  extern int sqlite3_open_file_count;
  extern int sqlite3_sort_count;
  extern int sqlite3_current_time;
#if SQLITE_OS_UNIX && defined(__APPLE__) && SQLITE_ENABLE_LOCKING_STYLE
  extern int sqlite3_hostid_num;
#endif
  extern int sqlite3_max_blobsize;
  extern int SQLITE_TCLAPI sqlite3BtreeSharedCacheReport(void*,
                                          Tcl_Interp*,int,Tcl_Obj*CONST*);
  static struct {
     char *zName;
     Tcl_CmdProc *xProc;
  } aCmd[] = {
     { "db_enter",                      (Tcl_CmdProc*)db_enter               },
     { "db_leave",                      (Tcl_CmdProc*)db_leave               },
     { "sqlite3_mprintf_int",           (Tcl_CmdProc*)sqlite3_mprintf_int    },
     { "sqlite3_mprintf_int64",         (Tcl_CmdProc*)sqlite3_mprintf_int64  },
     { "sqlite3_mprintf_long",          (Tcl_CmdProc*)sqlite3_mprintf_long   },
     { "sqlite3_mprintf_str",           (Tcl_CmdProc*)sqlite3_mprintf_str    },
     { "sqlite3_snprintf_str",          (Tcl_CmdProc*)sqlite3_snprintf_str   },
     { "sqlite3_mprintf_stronly",       (Tcl_CmdProc*)sqlite3_mprintf_stronly},
     { "sqlite3_mprintf_double",        (Tcl_CmdProc*)sqlite3_mprintf_double },
     { "sqlite3_mprintf_scaled",        (Tcl_CmdProc*)sqlite3_mprintf_scaled },
     { "sqlite3_mprintf_hexdouble",   (Tcl_CmdProc*)sqlite3_mprintf_hexdouble},
     { "sqlite3_mprintf_z_test",        (Tcl_CmdProc*)test_mprintf_z        },
     { "sqlite3_mprintf_n_test",        (Tcl_CmdProc*)test_mprintf_n        },
     { "sqlite3_snprintf_int",          (Tcl_CmdProc*)test_snprintf_int     },
     { "sqlite3_last_insert_rowid",     (Tcl_CmdProc*)test_last_rowid       },
     { "sqlite3_exec_printf",           (Tcl_CmdProc*)test_exec_printf      },
     { "sqlite3_exec_hex",              (Tcl_CmdProc*)test_exec_hex         },
     { "sqlite3_exec",                  (Tcl_CmdProc*)test_exec             },
     { "sqlite3_exec_nr",               (Tcl_CmdProc*)test_exec_nr          },
#ifndef SQLITE_OMIT_GET_TABLE
     { "sqlite3_get_table_printf",      (Tcl_CmdProc*)test_get_table_printf },
#endif
     { "sqlite3_close",                 (Tcl_CmdProc*)sqlite_test_close     },
     { "sqlite3_close_v2",              (Tcl_CmdProc*)sqlite_test_close_v2  },
     { "sqlite3_create_function",       (Tcl_CmdProc*)test_create_function  },
     { "sqlite3_create_aggregate",      (Tcl_CmdProc*)test_create_aggregate },
     { "sqlite3_drop_modules",          (Tcl_CmdProc*)test_drop_modules     },
     { "sqlite_register_test_function", (Tcl_CmdProc*)test_register_func    },
     { "sqlite_abort",                  (Tcl_CmdProc*)sqlite_abort          },
     { "sqlite_bind",                   (Tcl_CmdProc*)test_bind             },
     { "breakpoint",                    (Tcl_CmdProc*)test_breakpoint       },
     { "sqlite3_key",                   (Tcl_CmdProc*)test_key              },
     { "sqlite3_rekey",                 (Tcl_CmdProc*)test_rekey            },
     { "sqlite3_interrupt",             (Tcl_CmdProc*)test_interrupt        },
     { "sqlite3_is_interrupted",        (Tcl_CmdProc*)test_is_interrupted   },
     { "sqlite_delete_function",        (Tcl_CmdProc*)delete_function       },
     { "sqlite_delete_collation",       (Tcl_CmdProc*)delete_collation      },
     { "sqlite3_get_autocommit",        (Tcl_CmdProc*)get_autocommit        },
     { "sqlite3_busy_timeout",          (Tcl_CmdProc*)test_busy_timeout     },
     { "printf",                        (Tcl_CmdProc*)test_printf           },
     { "sqlite3IoTrace",              (Tcl_CmdProc*)test_io_trace         },
     { "clang_sanitize_address",        (Tcl_CmdProc*)clang_sanitize_address },
  };
  static struct {
     char *zName;
     Tcl_ObjCmdProc *xProc;
     void *clientData;
  } aObjCmd[] = {
     { "sqlite3_db_config",             test_sqlite3_db_config, 0 },
     { "sqlite3_txn_state",             test_sqlite3_txn_state, 0 },
     { "register_dbstat_vtab",          test_register_dbstat_vtab  },
     { "sqlite3_connection_pointer",    get_sqlite_pointer, 0 },
     { "intarray_addr",                 test_intarray_addr, 0 },
     { "int64array_addr",               test_int64array_addr, 0 },
     { "doublearray_addr",              test_doublearray_addr, 0 },
     { "textarray_addr",                test_textarray_addr, 0 },
     { "sqlite3_bind_int",              test_bind_int,      0 },
     { "sqlite3_bind_zeroblob",         test_bind_zeroblob, 0 },
     { "sqlite3_bind_zeroblob64",       test_bind_zeroblob64, 0 },
     { "sqlite3_bind_int64",            test_bind_int64,    0 },
     { "sqlite3_bind_double",           test_bind_double,   0 },
     { "sqlite3_bind_null",             test_bind_null     ,0 },
     { "sqlite3_bind_text",             test_bind_text     ,0 },
     { "sqlite3_bind_text16",           test_bind_text16   ,0 },
     { "sqlite3_bind_blob",             test_bind_blob     ,0 },
     { "sqlite3_bind_value_from_select",test_bind_value_from_select ,0 },
     { "sqlite3_bind_value_from_preupdate",test_bind_value_from_preupdate ,0 },
#ifndef SQLITE_OMIT_VIRTUALTABLE
     { "sqlite3_carray_bind",           test_carray_bind   ,0 },
#endif
     { "sqlite3_bind_parameter_count",  test_bind_parameter_count, 0},
     { "sqlite3_bind_parameter_name",   test_bind_parameter_name,  0},
     { "sqlite3_bind_parameter_index",  test_bind_parameter_index, 0},
     { "sqlite3_clear_bindings",        test_clear_bindings, 0},
     { "sqlite3_sleep",                 test_sleep,          0},
     { "sqlite3_errcode",               test_errcode       ,0 },
     { "sqlite3_extended_errcode",      test_ex_errcode    ,0 },
     { "sqlite3_errmsg",                test_errmsg        ,0 },
     { "sqlite3_error_offset",          test_error_offset  ,0 },
     { "sqlite3_errmsg16",              test_errmsg16      ,0 },
     { "sqlite3_open",                  test_open          ,0 },
     { "sqlite3_open16",                test_open16        ,0 },
     { "sqlite3_open_v2",               test_open_v2       ,0 },
     { "sqlite3_complete16",            test_complete16    ,0 },
     { "sqlite3_normalize",             test_normalize     ,0 },

     { "sqlite3_prepare",               test_prepare       ,0 },
     { "sqlite3_prepare16",             test_prepare16     ,0 },
     { "sqlite3_prepare_v2",            test_prepare_v2    ,0 },
     { "sqlite3_prepare_v3",            test_prepare_v3    ,0 },
     { "sqlite3_prepare_tkt3134",       test_prepare_tkt3134, 0},
     { "sqlite3_prepare16_v2",          test_prepare16_v2  ,0 },
     { "sqlite3_finalize",              test_finalize      ,0 },
     { "sqlite3_stmt_status",           test_stmt_status   ,0 },
     { "sqlite3_reset",                 test_reset         ,0 },
     { "sqlite3_expired",               test_expired       ,0 },
     { "sqlite3_transfer_bindings",     test_transfer_bind ,0 },
     { "sqlite3_changes",               test_changes       ,0 },
     { "sqlite3_step",                  test_step          ,0 },
     { "sqlite3_sql",                   test_sql           ,0 },
     { "sqlite3_expanded_sql",          test_ex_sql        ,0 },
#ifdef SQLITE_ENABLE_NORMALIZE
     { "sqlite3_normalized_sql",        test_norm_sql      ,0 },
#endif
     { "sqlite3_next_stmt",             test_next_stmt     ,0 },
     { "sqlite3_stmt_readonly",         test_stmt_readonly ,0 },
     { "sqlite3_stmt_isexplain",        test_stmt_isexplain,0 },
     { "sqlite3_stmt_explain",          test_stmt_explain  ,0 },
     { "sqlite3_stmt_busy",             test_stmt_busy     ,0 },
     { "uses_stmt_journal",             uses_stmt_journal ,0 },

     { "sqlite3_release_memory",        test_release_memory,     0},
     { "sqlite3_db_release_memory",     test_db_release_memory,  0},
     { "sqlite3_db_cacheflush",         test_db_cacheflush,      0},
     { "sqlite3_system_errno",          test_system_errno,       0},
     { "sqlite3_db_filename",           test_db_filename,        0},
     { "sqlite3_db_readonly",           test_db_readonly,        0},
     { "sqlite3_soft_heap_limit",       test_soft_heap_limit,    0},
     { "sqlite3_soft_heap_limit64",     test_soft_heap_limit,    0},
     { "sqlite3_hard_heap_limit64",     test_hard_heap_limit,    0},
     { "sqlite3_thread_cleanup",        test_thread_cleanup,     0},
     { "sqlite3_pager_refcounts",       test_pager_refcounts,    0},

     { "sqlite3_load_extension",        test_load_extension,     0},
     { "sqlite3_enable_load_extension", test_enable_load,        0},
     { "sqlite3_extended_result_codes", test_extended_result_codes, 0},
     { "sqlite3_limit",                 test_limit,                 0},
     { "dbconfig_maindbname_icecube",   test_dbconfig_maindbname_icecube },

     { "save_prng_state",               save_prng_state,    0 },
     { "restore_prng_state",            restore_prng_state, 0 },
     { "reset_prng_state",              reset_prng_state,   0 },
     { "prng_seed",                     prng_seed,          0 },
     { "extra_schema_checks",           extra_schema_checks,    0},
     { "database_never_corrupt",        database_never_corrupt, 0},
     { "database_may_be_corrupt",       database_may_be_corrupt, 0},
     { "optimization_control",          optimization_control,0},
#if SQLITE_OS_WIN
     { "lock_win32_file",               win32_file_lock,    0 },
#endif
     { "tcl_objproc",                   runAsObjProc,       0 },

     /* sqlite3_column_*() API */
     { "sqlite3_column_count",          test_column_count  ,0 },
     { "sqlite3_data_count",            test_data_count    ,0 },
     { "sqlite3_column_type",           test_column_type   ,0 },
     { "sqlite3_column_blob",           test_column_blob   ,0 },
     { "sqlite3_column_double",         test_column_double ,0 },
     { "sqlite3_column_int64",          test_column_int64  ,0 },
     { "sqlite3_column_text",   test_stmt_utf8,  (void*)sqlite3_column_text },
     { "sqlite3_column_name",   test_stmt_utf8,  (void*)sqlite3_column_name },
     { "sqlite3_column_int",    test_stmt_int,   (void*)sqlite3_column_int  },
     { "sqlite3_column_bytes",  test_stmt_int,   (void*)sqlite3_column_bytes},
#ifndef SQLITE_OMIT_DECLTYPE
     { "sqlite3_column_decltype",test_stmt_utf8,(void*)sqlite3_column_decltype},
#endif
#ifdef SQLITE_ENABLE_COLUMN_METADATA
{ "sqlite3_column_database_name",test_stmt_utf8,(void*)sqlite3_column_database_name},
{ "sqlite3_column_table_name",test_stmt_utf8,(void*)sqlite3_column_table_name},
{ "sqlite3_column_origin_name",test_stmt_utf8,(void*)sqlite3_column_origin_name},
#endif

#ifndef SQLITE_OMIT_UTF16
     { "sqlite3_column_bytes16", test_stmt_int, (void*)sqlite3_column_bytes16 },
     { "sqlite3_column_text16",  test_stmt_utf16, (void*)sqlite3_column_text16},
     { "sqlite3_column_name16",  test_stmt_utf16, (void*)sqlite3_column_name16},
     { "add_alignment_test_collations", add_alignment_test_collations, 0      },
#ifndef SQLITE_OMIT_DECLTYPE
     { "sqlite3_column_decltype16",test_stmt_utf16,(void*)sqlite3_column_decltype16},
#endif
#ifdef SQLITE_ENABLE_COLUMN_METADATA
{"sqlite3_column_database_name16",
  test_stmt_utf16, (void*)sqlite3_column_database_name16},
{"sqlite3_column_table_name16", test_stmt_utf16, (void*)sqlite3_column_table_name16},
{"sqlite3_column_origin_name16", test_stmt_utf16, (void*)sqlite3_column_origin_name16},
#endif
#endif
     { "sqlite3_create_collation_v2", test_create_collation_v2, 0 },
     { "sqlite3_global_recover",     test_global_recover, 0   },
     { "working_64bit_int",          working_64bit_int,   0   },
     { "vfs_unlink_test",            vfs_unlink_test,     0   },
     { "vfs_initfail_test",          vfs_initfail_test,   0   },
     { "vfs_unregister_all",         vfs_unregister_all,  0   },
     { "vfs_reregister_all",         vfs_reregister_all,  0   },
     { "file_control_test",          file_control_test,   0   },
     { "file_control_lasterrno_test", file_control_lasterrno_test,  0   },
     { "file_control_lockproxy_test", file_control_lockproxy_test,  0   },
     { "file_control_chunksize_test", file_control_chunksize_test,  0   },
     { "file_control_sizehint_test",  file_control_sizehint_test,   0   },
     { "file_control_data_version",   file_control_data_version,    0   },
#if SQLITE_OS_WIN
     { "file_control_win32_av_retry", file_control_win32_av_retry,  0   },
     { "file_control_win32_get_handle", file_control_win32_get_handle, 0  },
     { "file_control_win32_set_handle", file_control_win32_set_handle, 0  },
#endif
     { "file_control_persist_wal",    file_control_persist_wal,     0   },
     { "file_control_powersafe_overwrite",file_control_powersafe_overwrite,0},
     { "file_control_vfsname",        file_control_vfsname,         0   },
     { "file_control_reservebytes",   file_control_reservebytes,    0   },
     { "file_control_tempfilename",   file_control_tempfilename,    0   },
     { "file_control_external_reader",   file_control_external_reader,    0   },
     { "sqlite3_vfs_list",           vfs_list,     0   },
     { "sqlite3_create_function_v2", test_create_function_v2, 0 },

     /* Functions from os.h */
#ifndef SQLITE_OMIT_UTF16
     { "add_test_collate",        test_collate, 0            },
     { "add_test_collate_needed", test_collate_needed, 0     },
     { "add_test_function",       test_function, 0           },
     { "add_test_utf16bin_collate",    test_utf16bin_collate, 0        },
#endif
     { "sqlite3_test_errstr",     test_errstr, 0             },
     { "tcl_variable_type",       tcl_variable_type, 0       },
     { "fpnum_compare",           fpnum_compare, 0           },
#ifndef SQLITE_OMIT_SHARED_CACHE
     { "sqlite3_enable_shared_cache", test_enable_shared, 0  },
     { "sqlite3_shared_cache_report", sqlite3BtreeSharedCacheReport, 0},
#endif
     { "sqlite3_libversion_number", test_libversion_number, 0  },
     { "sqlite3_table_column_metadata", test_table_column_metadata, 0  },
#ifndef SQLITE_OMIT_INCRBLOB
     { "sqlite3_blob_reopen", test_blob_reopen, 0  },
#endif
     { "pcache_stats",       test_pcache_stats, 0  },
#ifdef SQLITE_ENABLE_UNLOCK_NOTIFY
     { "sqlite3_unlock_notify", test_unlock_notify, 0  },
#endif
     { "sqlite3_wal_checkpoint",   test_wal_checkpoint, 0  },
     { "sqlite3_wal_checkpoint_v2",test_wal_checkpoint_v2, 0  },
     { "sqlite3_wal_autocheckpoint",test_wal_autocheckpoint, 0  },
     { "test_sqlite3_log",         test_sqlite3_log, 0  },
#ifndef SQLITE_OMIT_EXPLAIN
     { "print_explain_query_plan", test_print_eqp, 0  },
#endif
     { "strftime",             strftime_cmd      },
     { "sqlite3_test_control", test_test_control },
     { ".treetrace",           test_treetrace    },
#if SQLITE_OS_UNIX
     { "getrusage", test_getrusage },
#endif
     { "load_static_extension", tclLoadStaticExtensionCmd },
     { "sorter_test_fakeheap", sorter_test_fakeheap },
     { "sorter_test_sort4_helper", sorter_test_sort4_helper },
#ifdef SQLITE_ENABLE_STMT_SCANSTATUS
     { "sqlite3_stmt_scanstatus",       test_stmt_scanstatus,   0 },
     { "sqlite3_stmt_scanstatus_reset", test_stmt_scanstatus_reset,   0 },
#endif
#ifdef SQLITE_ENABLE_SQLLOG
     { "sqlite3_config_sqllog",         test_config_sqllog,   0 },
#endif
     { "vfs_current_time_int64",           vfsCurrentTimeInt64,   0 },
#ifdef SQLITE_ENABLE_SNAPSHOT
     { "sqlite3_snapshot_get", test_snapshot_get, 0 },
     { "sqlite3_snapshot_open", test_snapshot_open, 0 },
     { "sqlite3_snapshot_free", test_snapshot_free, 0 },
     { "sqlite3_snapshot_cmp", test_snapshot_cmp, 0 },
     { "sqlite3_snapshot_recover", test_snapshot_recover, 0 },
     { "sqlite3_snapshot_get_blob", test_snapshot_get_blob, 0 },
     { "sqlite3_snapshot_open_blob", test_snapshot_open_blob, 0 },
     { "sqlite3_snapshot_cmp_blob", test_snapshot_cmp_blob, 0 },
#endif
     { "sqlite3_delete_database", test_delete_database,    0 },
     { "atomic_batch_write",      test_atomic_batch_write, 0 },
     { "sqlite3_mmap_warm",       test_mmap_warm,          0 },
     { "sqlite3_config_sorterref", test_config_sorterref,   0 },
     { "sqlite3_autovacuum_pages", test_autovacuum_pages,   0 },
     { "decode_hexdb",             test_decode_hexdb,       0 },
     { "test_write_db",            test_write_db,           0 },
     { "sqlite3_register_cksumvfs", test_register_cksumvfs,  0 },
     { "sqlite3_unregister_cksumvfs", test_unregister_cksumvfs,  0 },
     { "number_of_cores",             guess_number_of_cores,     0 },
#ifndef SQLITE_OMIT_VIRTUALTABLE
     { "create_null_module",       test_create_null_module,     0 },
#endif
  };
  static int bitmask_size = sizeof(Bitmask)*8;
  int i;
  extern int sqlite3_sync_count, sqlite3_fullsync_count;
  extern int sqlite3_opentemp_count;
  extern int sqlite3_like_count;
  extern int sqlite3_xferopt_count;
  extern int sqlite3_pager_readdb_count;
  extern int sqlite3_pager_writedb_count;
  extern int sqlite3_pager_writej_count;
#if SQLITE_OS_WIN
  extern LONG volatile sqlite3_os_type;
#endif
#ifdef SQLITE_DEBUG
  extern u32 sqlite3WhereTrace;
  extern int sqlite3OSTrace;
  extern int sqlite3WalTrace;
#endif
#ifdef SQLITE_TEST
#ifdef SQLITE_ENABLE_FTS3
  extern int sqlite3_fts3_enable_parentheses;
#endif
#endif

  for(i=0; i<sizeof(aCmd)/sizeof(aCmd[0]); i++){
    Tcl_CreateCommand(interp, aCmd[i].zName, aCmd[i].xProc, 0, 0);
  }
  for(i=0; i<sizeof(aObjCmd)/sizeof(aObjCmd[0]); i++){
    Tcl_CreateObjCommand(interp, aObjCmd[i].zName, 
        aObjCmd[i].xProc, aObjCmd[i].clientData, 0);
  }
  Tcl_LinkVar(interp, "sqlite_search_count", 
      (char*)&sqlite3_search_count, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite_found_count", 
      (char*)&sqlite3_found_count, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite_sort_count", 
      (char*)&sqlite3_sort_count, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite3_max_blobsize", 
      (char*)&sqlite3_max_blobsize, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite_like_count", 
      (char*)&sqlite3_like_count, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite_interrupt_count", 
      (char*)&sqlite3_interrupt_count, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite_open_file_count", 
      (char*)&sqlite3_open_file_count, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite_current_time", 
      (char*)&sqlite3_current_time, TCL_LINK_INT);
#if SQLITE_OS_UNIX && defined(__APPLE__) && SQLITE_ENABLE_LOCKING_STYLE
  Tcl_LinkVar(interp, "sqlite_hostid_num", 
      (char*)&sqlite3_hostid_num, TCL_LINK_INT);
#endif
  Tcl_LinkVar(interp, "sqlite3_xferopt_count",
      (char*)&sqlite3_xferopt_count, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite3_pager_readdb_count",
      (char*)&sqlite3_pager_readdb_count, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite3_pager_writedb_count",
      (char*)&sqlite3_pager_writedb_count, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite3_pager_writej_count",
      (char*)&sqlite3_pager_writej_count, TCL_LINK_INT);
#ifndef SQLITE_OMIT_UTF16
  Tcl_LinkVar(interp, "unaligned_string_counter",
      (char*)&unaligned_string_counter, TCL_LINK_INT);
#endif
#ifndef SQLITE_OMIT_UTF16
  Tcl_LinkVar(interp, "sqlite_last_needed_collation",
      (char*)&pzNeededCollation, TCL_LINK_STRING|TCL_LINK_READ_ONLY);
#endif
#if SQLITE_OS_WIN
  Tcl_LinkVar(interp, "sqlite_os_type",
      (char*)&sqlite3_os_type, TCL_LINK_LONG);
#endif
#ifdef SQLITE_TEST
  {
    static const char *query_plan = "*** OBSOLETE VARIABLE ***";
    Tcl_LinkVar(interp, "sqlite_query_plan",
       (char*)&query_plan, TCL_LINK_STRING|TCL_LINK_READ_ONLY);
  }
#endif
#ifdef SQLITE_DEBUG
  Tcl_LinkVar(interp, "sqlite_where_trace",
      (char*)&sqlite3WhereTrace, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite_os_trace",
      (char*)&sqlite3OSTrace, TCL_LINK_INT);
#ifndef SQLITE_OMIT_WAL
  Tcl_LinkVar(interp, "sqlite_wal_trace",
      (char*)&sqlite3WalTrace, TCL_LINK_INT);
#endif
#endif
#ifndef SQLITE_OMIT_DISKIO
  Tcl_LinkVar(interp, "sqlite_opentemp_count",
      (char*)&sqlite3_opentemp_count, TCL_LINK_INT);
#endif
  Tcl_LinkVar(interp, "sqlite_static_bind_value",
      (char*)&sqlite_static_bind_value, TCL_LINK_STRING);
  Tcl_LinkVar(interp, "sqlite_static_bind_nbyte",
      (char*)&sqlite_static_bind_nbyte, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite_temp_directory",
      (char*)&sqlite3_temp_directory, TCL_LINK_STRING);
  Tcl_LinkVar(interp, "sqlite_data_directory",
      (char*)&sqlite3_data_directory, TCL_LINK_STRING);
  Tcl_LinkVar(interp, "bitmask_size",
      (char*)&bitmask_size, TCL_LINK_INT|TCL_LINK_READ_ONLY);
  Tcl_LinkVar(interp, "sqlite_sync_count",
      (char*)&sqlite3_sync_count, TCL_LINK_INT);
  Tcl_LinkVar(interp, "sqlite_fullsync_count",
      (char*)&sqlite3_fullsync_count, TCL_LINK_INT);
#if defined(SQLITE_ENABLE_TREETRACE)
  Tcl_LinkVar(interp, "sqlite3_unsupported_treetrace",
      (char*)&sqlite3TreeTrace, TCL_LINK_INT);
#endif
#if defined(SQLITE_ENABLE_FTS3) && defined(SQLITE_TEST)
  Tcl_LinkVar(interp, "sqlite_fts3_enable_parentheses",
      (char*)&sqlite3_fts3_enable_parentheses, TCL_LINK_INT);
#endif
  return TCL_OK;
}
