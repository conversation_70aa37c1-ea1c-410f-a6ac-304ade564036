# This is the CMakeCache file.
# For build in directory: /home/<USER>/sqlite_loger/build
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/llvm-addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/llvm-ar

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:STRING=Debug

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//No help, variable specified on the command line.
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/clang++

//LLVM archiver
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/llvm-ar-18

//`clang-scan-deps` dependency scanner
CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS:FILEPATH=CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND

//Generate index for LLVM archive
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/llvm-ranlib-18

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//No help, variable specified on the command line.
CMAKE_C_COMPILER:FILEPATH=/usr/bin/clang

//LLVM archiver
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/llvm-ar-18

//`clang-scan-deps` dependency scanner
CMAKE_C_COMPILER_CLANG_SCAN_DEPS:FILEPATH=CMAKE_C_COMPILER_CLANG_SCAN_DEPS-NOTFOUND

//Generate index for LLVM archive
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/llvm-ranlib-18

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=/usr/bin/llvm-dlltool

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//No help, variable specified on the command line.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/home/<USER>/sqlite_loger/build/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld.lld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/llvm-nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/llvm-objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/llvm-objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=FunctionLoggerPass

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/llvm-ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/llvm-readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/llvm-strip

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//The directory containing a CMake configuration file for CURL.
CURL_DIR:PATH=CURL_DIR-NOTFOUND

//Path to a file.
CURL_INCLUDE_DIR:PATH=/usr/include/x86_64-linux-gnu

//Path to a library.
CURL_LIBRARY_DEBUG:FILEPATH=CURL_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
CURL_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libcurl.so

//Path to a file.
FFI_INCLUDE_DIRS:PATH=/usr/include/x86_64-linux-gnu

//Path to a library.
FFI_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libffi.so

//Path to a library.
FFI_STATIC_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libffi.a

//Value Computed by CMake
FunctionLoggerPass_BINARY_DIR:STATIC=/home/<USER>/sqlite_loger/build

//Value Computed by CMake
FunctionLoggerPass_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
FunctionLoggerPass_SOURCE_DIR:STATIC=/home/<USER>/sqlite_loger/pass

//Path to a file.
LIBXML2_INCLUDE_DIR:PATH=/usr/include/libxml2

//Path to a library.
LIBXML2_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libxml2.so

//Path to a program.
LIBXML2_XMLLINT_EXECUTABLE:FILEPATH=LIBXML2_XMLLINT_EXECUTABLE-NOTFOUND

//The directory containing a CMake configuration file for LLVM.
LLVM_DIR:PATH=/usr/lib/llvm-18/cmake

//Path to a file.
LibEdit_INCLUDE_DIRS:PATH=/usr/include

//Path to a library.
LibEdit_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libedit.so

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=PKG_CONFIG_EXECUTABLE-NOTFOUND

//Path to a library.
Terminfo_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libtinfo.so

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=ZLIB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libz.so

//Path to a file.
zstd_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
zstd_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libzstd.so

//Path to a library.
zstd_STATIC_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libzstd.a


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/sqlite_loger/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=28
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS
CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_CLANG_SCAN_DEPS
CMAKE_C_COMPILER_CLANG_SCAN_DEPS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/sqlite_loger/pass
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.28
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_DIR
CURL_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_INCLUDE_DIR
CURL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_LIBRARY_DEBUG
CURL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_LIBRARY_RELEASE
CURL_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFI_HEADER
FFI_HEADER-ADVANCED:INTERNAL=1
FFI_HEADER:INTERNAL=ffi.h
//ADVANCED property for variable: FFI_INCLUDE_DIRS
FFI_INCLUDE_DIRS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFI_LIBRARIES
FFI_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FFI_STATIC_LIBRARIES
FFI_STATIC_LIBRARIES-ADVANCED:INTERNAL=1
//Details about finding CURL
FIND_PACKAGE_MESSAGE_DETAILS_CURL:INTERNAL=[/usr/lib/x86_64-linux-gnu/libcurl.so][/usr/include/x86_64-linux-gnu][c ][v8.5.0()]
//Details about finding FFI
FIND_PACKAGE_MESSAGE_DETAILS_FFI:INTERNAL=[/usr/lib/x86_64-linux-gnu/libffi.so][1][v()]
//Details about finding LibEdit
FIND_PACKAGE_MESSAGE_DETAILS_LibEdit:INTERNAL=[/usr/include][/usr/lib/x86_64-linux-gnu/libedit.so][v2.11()]
//Details about finding LibXml2
FIND_PACKAGE_MESSAGE_DETAILS_LibXml2:INTERNAL=[/usr/lib/x86_64-linux-gnu/libxml2.so][/usr/include/libxml2][v2.9.14()]
//Details about finding Terminfo
FIND_PACKAGE_MESSAGE_DETAILS_Terminfo:INTERNAL=[/usr/lib/x86_64-linux-gnu/libtinfo.so][1][v()]
//Details about finding ZLIB
FIND_PACKAGE_MESSAGE_DETAILS_ZLIB:INTERNAL=[/usr/lib/x86_64-linux-gnu/libz.so][/usr/include][c ][v1.3()]
//Details about finding zstd
FIND_PACKAGE_MESSAGE_DETAILS_zstd:INTERNAL=[/usr/lib/x86_64-linux-gnu/libzstd.so][/usr/include][v()]
//ADVANCED property for variable: HAVE_FFI_CALL
HAVE_FFI_CALL-ADVANCED:INTERNAL=1
//Test HAVE_FFI_CALL
HAVE_FFI_CALL:INTERNAL=1
//ADVANCED property for variable: HAVE_FFI_H
HAVE_FFI_H-ADVANCED:INTERNAL=1
HAVE_FFI_H:INTERNAL=1
//Have include histedit.h
HAVE_HISTEDIT_H:INTERNAL=1
//ADVANCED property for variable: LIBXML2_INCLUDE_DIR
LIBXML2_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LIBXML2_LIBRARY
LIBXML2_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LIBXML2_XMLLINT_EXECUTABLE
LIBXML2_XMLLINT_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LibEdit_INCLUDE_DIRS
LibEdit_INCLUDE_DIRS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LibEdit_LIBRARIES
LibEdit_LIBRARIES-ADVANCED:INTERNAL=1
PC_LIBEDIT_CFLAGS:INTERNAL=
PC_LIBEDIT_CFLAGS_I:INTERNAL=
PC_LIBEDIT_CFLAGS_OTHER:INTERNAL=
PC_LIBEDIT_FOUND:INTERNAL=
PC_LIBEDIT_INCLUDEDIR:INTERNAL=
PC_LIBEDIT_LIBDIR:INTERNAL=
PC_LIBEDIT_LIBS:INTERNAL=
PC_LIBEDIT_LIBS_L:INTERNAL=
PC_LIBEDIT_LIBS_OTHER:INTERNAL=
PC_LIBEDIT_LIBS_PATHS:INTERNAL=
PC_LIBEDIT_MODULE_NAME:INTERNAL=
PC_LIBEDIT_PREFIX:INTERNAL=
PC_LIBEDIT_STATIC_CFLAGS:INTERNAL=
PC_LIBEDIT_STATIC_CFLAGS_I:INTERNAL=
PC_LIBEDIT_STATIC_CFLAGS_OTHER:INTERNAL=
PC_LIBEDIT_STATIC_LIBDIR:INTERNAL=
PC_LIBEDIT_STATIC_LIBS:INTERNAL=
PC_LIBEDIT_STATIC_LIBS_L:INTERNAL=
PC_LIBEDIT_STATIC_LIBS_OTHER:INTERNAL=
PC_LIBEDIT_STATIC_LIBS_PATHS:INTERNAL=
PC_LIBEDIT_VERSION:INTERNAL=
PC_LIBXML_CFLAGS:INTERNAL=
PC_LIBXML_CFLAGS_I:INTERNAL=
PC_LIBXML_CFLAGS_OTHER:INTERNAL=
PC_LIBXML_FOUND:INTERNAL=
PC_LIBXML_INCLUDEDIR:INTERNAL=
PC_LIBXML_LIBDIR:INTERNAL=
PC_LIBXML_LIBS:INTERNAL=
PC_LIBXML_LIBS_L:INTERNAL=
PC_LIBXML_LIBS_OTHER:INTERNAL=
PC_LIBXML_LIBS_PATHS:INTERNAL=
PC_LIBXML_MODULE_NAME:INTERNAL=
PC_LIBXML_PREFIX:INTERNAL=
PC_LIBXML_STATIC_CFLAGS:INTERNAL=
PC_LIBXML_STATIC_CFLAGS_I:INTERNAL=
PC_LIBXML_STATIC_CFLAGS_OTHER:INTERNAL=
PC_LIBXML_STATIC_LIBDIR:INTERNAL=
PC_LIBXML_STATIC_LIBS:INTERNAL=
PC_LIBXML_STATIC_LIBS_L:INTERNAL=
PC_LIBXML_STATIC_LIBS_OTHER:INTERNAL=
PC_LIBXML_STATIC_LIBS_PATHS:INTERNAL=
PC_LIBXML_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Terminfo_LIBRARIES
Terminfo_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Terminfo_LINKABLE
Terminfo_LINKABLE-ADVANCED:INTERNAL=1
//Test Terminfo_LINKABLE
Terminfo_LINKABLE:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//linker supports push/pop state
_CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED:INTERNAL=TRUE
__pkg_config_checked_PC_LIBEDIT:INTERNAL=1
__pkg_config_checked_PC_LIBXML:INTERNAL=1
//ADVANCED property for variable: zstd_INCLUDE_DIR
zstd_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: zstd_LIBRARY
zstd_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: zstd_STATIC_LIBRARY
zstd_STATIC_LIBRARY-ADVANCED:INTERNAL=1

