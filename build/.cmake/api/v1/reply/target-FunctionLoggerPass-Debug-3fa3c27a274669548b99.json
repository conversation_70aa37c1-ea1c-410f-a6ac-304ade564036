{"artifacts": [{"path": "lib/libFunctionLoggerPass.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 13, "parent": 0}, {"command": 1, "file": 0, "line": 11, "parent": 0}, {"command": 2, "file": 0, "line": 9, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17 -fPIC"}], "defines": [{"define": "FunctionLoggerPass_EXPORTS"}, {"backtrace": 2, "define": "_GNU_SOURCE"}, {"backtrace": 2, "define": "__STDC_CONSTANT_MACROS"}, {"backtrace": 2, "define": "__STDC_FORMAT_MACROS"}, {"backtrace": 2, "define": "__STDC_LIMIT_MACROS"}], "includes": [{"backtrace": 3, "path": "/usr/lib/llvm-18/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "id": "FunctionLoggerPass::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "", "role": "flags"}], "language": "CXX"}, "name": "FunctionLoggerPass", "nameOnDisk": "libFunctionLoggerPass.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "FunctionLogger.cpp", "sourceGroupIndex": 0}], "type": "MODULE_LIBRARY"}