{"kind": "toolchains", "toolchains": [{"compiler": {"id": "Clang", "implicit": {"includeDirectories": ["/usr/lib/llvm-18/lib/clang/18/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "linkDirectories": ["/usr/lib/gcc/x86_64-linux-gnu/13", "/usr/lib64", "/lib/x86_64-linux-gnu", "/lib64", "/usr/lib/x86_64-linux-gnu", "/lib", "/usr/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["gcc", "gcc_s", "c", "gcc", "gcc_s"]}, "path": "/usr/bin/clang", "version": "18.1.3"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "Clang", "implicit": {"includeDirectories": ["/usr/include/c++/13", "/usr/include/x86_64-linux-gnu/c++/13", "/usr/include/c++/13/backward", "/usr/lib/llvm-18/lib/clang/18/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "linkDirectories": ["/usr/lib/gcc/x86_64-linux-gnu/13", "/usr/lib64", "/lib/x86_64-linux-gnu", "/lib64", "/usr/lib/x86_64-linux-gnu", "/lib", "/usr/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "m", "gcc_s", "gcc", "c", "gcc_s", "gcc"]}, "path": "/usr/bin/clang++", "version": "18.1.3"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}], "version": {"major": 1, "minor": 0}}