{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "/home/<USER>/sqlite_loger/build/CMakeFiles/3.28.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake"}, {"isGenerated": true, "path": "/home/<USER>/sqlite_loger/build/CMakeFiles/3.28.3/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "/home/<USER>/sqlite_loger/build/CMakeFiles/3.28.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "/usr/lib/llvm-18/cmake/LLVMConfigVersion.cmake"}, {"isExternal": true, "path": "/usr/lib/llvm-18/cmake/LLVMConfig.cmake"}, {"isExternal": true, "path": "/usr/lib/llvm-18/cmake/FindFFI.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakePushCheckState.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/usr/lib/llvm-18/cmake/FindLibEdit.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakePushCheckState.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/usr/lib/llvm-18/cmake/FindTerminfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakePushCheckState.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/usr/lib/llvm-18/cmake/Findzstd.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindLibXml2.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindCURL.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/SelectLibraryConfigurations.cmake"}, {"isExternal": true, "path": "/usr/lib/llvm-18/lib/cmake/llvm/LLVMExports.cmake"}, {"isExternal": true, "path": "/usr/lib/llvm-18/lib/cmake/llvm/LLVMExports-relwithdebinfo.cmake"}, {"isExternal": true, "path": "/usr/lib/llvm-18/lib/cmake/llvm/LLVM-Config.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/sqlite_loger/build", "source": "/home/<USER>/sqlite_loger/pass"}, "version": {"major": 1, "minor": 0}}