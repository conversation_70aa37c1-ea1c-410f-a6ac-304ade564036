# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/sqlite_loger/pass

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/sqlite_loger/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/FunctionLoggerPass.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/intrinsics_gen.dir/clean
clean: CMakeFiles/omp_gen.dir/clean
clean: CMakeFiles/acc_gen.dir/clean
clean: CMakeFiles/RISCVTargetParserTableGen.dir/clean
clean: CMakeFiles/FunctionLoggerPass.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/intrinsics_gen.dir

# All Build rule for target.
CMakeFiles/intrinsics_gen.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/intrinsics_gen.dir/build.make CMakeFiles/intrinsics_gen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/intrinsics_gen.dir/build.make CMakeFiles/intrinsics_gen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/sqlite_loger/build/CMakeFiles --progress-num= "Built target intrinsics_gen"
.PHONY : CMakeFiles/intrinsics_gen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/intrinsics_gen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/sqlite_loger/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/intrinsics_gen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/sqlite_loger/build/CMakeFiles 0
.PHONY : CMakeFiles/intrinsics_gen.dir/rule

# Convenience name for target.
intrinsics_gen: CMakeFiles/intrinsics_gen.dir/rule
.PHONY : intrinsics_gen

# clean rule for target.
CMakeFiles/intrinsics_gen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/intrinsics_gen.dir/build.make CMakeFiles/intrinsics_gen.dir/clean
.PHONY : CMakeFiles/intrinsics_gen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/omp_gen.dir

# All Build rule for target.
CMakeFiles/omp_gen.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/omp_gen.dir/build.make CMakeFiles/omp_gen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/omp_gen.dir/build.make CMakeFiles/omp_gen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/sqlite_loger/build/CMakeFiles --progress-num= "Built target omp_gen"
.PHONY : CMakeFiles/omp_gen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/omp_gen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/sqlite_loger/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/omp_gen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/sqlite_loger/build/CMakeFiles 0
.PHONY : CMakeFiles/omp_gen.dir/rule

# Convenience name for target.
omp_gen: CMakeFiles/omp_gen.dir/rule
.PHONY : omp_gen

# clean rule for target.
CMakeFiles/omp_gen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/omp_gen.dir/build.make CMakeFiles/omp_gen.dir/clean
.PHONY : CMakeFiles/omp_gen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/acc_gen.dir

# All Build rule for target.
CMakeFiles/acc_gen.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/acc_gen.dir/build.make CMakeFiles/acc_gen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/acc_gen.dir/build.make CMakeFiles/acc_gen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/sqlite_loger/build/CMakeFiles --progress-num= "Built target acc_gen"
.PHONY : CMakeFiles/acc_gen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/acc_gen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/sqlite_loger/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/acc_gen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/sqlite_loger/build/CMakeFiles 0
.PHONY : CMakeFiles/acc_gen.dir/rule

# Convenience name for target.
acc_gen: CMakeFiles/acc_gen.dir/rule
.PHONY : acc_gen

# clean rule for target.
CMakeFiles/acc_gen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/acc_gen.dir/build.make CMakeFiles/acc_gen.dir/clean
.PHONY : CMakeFiles/acc_gen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/RISCVTargetParserTableGen.dir

# All Build rule for target.
CMakeFiles/RISCVTargetParserTableGen.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RISCVTargetParserTableGen.dir/build.make CMakeFiles/RISCVTargetParserTableGen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RISCVTargetParserTableGen.dir/build.make CMakeFiles/RISCVTargetParserTableGen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/sqlite_loger/build/CMakeFiles --progress-num= "Built target RISCVTargetParserTableGen"
.PHONY : CMakeFiles/RISCVTargetParserTableGen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/RISCVTargetParserTableGen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/sqlite_loger/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/RISCVTargetParserTableGen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/sqlite_loger/build/CMakeFiles 0
.PHONY : CMakeFiles/RISCVTargetParserTableGen.dir/rule

# Convenience name for target.
RISCVTargetParserTableGen: CMakeFiles/RISCVTargetParserTableGen.dir/rule
.PHONY : RISCVTargetParserTableGen

# clean rule for target.
CMakeFiles/RISCVTargetParserTableGen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RISCVTargetParserTableGen.dir/build.make CMakeFiles/RISCVTargetParserTableGen.dir/clean
.PHONY : CMakeFiles/RISCVTargetParserTableGen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/FunctionLoggerPass.dir

# All Build rule for target.
CMakeFiles/FunctionLoggerPass.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FunctionLoggerPass.dir/build.make CMakeFiles/FunctionLoggerPass.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FunctionLoggerPass.dir/build.make CMakeFiles/FunctionLoggerPass.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/sqlite_loger/build/CMakeFiles --progress-num=1,2 "Built target FunctionLoggerPass"
.PHONY : CMakeFiles/FunctionLoggerPass.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/FunctionLoggerPass.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/sqlite_loger/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/FunctionLoggerPass.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/sqlite_loger/build/CMakeFiles 0
.PHONY : CMakeFiles/FunctionLoggerPass.dir/rule

# Convenience name for target.
FunctionLoggerPass: CMakeFiles/FunctionLoggerPass.dir/rule
.PHONY : FunctionLoggerPass

# clean rule for target.
CMakeFiles/FunctionLoggerPass.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FunctionLoggerPass.dir/build.make CMakeFiles/FunctionLoggerPass.dir/clean
.PHONY : CMakeFiles/FunctionLoggerPass.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

