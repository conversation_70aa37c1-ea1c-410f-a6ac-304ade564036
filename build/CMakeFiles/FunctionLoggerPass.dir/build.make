# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/sqlite_loger/pass

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/sqlite_loger/build

# Include any dependencies generated for this target.
include CMakeFiles/FunctionLoggerPass.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/FunctionLoggerPass.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/FunctionLoggerPass.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/FunctionLoggerPass.dir/flags.make

CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.o: CMakeFiles/FunctionLoggerPass.dir/flags.make
CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.o: /home/<USER>/sqlite_loger/pass/FunctionLogger.cpp
CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.o: CMakeFiles/FunctionLoggerPass.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/sqlite_loger/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.o"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.o -MF CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.o.d -o CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.o -c /home/<USER>/sqlite_loger/pass/FunctionLogger.cpp

CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.i"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/sqlite_loger/pass/FunctionLogger.cpp > CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.i

CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.s"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/sqlite_loger/pass/FunctionLogger.cpp -o CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.s

# Object files for target FunctionLoggerPass
FunctionLoggerPass_OBJECTS = \
"CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.o"

# External object files for target FunctionLoggerPass
FunctionLoggerPass_EXTERNAL_OBJECTS =

lib/libFunctionLoggerPass.so: CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.o
lib/libFunctionLoggerPass.so: CMakeFiles/FunctionLoggerPass.dir/build.make
lib/libFunctionLoggerPass.so: CMakeFiles/FunctionLoggerPass.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/sqlite_loger/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared module lib/libFunctionLoggerPass.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/FunctionLoggerPass.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/FunctionLoggerPass.dir/build: lib/libFunctionLoggerPass.so
.PHONY : CMakeFiles/FunctionLoggerPass.dir/build

CMakeFiles/FunctionLoggerPass.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/FunctionLoggerPass.dir/cmake_clean.cmake
.PHONY : CMakeFiles/FunctionLoggerPass.dir/clean

CMakeFiles/FunctionLoggerPass.dir/depend:
	cd /home/<USER>/sqlite_loger/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/sqlite_loger/pass /home/<USER>/sqlite_loger/pass /home/<USER>/sqlite_loger/build /home/<USER>/sqlite_loger/build /home/<USER>/sqlite_loger/build/CMakeFiles/FunctionLoggerPass.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/FunctionLoggerPass.dir/depend

