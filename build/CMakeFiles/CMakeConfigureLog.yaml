
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake:233 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Linux - 6.8.0-51-generic - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/clang 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is Clang, found in:
        /home/<USER>/sqlite_loger/build/CMakeFiles/3.28.3/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/clang++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is Clang, found in:
        /home/<USER>/sqlite_loger/build/CMakeFiles/3.28.3/CompilerIdCXX/a.out
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-zxtq1i"
      binary: "/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-zxtq1i"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND"
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-zxtq1i'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_740e4/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_740e4.dir/build.make CMakeFiles/cmTC_740e4.dir/build
        gmake[1]: Entering directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-zxtq1i'
        Building C object CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o
        /usr/bin/clang   -v -MD -MT CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c
        Ubuntu clang version 18.1.3 (1ubuntu1)
        Target: x86_64-pc-linux-gnu
        Thread model: posix
        InstalledDir: /usr/bin
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Candidate multilib: .;@m64
        Selected multilib: .;@m64
         (in-process)
         "/usr/lib/llvm-18/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -fmath-errno -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu x86-64 -tune-cpu generic -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-zxtq1i -v -fcoverage-compilation-dir=/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-zxtq1i -resource-dir /usr/lib/llvm-18/lib/clang/18 -dependency-file CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -sys-header-deps -internal-isystem /usr/lib/llvm-18/lib/clang/18/include -internal-isystem /usr/local/include -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -ferror-limit 19 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -faddrsig -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -x c /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c
        clang -cc1 version 18.1.3 based upon LLVM 18.1.3 default target x86_64-pc-linux-gnu
        ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include"
        ignoring nonexistent directory "/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/lib/llvm-18/lib/clang/18/include
         /usr/local/include
         /usr/include/x86_64-linux-gnu
         /usr/include
        End of search list.
        Linking C executable cmTC_740e4
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_740e4.dir/link.txt --verbose=1
        /usr/bin/clang  -v CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -o cmTC_740e4 
        Ubuntu clang version 18.1.3 (1ubuntu1)
        Target: x86_64-pc-linux-gnu
        Thread model: posix
        InstalledDir: /usr/bin
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Candidate multilib: .;@m64
        Selected multilib: .;@m64
         "/usr/bin/ld" -z relro --hash-style=gnu --build-id --eh-frame-hdr -m elf_x86_64 -pie -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_740e4 /lib/x86_64-linux-gnu/Scrt1.o /lib/x86_64-linux-gnu/crti.o /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13 -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/lib -L/usr/lib CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o /lib/x86_64-linux-gnu/crtn.o
        gmake[1]: Leaving directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-zxtq1i'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/lib/llvm-18/lib/clang/18/include]
          add: [/usr/local/include]
          add: [/usr/include/x86_64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/lib/llvm-18/lib/clang/18/include] ==> [/usr/lib/llvm-18/lib/clang/18/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/lib/llvm-18/lib/clang/18/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:159 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld\\.lld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-zxtq1i']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_740e4/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_740e4.dir/build.make CMakeFiles/cmTC_740e4.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-zxtq1i']
        ignore line: [Building C object CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/clang   -v -MD -MT CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c]
        ignore line: [Ubuntu clang version 18.1.3 (1ubuntu1)]
        ignore line: [Target: x86_64-pc-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /usr/bin]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Candidate multilib: .]
        ignore line: [@m64]
        ignore line: [Selected multilib: .]
        ignore line: [@m64]
        ignore line: [ (in-process)]
        ignore line: [ "/usr/lib/llvm-18/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -fmath-errno -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu x86-64 -tune-cpu generic -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-zxtq1i -v -fcoverage-compilation-dir=/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-zxtq1i -resource-dir /usr/lib/llvm-18/lib/clang/18 -dependency-file CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -sys-header-deps -internal-isystem /usr/lib/llvm-18/lib/clang/18/include -internal-isystem /usr/local/include -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -ferror-limit 19 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -faddrsig -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -x c /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 18.1.3 based upon LLVM 18.1.3 default target x86_64-pc-linux-gnu]
        ignore line: [ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include"]
        ignore line: [ignoring nonexistent directory "/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/lib/llvm-18/lib/clang/18/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/x86_64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [Linking C executable cmTC_740e4]
        ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_740e4.dir/link.txt --verbose=1]
        ignore line: [/usr/bin/clang  -v CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -o cmTC_740e4 ]
        ignore line: [Ubuntu clang version 18.1.3 (1ubuntu1)]
        ignore line: [Target: x86_64-pc-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /usr/bin]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Candidate multilib: .]
        ignore line: [@m64]
        ignore line: [Selected multilib: .]
        ignore line: [@m64]
        link line: [ "/usr/bin/ld" -z relro --hash-style=gnu --build-id --eh-frame-hdr -m elf_x86_64 -pie -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_740e4 /lib/x86_64-linux-gnu/Scrt1.o /lib/x86_64-linux-gnu/crti.o /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13 -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/lib -L/usr/lib CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o /lib/x86_64-linux-gnu/crtn.o]
          arg [/usr/bin/ld] ==> ignore
          arg [-zrelro] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [-pie] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_740e4] ==> ignore
          arg [/lib/x86_64-linux-gnu/Scrt1.o] ==> obj [/lib/x86_64-linux-gnu/Scrt1.o]
          arg [/lib/x86_64-linux-gnu/crti.o] ==> obj [/lib/x86_64-linux-gnu/crti.o]
          arg [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o] ==> obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o]
          arg [-L/usr/bin/../lib/gcc/x86_64-linux-gnu/13] ==> dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13]
          arg [-L/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64] ==> dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64]
          arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
          arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
          arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
          arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
          arg [-L/lib] ==> dir [/lib]
          arg [-L/usr/lib] ==> dir [/usr/lib]
          arg [CMakeFiles/cmTC_740e4.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--no-as-needed] ==> ignore
          arg [-lc] ==> lib [c]
          arg [-lgcc] ==> lib [gcc]
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--no-as-needed] ==> ignore
          arg [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o] ==> obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o]
          arg [/lib/x86_64-linux-gnu/crtn.o] ==> obj [/lib/x86_64-linux-gnu/crtn.o]
        collapse obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o] ==> [/usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o]
        collapse obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o] ==> [/usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o]
        collapse library dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13] ==> [/usr/lib/gcc/x86_64-linux-gnu/13]
        collapse library dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64] ==> [/usr/lib64]
        collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
        collapse library dir [/lib/../lib64] ==> [/lib64]
        collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
        collapse library dir [/lib] ==> [/lib]
        collapse library dir [/usr/lib] ==> [/usr/lib]
        implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
        implicit objs: [/lib/x86_64-linux-gnu/Scrt1.o;/lib/x86_64-linux-gnu/crti.o;/usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o;/usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o;/lib/x86_64-linux-gnu/crtn.o]
        implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/13;/usr/lib64;/lib/x86_64-linux-gnu;/lib64;/usr/lib/x86_64-linux-gnu;/lib;/usr/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-3lwZeW"
      binary: "/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-3lwZeW"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-3lwZeW'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_7dd00/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_7dd00.dir/build.make CMakeFiles/cmTC_7dd00.dir/build
        gmake[1]: Entering directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-3lwZeW'
        Building CXX object CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/clang++   -v -MD -MT CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp
        Ubuntu clang version 18.1.3 (1ubuntu1)
        Target: x86_64-pc-linux-gnu
        Thread model: posix
        InstalledDir: /usr/bin
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Candidate multilib: .;@m64
        Selected multilib: .;@m64
         (in-process)
         "/usr/lib/llvm-18/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -fmath-errno -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu x86-64 -tune-cpu generic -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-3lwZeW -v -fcoverage-compilation-dir=/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-3lwZeW -resource-dir /usr/lib/llvm-18/lib/clang/18 -dependency-file CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13 -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13 -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward -internal-isystem /usr/lib/llvm-18/lib/clang/18/include -internal-isystem /usr/local/include -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdeprecated-macro -ferror-limit 19 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -faddrsig -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 18.1.3 based upon LLVM 18.1.3 default target x86_64-pc-linux-gnu
        ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include"
        ignoring nonexistent directory "/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13
         /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13
         /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward
         /usr/lib/llvm-18/lib/clang/18/include
         /usr/local/include
         /usr/include/x86_64-linux-gnu
         /usr/include
        End of search list.
        Linking CXX executable cmTC_7dd00
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7dd00.dir/link.txt --verbose=1
        /usr/bin/clang++  -v CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_7dd00 
        Ubuntu clang version 18.1.3 (1ubuntu1)
        Target: x86_64-pc-linux-gnu
        Thread model: posix
        InstalledDir: /usr/bin
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Candidate multilib: .;@m64
        Selected multilib: .;@m64
         "/usr/bin/ld" -z relro --hash-style=gnu --build-id --eh-frame-hdr -m elf_x86_64 -pie -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_7dd00 /lib/x86_64-linux-gnu/Scrt1.o /lib/x86_64-linux-gnu/crti.o /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13 -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/lib -L/usr/lib CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o /lib/x86_64-linux-gnu/crtn.o
        gmake[1]: Leaving directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-3lwZeW'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13]
          add: [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13]
          add: [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward]
          add: [/usr/lib/llvm-18/lib/clang/18/include]
          add: [/usr/local/include]
          add: [/usr/include/x86_64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13] ==> [/usr/include/c++/13]
        collapse include dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13] ==> [/usr/include/x86_64-linux-gnu/c++/13]
        collapse include dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward] ==> [/usr/include/c++/13/backward]
        collapse include dir [/usr/lib/llvm-18/lib/clang/18/include] ==> [/usr/lib/llvm-18/lib/clang/18/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/include/c++/13;/usr/include/x86_64-linux-gnu/c++/13;/usr/include/c++/13/backward;/usr/lib/llvm-18/lib/clang/18/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:159 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld\\.lld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-3lwZeW']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_7dd00/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_7dd00.dir/build.make CMakeFiles/cmTC_7dd00.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-3lwZeW']
        ignore line: [Building CXX object CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/clang++   -v -MD -MT CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Ubuntu clang version 18.1.3 (1ubuntu1)]
        ignore line: [Target: x86_64-pc-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /usr/bin]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Candidate multilib: .]
        ignore line: [@m64]
        ignore line: [Selected multilib: .]
        ignore line: [@m64]
        ignore line: [ (in-process)]
        ignore line: [ "/usr/lib/llvm-18/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -fmath-errno -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu x86-64 -tune-cpu generic -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-3lwZeW -v -fcoverage-compilation-dir=/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-3lwZeW -resource-dir /usr/lib/llvm-18/lib/clang/18 -dependency-file CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13 -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13 -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward -internal-isystem /usr/lib/llvm-18/lib/clang/18/include -internal-isystem /usr/local/include -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdeprecated-macro -ferror-limit 19 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -faddrsig -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 18.1.3 based upon LLVM 18.1.3 default target x86_64-pc-linux-gnu]
        ignore line: [ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include"]
        ignore line: [ignoring nonexistent directory "/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13]
        ignore line: [ /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13]
        ignore line: [ /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward]
        ignore line: [ /usr/lib/llvm-18/lib/clang/18/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/x86_64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [Linking CXX executable cmTC_7dd00]
        ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7dd00.dir/link.txt --verbose=1]
        ignore line: [/usr/bin/clang++  -v CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_7dd00 ]
        ignore line: [Ubuntu clang version 18.1.3 (1ubuntu1)]
        ignore line: [Target: x86_64-pc-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /usr/bin]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Candidate multilib: .]
        ignore line: [@m64]
        ignore line: [Selected multilib: .]
        ignore line: [@m64]
        link line: [ "/usr/bin/ld" -z relro --hash-style=gnu --build-id --eh-frame-hdr -m elf_x86_64 -pie -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_7dd00 /lib/x86_64-linux-gnu/Scrt1.o /lib/x86_64-linux-gnu/crti.o /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13 -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/lib -L/usr/lib CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o /lib/x86_64-linux-gnu/crtn.o]
          arg [/usr/bin/ld] ==> ignore
          arg [-zrelro] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [-pie] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_7dd00] ==> ignore
          arg [/lib/x86_64-linux-gnu/Scrt1.o] ==> obj [/lib/x86_64-linux-gnu/Scrt1.o]
          arg [/lib/x86_64-linux-gnu/crti.o] ==> obj [/lib/x86_64-linux-gnu/crti.o]
          arg [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o] ==> obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o]
          arg [-L/usr/bin/../lib/gcc/x86_64-linux-gnu/13] ==> dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13]
          arg [-L/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64] ==> dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64]
          arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
          arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
          arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
          arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
          arg [-L/lib] ==> dir [/lib]
          arg [-L/usr/lib] ==> dir [/usr/lib]
          arg [CMakeFiles/cmTC_7dd00.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o] ==> obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o]
          arg [/lib/x86_64-linux-gnu/crtn.o] ==> obj [/lib/x86_64-linux-gnu/crtn.o]
        collapse obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o] ==> [/usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o]
        collapse obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o] ==> [/usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o]
        collapse library dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13] ==> [/usr/lib/gcc/x86_64-linux-gnu/13]
        collapse library dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64] ==> [/usr/lib64]
        collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
        collapse library dir [/lib/../lib64] ==> [/lib64]
        collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
        collapse library dir [/lib] ==> [/lib]
        collapse library dir [/usr/lib] ==> [/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/lib/x86_64-linux-gnu/Scrt1.o;/lib/x86_64-linux-gnu/crti.o;/usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o;/usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o;/lib/x86_64-linux-gnu/crtn.o]
        implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/13;/usr/lib64;/lib/x86_64-linux-gnu;/lib64;/usr/lib/x86_64-linux-gnu;/lib;/usr/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/usr/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "/usr/lib/llvm-18/cmake/FindFFI.cmake:59 (check_c_source_compiles)"
      - "/usr/lib/llvm-18/cmake/LLVMConfig.cmake:279 (find_package)"
      - "CMakeLists.txt:4 (find_package)"
    checks:
      - "Performing Test HAVE_FFI_CALL"
    directories:
      source: "/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-B9QBN7"
      binary: "/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-B9QBN7"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND"
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/usr/lib/llvm-18/cmake"
    buildResult:
      variable: "HAVE_FFI_CALL"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-B9QBN7'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_bd9ac/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_bd9ac.dir/build.make CMakeFiles/cmTC_bd9ac.dir/build
        gmake[1]: Entering directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-B9QBN7'
        Building C object CMakeFiles/cmTC_bd9ac.dir/src.c.o
        /usr/bin/clang -DHAVE_FFI_CALL   -MD -MT CMakeFiles/cmTC_bd9ac.dir/src.c.o -MF CMakeFiles/cmTC_bd9ac.dir/src.c.o.d -o CMakeFiles/cmTC_bd9ac.dir/src.c.o -c /home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-B9QBN7/src.c
        Linking C executable cmTC_bd9ac
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_bd9ac.dir/link.txt --verbose=1
        /usr/bin/clang CMakeFiles/cmTC_bd9ac.dir/src.c.o -o cmTC_bd9ac  /usr/lib/x86_64-linux-gnu/libffi.so 
        gmake[1]: Leaving directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-B9QBN7'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "/usr/lib/llvm-18/cmake/FindLibEdit.cmake:28 (check_include_file)"
      - "/usr/lib/llvm-18/cmake/LLVMConfig.cmake:286 (find_package)"
      - "CMakeLists.txt:4 (find_package)"
    checks:
      - "Looking for histedit.h"
    directories:
      source: "/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-vNbJ6V"
      binary: "/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-vNbJ6V"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND"
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/usr/lib/llvm-18/cmake"
    buildResult:
      variable: "HAVE_HISTEDIT_H"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-vNbJ6V'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_ff69d/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_ff69d.dir/build.make CMakeFiles/cmTC_ff69d.dir/build
        gmake[1]: Entering directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-vNbJ6V'
        Building C object CMakeFiles/cmTC_ff69d.dir/CheckIncludeFile.c.o
        /usr/bin/clang    -MD -MT CMakeFiles/cmTC_ff69d.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_ff69d.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_ff69d.dir/CheckIncludeFile.c.o -c /home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-vNbJ6V/CheckIncludeFile.c
        Linking C executable cmTC_ff69d
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ff69d.dir/link.txt --verbose=1
        /usr/bin/clang CMakeFiles/cmTC_ff69d.dir/CheckIncludeFile.c.o -o cmTC_ff69d  /usr/lib/x86_64-linux-gnu/libedit.so 
        gmake[1]: Leaving directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-vNbJ6V'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/usr/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "/usr/lib/llvm-18/cmake/FindTerminfo.cmake:32 (check_c_source_compiles)"
      - "/usr/lib/llvm-18/cmake/LLVMConfig.cmake:291 (find_package)"
      - "CMakeLists.txt:4 (find_package)"
    checks:
      - "Performing Test Terminfo_LINKABLE"
    directories:
      source: "/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-I7SELR"
      binary: "/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-I7SELR"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND"
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/usr/lib/llvm-18/cmake"
    buildResult:
      variable: "Terminfo_LINKABLE"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-I7SELR'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_fe0fa/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_fe0fa.dir/build.make CMakeFiles/cmTC_fe0fa.dir/build
        gmake[1]: Entering directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-I7SELR'
        Building C object CMakeFiles/cmTC_fe0fa.dir/src.c.o
        /usr/bin/clang -DTerminfo_LINKABLE   -MD -MT CMakeFiles/cmTC_fe0fa.dir/src.c.o -MF CMakeFiles/cmTC_fe0fa.dir/src.c.o.d -o CMakeFiles/cmTC_fe0fa.dir/src.c.o -c /home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-I7SELR/src.c
        Linking C executable cmTC_fe0fa
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_fe0fa.dir/link.txt --verbose=1
        /usr/bin/clang CMakeFiles/cmTC_fe0fa.dir/src.c.o -o cmTC_fe0fa  /usr/lib/x86_64-linux-gnu/libtinfo.so 
        gmake[1]: Leaving directory '/home/<USER>/sqlite_loger/build/CMakeFiles/CMakeScratch/TryCompile-I7SELR'
        
      exitCode: 0
...
