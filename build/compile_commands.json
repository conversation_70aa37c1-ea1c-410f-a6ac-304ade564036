[{"directory": "/home/<USER>/sqlite_loger/build", "command": "/usr/bin/clang++ -DFunctionLoggerPass_EXPORTS -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -I/usr/lib/llvm-18/include -g -std=gnu++17 -fPIC -o CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.o -c /home/<USER>/sqlite_loger/pass/FunctionLogger.cpp", "file": "/home/<USER>/sqlite_loger/pass/FunctionLogger.cpp", "output": "CMakeFiles/FunctionLoggerPass.dir/FunctionLogger.cpp.o"}]