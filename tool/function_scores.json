{"sqlite3BtreeNext": {"call_count": 111755, "avg_time_ms": 1.049, "total_time_ms": 117271, "max_time_ms": 21, "normalized_score": 87.27}, "sqlite3MemCompare": {"call_count": 60943, "avg_time_ms": 1.053, "total_time_ms": 64182, "max_time_ms": 26, "normalized_score": 56.36}, "sqlite3VdbeSorterWrite": {"call_count": 45825, "avg_time_ms": 1.04, "total_time_ms": 47674, "max_time_ms": 64, "normalized_score": 52.43}, "sqlite3PagerGet": {"call_count": 6760, "avg_time_ms": 1.042, "total_time_ms": 7044, "max_time_ms": 38, "normalized_score": 24.05}, "sqlite3VdbeSorterInit": {"call_count": 2, "avg_time_ms": 1.5, "total_time_ms": 3, "max_time_ms": 2, "normalized_score": 20.31}, "sqlite3PagerPagecount": {"call_count": 3, "avg_time_ms": 1.333, "total_time_ms": 4, "max_time_ms": 2, "normalized_score": 18.09}, "sqlite3BtreeBeginTrans": {"call_count": 3, "avg_time_ms": 1.333, "total_time_ms": 4, "max_time_ms": 2, "normalized_score": 18.09}, "sqlite3PagerSharedLock": {"call_count": 4, "avg_time_ms": 1.25, "total_time_ms": 5, "max_time_ms": 2, "normalized_score": 16.98}, "sqlite3Malloc": {"call_count": 263, "avg_time_ms": 1.053, "total_time_ms": 277, "max_time_ms": 8, "normalized_score": 15.46}, "resolveExprStep": {"call_count": 14, "avg_time_ms": 1.071, "total_time_ms": 15, "max_time_ms": 2, "normalized_score": 14.61}, "sqlite3HashFind": {"call_count": 63, "avg_time_ms": 1.048, "total_time_ms": 66, "max_time_ms": 3, "normalized_score": 14.48}, "sqlite3DbMallocZero": {"call_count": 26, "avg_time_ms": 1.038, "total_time_ms": 27, "max_time_ms": 2, "normalized_score": 14.17}, "sqlite3_free": {"call_count": 301, "avg_time_ms": 1.01, "total_time_ms": 304, "max_time_ms": 3, "normalized_score": 14.12}, "sqlite3DbFree": {"call_count": 76, "avg_time_ms": 1.0, "total_time_ms": 76, "max_time_ms": 1, "normalized_score": 13.54}, "sqlite3DbRealloc": {"call_count": 43, "avg_time_ms": 1.0, "total_time_ms": 43, "max_time_ms": 1, "normalized_score": 13.52}, "sqlite3HashInsert": {"call_count": 32, "avg_time_ms": 1.0, "total_time_ms": 32, "max_time_ms": 1, "normalized_score": 13.51}, "sqlite3Realloc": {"call_count": 37, "avg_time_ms": 1.0, "total_time_ms": 37, "max_time_ms": 1, "normalized_score": 13.51}, "lookupName": {"call_count": 11, "avg_time_ms": 1.0, "total_time_ms": 11, "max_time_ms": 1, "normalized_score": 13.5}, "sqlite3ExprCodeTarget": {"call_count": 13, "avg_time_ms": 1.0, "total_time_ms": 13, "max_time_ms": 1, "normalized_score": 13.5}, "sqlite3VdbeExec": {"call_count": 11, "avg_time_ms": 1.0, "total_time_ms": 11, "max_time_ms": 1, "normalized_score": 13.5}, "sqlite3Step": {"call_count": 10, "avg_time_ms": 1.0, "total_time_ms": 10, "max_time_ms": 1, "normalized_score": 13.5}, "sqlite3_step": {"call_count": 10, "avg_time_ms": 1.0, "total_time_ms": 10, "max_time_ms": 1, "normalized_score": 13.5}, "sqlite3_str_vappendf": {"call_count": 7, "avg_time_ms": 1.0, "total_time_ms": 7, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3HashInit": {"call_count": 5, "avg_time_ms": 1.0, "total_time_ms": 5, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3MallocZero": {"call_count": 3, "avg_time_ms": 1.0, "total_time_ms": 3, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3FinishCoding": {"call_count": 8, "avg_time_ms": 1.0, "total_time_ms": 8, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3_finalize": {"call_count": 7, "avg_time_ms": 1.0, "total_time_ms": 7, "max_time_ms": 1, "normalized_score": 13.49}, "selectExpander": {"call_count": 5, "avg_time_ms": 1.0, "total_time_ms": 5, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3ExprCompare": {"call_count": 8, "avg_time_ms": 1.0, "total_time_ms": 8, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3ExprCollSeq": {"call_count": 4, "avg_time_ms": 1.0, "total_time_ms": 4, "max_time_ms": 1, "normalized_score": 13.49}, "whereScanNext": {"call_count": 4, "avg_time_ms": 1.0, "total_time_ms": 4, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3WhereCodeOneLoopStart": {"call_count": 3, "avg_time_ms": 1.0, "total_time_ms": 3, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3WhereBegin": {"call_count": 3, "avg_time_ms": 1.0, "total_time_ms": 3, "max_time_ms": 1, "normalized_score": 13.49}, "selectInnerLoop": {"call_count": 2, "avg_time_ms": 1.0, "total_time_ms": 2, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3BtreeCursor": {"call_count": 4, "avg_time_ms": 1.0, "total_time_ms": 4, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3Prepare": {"call_count": 6, "avg_time_ms": 1.0, "total_time_ms": 6, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3EndTable": {"call_count": 5, "avg_time_ms": 1.0, "total_time_ms": 5, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3BtreeCloseCursor": {"call_count": 4, "avg_time_ms": 1.0, "total_time_ms": 4, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3InitOne": {"call_count": 1, "avg_time_ms": 1.0, "total_time_ms": 1, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3VdbeHalt": {"call_count": 2, "avg_time_ms": 1.0, "total_time_ms": 2, "max_time_ms": 1, "normalized_score": 13.49}, "exprAnalyze": {"call_count": 1, "avg_time_ms": 1.0, "total_time_ms": 1, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3ExprIfFalse": {"call_count": 1, "avg_time_ms": 1.0, "total_time_ms": 1, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3WhereEnd": {"call_count": 1, "avg_time_ms": 1.0, "total_time_ms": 1, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3Select": {"call_count": 1, "avg_time_ms": 1.0, "total_time_ms": 1, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3ExprCode": {"call_count": 1, "avg_time_ms": 1.0, "total_time_ms": 1, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3BtreeFirst": {"call_count": 2, "avg_time_ms": 1.0, "total_time_ms": 2, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3LocateTable": {"call_count": 2, "avg_time_ms": 1.0, "total_time_ms": 2, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3LocateTableItem": {"call_count": 2, "avg_time_ms": 1.0, "total_time_ms": 2, "max_time_ms": 1, "normalized_score": 13.49}, "wherePathSatisfiesOrderBy": {"call_count": 3, "avg_time_ms": 1.0, "total_time_ms": 3, "max_time_ms": 1, "normalized_score": 13.49}, "wherePathSolver": {"call_count": 2, "avg_time_ms": 1.0, "total_time_ms": 2, "max_time_ms": 1, "normalized_score": 13.49}, "updateAccumulator": {"call_count": 1, "avg_time_ms": 1.0, "total_time_ms": 1, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3BtreeRollback": {"call_count": 2, "avg_time_ms": 1.0, "total_time_ms": 2, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3PagerClose": {"call_count": 1, "avg_time_ms": 1.0, "total_time_ms": 1, "max_time_ms": 1, "normalized_score": 13.49}, "sqlite3BtreeClose": {"call_count": 1, "avg_time_ms": 1.0, "total_time_ms": 1, "max_time_ms": 1, "normalized_score": 13.49}}