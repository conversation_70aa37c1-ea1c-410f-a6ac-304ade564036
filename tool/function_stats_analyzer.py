#!/usr/bin/env python3
"""
Function Call Statistics Analyzer with Normalized Scoring

This script analyzes function_calls.json file and generates statistics with normalized scores.
"""

import json
import math
from collections import defaultdict


class FunctionStatsAnalyzer:
    def __init__(self):
        self.function_stats = defaultdict(lambda: {
            'call_count': 0,
            'total_time_ms': 0,
            'max_time_ms': 0
        })

    def analyze_file(self, file_path):
        """Analyze the function_calls.json file and collect statistics."""
        print(f"Analyzing file: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                line_count = 0
                for line in f:
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        data = json.loads(line)
                        function_name = data.get('function', 'unknown')
                        execution_time = data.get('execution_time_ms', 0)

                        # Update statistics
                        stats = self.function_stats[function_name]
                        stats['call_count'] += 1
                        stats['total_time_ms'] += execution_time
                        stats['max_time_ms'] = max(stats['max_time_ms'], execution_time)

                        line_count += 1
                        if line_count % 10000 == 0:
                            print(f"Processed {line_count} lines...")

                    except json.JSONDecodeError:
                        continue

                print(f"Total lines processed: {line_count}")

        except FileNotFoundError:
            print(f"Error: File {file_path} not found!")
            return False
        except Exception as e:
            print(f"Error reading file: {e}")
            return False

        return True

    def calculate_scores(self):
        """Calculate average execution times and normalized scores."""
        # Calculate averages
        for stats in self.function_stats.values():
            if stats['call_count'] > 0:
                stats['avg_time_ms'] = stats['total_time_ms'] / stats['call_count']
            else:
                stats['avg_time_ms'] = 0

        # Get max values for normalization
        max_call_count = max(stats['call_count'] for stats in self.function_stats.values())
        max_avg_time = max(stats['avg_time_ms'] for stats in self.function_stats.values())
        max_total_time = max(stats['total_time_ms'] for stats in self.function_stats.values())
        max_max_time = max(stats['max_time_ms'] for stats in self.function_stats.values())

        # Calculate normalized scores and final score
        for stats in self.function_stats.values():
            # Normalize each metric to 0-1 range
            norm_call_count = stats['call_count'] / max_call_count if max_call_count > 0 else 0
            norm_avg_time = stats['avg_time_ms'] / max_avg_time if max_avg_time > 0 else 0
            norm_total_time = stats['total_time_ms'] / max_total_time if max_total_time > 0 else 0
            norm_max_time = stats['max_time_ms'] / max_max_time if max_max_time > 0 else 0

            # Calculate weighted score (you can adjust weights as needed)
            # Higher weight for total_time and call_count as they indicate overall impact
            score = (norm_call_count * 0.3 +
                    norm_avg_time * 0.2 +
                    norm_total_time * 0.4 +
                    norm_max_time * 0.1) * 100

            stats['normalized_score'] = round(score, 2)

    def save_scored_json(self, output_file='function_scores.json'):
        """Save statistics with normalized scores to JSON file, sorted by score."""
        self.calculate_scores()

        # Sort by normalized score (highest first)
        sorted_functions = sorted(
            self.function_stats.items(),
            key=lambda x: x[1]['normalized_score'],
            reverse=True
        )

        # Convert to ordered dict for JSON output
        output_data = {}
        for function_name, stats in sorted_functions:
            output_data[function_name] = {
                'call_count': stats['call_count'],
                'avg_time_ms': round(stats['avg_time_ms'], 3),
                'total_time_ms': stats['total_time_ms'],
                'max_time_ms': stats['max_time_ms'],
                'normalized_score': stats['normalized_score']
            }

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)

            print(f"Scored statistics saved to: {output_file}")
            print(f"Total unique functions: {len(output_data)}")

            # Show top 10 functions
            print("\nTop 10 functions by normalized score:")
            for i, (func_name, stats) in enumerate(list(output_data.items())[:10], 1):
                print(f"{i:2d}. {func_name:<35} (Score: {stats['normalized_score']:6.2f})")

        except Exception as e:
            print(f"Error saving scored JSON: {e}")


def main():
    """Main function to run the analyzer."""
    analyzer = FunctionStatsAnalyzer()

    if analyzer.analyze_file('../pass/function_calls.json'):
        analyzer.save_scored_json()
    else:
        print("Failed to analyze the file.")


if __name__ == "__main__":
    main()
