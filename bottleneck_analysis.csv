Rank,Function_Name,File,Line,Bottleneck_Score,Fan_In,Fan_Out,Cyclomatic_Complexity,Lines_of_Code,Parameters,Local_Variables,Nested_Depth,Call_Depth
1,sqlite3VdbeExec,vdbe.c,814,70.46,2,379,592,4244,1,0,8,37
2,sqlite3Pragma,pragma.c,409,46.60,0,270,313,1489,5,0,8,38
3,DbObj<PERSON>md,tclsqlite.c,1994,41.83,0,253,234,1235,4,0,6,42
4,sqlite3Select,select.c,7396,31.37,20,148,132,795,3,0,6,39
5,sqlite3WhereCodeOneLoopStart,wherecode.c,1470,29.04,1,117,154,912,6,0,8,33
6,sqlite3Update,update.c,285,27.61,2,76,124,581,8,0,6,46
7,sqlite3_free,malloc.c,412,26.41,381,2,3,14,1,0,1,2
8,sqlite3<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>int<PERSON><PERSON><PERSON>,insert.c,1898,25.86,2,79,113,594,13,0,6,41
9,sqlite3Insert,insert.c,891,25.46,1,79,96,490,6,0,5,45
10,sqlite3ExprCodeTarget,expr.c,4727,24.71,11,85,62,619,3,0,5,43
11,sqlite3CreateIndex,build.c,3895,21.46,2,65,91,418,11,0,5,35
12,lookupName,resolve.c,278,19.91,1,51,90,411,6,0,8,27
13,renameTableFunc,alter.c,1720,19.71,0,35,26,124,3,0,9,39
14,sqlite3WhereBegin,where.c,6644,19.55,9,51,68,375,8,0,4,35
15,sqlite3DeleteFrom,delete.c,288,19.41,2,45,47,269,5,0,4,41
16,sqlite3WhereEnd,where.c,7322,19.39,15,33,44,249,1,0,6,37
17,balance_nonroot,btree.c,8160,19.30,1,63,94,503,5,0,4,29
18,sqlite3WindowCodeStep,window.c,2786,19.22,1,65,40,270,5,0,4,38
19,sqlite3_str_vappendf,printf.c,176,18.97,8,35,182,635,3,0,7,12
20,sqlite3EndTable,build.c,2597,18.55,1,40,43,241,5,0,4,40
21,sqlite3ErrorMsg,util.c,242,18.38,193,5,4,25,3,0,2,14
22,sqlite3ExprCodeIN,expr.c,3914,18.34,4,36,28,177,4,0,4,42
23,selectExpander,select.c,6016,18.31,0,39,66,319,2,0,7,30
24,renameColumnFunc,alter.c,1495,18.24,0,43,33,138,3,0,5,39
25,isLikeOrGlob,whereexpr.c,178,18.13,0,14,20,117,5,0,6,44
26,wherePathSatisfiesOrderBy,where.c,4997,18.05,4,12,70,256,7,0,7,33
27,updateVirtualTable,update.c,1202,18.01,1,26,19,132,8,0,4,45
28,sqlite3_blob_open,vdbeblob.c,121,17.92,0,34,29,165,7,0,6,38
29,allocateBtreePage,btree.c,6434,17.48,10,56,49,236,5,0,7,25
30,codeRowTrigger,trigger.c,1235,17.48,1,19,10,86,4,0,3,48
31,exprAnalyze,whereexpr.c,1089,17.17,8,59,47,337,3,0,4,29
32,sqlite3CodeRowTrigger,trigger.c,1456,17.12,1,2,5,32,9,0,3,51
33,sqlite3FinishCoding,build.c,140,17.11,0,11,20,98,1,0,4,45
34,attachFunc,attach.c,74,16.91,0,39,33,152,3,0,4,37
35,wherePathSolver,where.c,5660,16.86,2,17,45,253,2,0,6,34
36,multiSelect,select.c,2884,16.74,1,53,31,270,3,0,4,33
37,sqlite3FkCheck,fkey.c,888,16.72,5,24,27,124,6,0,4,39
38,resolveExprStep,resolve.c,965,16.62,1,40,61,362,2,0,5,28
39,sqlite3ExprCode,expr.c,5631,16.46,31,3,4,21,3,0,2,44
40,sqlite3_backup_step,backup.c,314,16.24,1,31,32,174,2,0,5,34
41,codeTriggerProgram,trigger.c,1115,16.20,1,16,5,61,3,0,2,47
42,renameQuotefixFunc,alter.c,1906,16.03,0,21,12,70,3,0,5,39
43,selectInnerLoop,select.c,1106,15.98,8,28,48,258,8,0,4,31
44,sqlite3DropTable,build.c,3451,15.79,0,18,21,84,4,0,2,43
45,sqlite3CodeRowTriggerDirect,trigger.c,1384,15.75,3,3,2,21,6,0,1,50
46,sqlite3DbFree,malloc.c,516,15.66,210,1,2,4,2,0,1,4
47,sqlite3VdbeHalt,vdbeaux.c,3296,15.65,3,17,28,130,1,0,5,35
48,renameTableTest,alter.c,2019,15.62,0,15,11,50,3,0,5,39
49,sqlite3BtreeInsert,btree.c,9319,15.61,3,31,40,192,4,0,4,32
50,constructAutomaticIndex,where.c,981,15.59,1,24,36,218,4,0,4,34
51,codeVectorCompare,expr.c,693,15.54,1,15,11,84,5,0,3,42
52,sqlite3Prepare,prepare.c,681,15.51,0,19,20,113,7,0,4,38
53,jsonTranslateTextToBlob,json.c,1512,15.45,8,36,128,446,2,0,6,11
54,sqlite3MultiValues,insert.c,676,15.44,0,14,12,88,3,0,4,40
55,sqlite3FkDropTable,fkey.c,735,15.43,2,6,8,31,3,0,4,42
56,xferOptimization,insert.c,3015,15.42,1,38,60,277,5,0,4,27
57,sqlite3RunVacuum,vacuum.c,143,15.40,1,40,26,183,4,0,2,36
58,codeAttach,attach.c,339,15.39,0,11,8,54,7,0,2,45
59,sqlite3FindInIndex,expr.c,3146,15.37,4,22,34,162,6,0,7,28
60,getRowTrigger,trigger.c,1349,15.37,2,1,4,19,4,0,1,49
61,sqlite3GenerateRowDelete,delete.c,745,15.28,3,18,12,84,11,0,3,40
62,windowCodeOp,window.c,2235,15.25,28,17,21,122,4,0,4,31
63,convertToWithoutRowidTable,build.c,2316,15.04,1,14,27,120,2,0,4,36
64,sqlite3Vacuum,vacuum.c,105,15.01,0,6,7,22,3,0,2,45
65,delDatabaseRef,tclsqlite.c,594,14.99,3,21,16,58,1,0,2,40
66,sqlite3_declare_vtab,vtab.c,810,14.85,9,15,10,86,2,0,3,38
67,sqlite3Step,vdbeapi.c,753,14.71,0,7,18,78,1,0,4,38
68,sqlite3AddPrimaryKey,build.c,1801,14.67,0,12,12,70,5,0,5,36
69,fstreeFilter,test_fs.c,464,14.66,0,13,10,66,5,0,4,38
70,DbEvalNextCmd,tclsqlite.c,1880,14.63,0,4,7,42,3,0,3,42
71,sqlite3InitOne,prepare.c,198,14.62,3,22,23,139,4,0,3,35
72,whereScanNext,where.c,346,14.58,5,6,18,87,1,0,8,29
73,sqlite3AlterDropColumn,alter.c,2159,14.51,0,21,22,119,3,0,5,32
74,flattenSubquery,select.c,4349,14.50,1,20,55,256,4,0,4,28
75,exprAnalyzeOrTerm,whereexpr.c,668,14.47,1,27,30,186,3,0,6,27
76,sqlite3BtreeDelete,btree.c,9751,14.41,2,29,32,136,2,0,3,32
77,fkScanChildren,fkey.c,547,14.36,2,14,11,79,8,0,3,38
78,sqlite3_table_column_metadata,main.c,3900,14.36,1,15,18,82,9,0,3,37
79,multiSelectOrderBy,select.c,3482,14.35,1,42,25,237,3,0,4,28
80,exprCodeVector,expr.c,4401,14.35,3,4,4,21,3,0,3,41
81,dbEvalStep,tclsqlite.c,1731,14.35,1,8,8,39,1,0,3,40
82,echoDeclareVtab,test8.c,331,14.34,1,3,8,36,2,0,4,39
83,updateFromSelect,update.c,187,14.30,2,12,12,66,8,0,2,40
84,pragmaVtabFilter,pragma.c,2944,14.21,0,12,8,45,5,0,3,39
85,sqlite3CodeSubselect,expr.c,3745,14.19,5,7,10,83,2,0,2,40
86,sqlite3_step,vdbeapi.c,895,13.98,5,9,6,35,1,0,3,38
87,sqlite3CreateView,build.c,2947,13.77,0,12,9,61,8,0,1,41
88,echoConstructor,test8.c,396,13.74,0,8,8,46,6,0,2,40
89,sqlite3WhereRightJoinLoop,wherecode.c,2844,13.74,1,13,12,97,3,0,3,36
90,blobSeekToRow,vdbeblob.c,54,13.70,0,11,10,51,3,0,2,39
91,pragmaVtabConnect,pragma.c,2782,13.70,0,13,7,56,6,0,2,39
92,updateAccumulator,select.c,6857,13.68,2,17,30,144,4,0,4,30
93,sqlite3CodeRhsOfIN,expr.c,3501,13.68,2,36,26,163,3,0,4,27
94,jsonExtractFunc,json.c,3827,13.65,0,40,21,97,3,0,5,26
95,DbTransPostCmd,tclsqlite.c,1289,13.63,0,5,3,28,3,0,2,41
96,dropColumnFunc,alter.c,2094,13.58,0,12,6,46,3,0,2,39
97,jsonTranslateBlobToText,json.c,2125,13.57,9,36,52,207,3,0,4,21
98,tclNext,test_bestindex.c,269,13.56,0,4,4,17,1,0,3,39
99,sqlite3ExprIfFalse,expr.c,5990,13.54,23,28,14,137,4,0,2,29
100,sqlite3_finalize,vdbeapi.c,99,13.48,13,8,4,18,1,0,2,37
101,sqlite3Reindex,build.c,5552,13.47,0,17,11,49,3,0,2,37
102,resolveSelectStep,resolve.c,1849,13.45,0,16,35,159,2,0,4,29
103,dbReleaseStmt,tclsqlite.c,1554,13.38,3,5,6,35,3,0,2,39
104,fkActionTrigger,fkey.c,1215,13.37,1,28,24,166,4,0,5,26
105,btreeCreateTable,btree.c,9964,13.35,1,24,17,93,3,0,3,32
106,dbEvalFinalize,tclsqlite.c,1795,13.29,3,6,3,14,1,0,1,41
107,sqlite3ExprCompare,expr.c,6245,13.24,28,7,30,82,4,0,4,25
108,valueFromExpr,vdbemem.c,1599,13.20,3,20,28,130,6,0,4,28
109,codeINTerm,wherecode.c,664,13.18,1,12,21,120,6,0,5,29
110,autoVacuumCommit,btree.c,4156,13.15,1,14,13,71,1,0,4,32
111,balance,btree.c,9040,13.15,3,12,13,70,1,0,5,30
112,sqlite3BtreeOpen,btree.c,2522,13.14,0,26,42,207,6,0,6,21
113,sqlite3BtreeIndexMoveto,btree.c,5959,13.14,6,23,23,169,3,0,4,27
114,accessPayload,btree.c,5079,13.11,5,21,22,134,5,0,5,26
115,sqlite3MaterializeView,delete.c,142,13.08,2,9,2,29,6,0,1,40
116,blobReadWrite,vdbeblob.c,382,13.06,0,9,5,36,5,0,2,38
117,btreeDropTable,btree.c,10238,13.05,1,19,11,61,3,0,3,33
118,resolveCompoundOrderBy,resolve.c,1595,13.01,1,11,21,88,2,0,6,27
119,windowAggStep,window.c,1658,13.01,3,17,16,95,5,0,5,28
120,sqlite3Init,prepare.c,437,12.96,3,3,7,24,2,0,3,36
121,sqlite3ConstructBloomFilter,where.c,1261,12.95,1,13,14,101,4,0,3,33
122,echoNext,test8.c,587,12.95,0,3,4,17,1,0,2,39
123,exprCodeSubselect,expr.c,627,12.90,2,1,2,7,2,0,1,41
124,isDistinctRedundant,where.c,631,12.88,1,4,14,34,4,0,4,33
125,whereShortCut,where.c,6167,12.86,1,4,18,80,1,0,4,32
126,analyzeAggregate,expr.c,7077,12.85,0,8,27,129,2,0,6,26
127,sqlite3ExprCodeGetColumnOfTable,expr.c,4288,12.82,19,8,6,47,5,0,3,31
128,sqlite3ExprCollSeq,expr.c,246,12.74,17,4,14,59,2,0,6,25
129,jsonReturnFromBlob,json.c,3030,12.73,4,18,38,145,4,0,4,25
130,fsConnect,test_fs.c,613,12.70,0,10,2,28,6,0,1,39
131,schemaIsValid,prepare.c,491,12.70,1,5,8,31,1,0,3,35
132,DbMain,tclsqlite.c,3795,12.70,0,36,34,146,4,0,3,25
133,codeAllEqualityTerms,wherecode.c,897,12.69,2,8,14,88,5,0,4,31
134,vlogConnect,test_osinst.c,863,12.67,0,8,3,42,6,0,1,39
135,sqlite3_reset,vdbeapi.c,128,12.67,3,6,3,17,1,0,2,37
136,fkLookupParent,fkey.c,320,12.64,2,20,12,97,9,0,5,27
137,sqlite3ExprIfTrue,expr.c,5847,12.64,14,28,14,129,4,0,2,28
138,analyzeOneTable,analyze.c,976,12.61,2,31,20,165,6,0,3,27
139,resolveFromTermToCte,select.c,5729,12.60,1,17,27,154,3,0,5,25
140,renameParseCleanup,alter.c,1454,12.59,5,7,3,16,1,0,1,38
141,sqlite3BtreeTransferRow,btree.c,9637,12.52,1,20,18,90,3,0,5,26
142,sqlite3ReadSchema,prepare.c,469,12.51,4,1,4,15,1,0,2,37
143,incrVacuumStep,btree.c,3992,12.51,1,16,16,74,4,0,4,29
144,reindexTable,build.c,5499,12.50,2,4,4,12,3,0,3,35
145,pushDownWhereTerms,select.c,5195,12.48,2,12,19,82,5,0,5,27
146,test_table_column_metadata,test1.c,1715,12.46,0,8,5,41,4,0,1,38
147,sqlite3BeginTrigger,trigger.c,103,12.44,0,29,29,156,10,0,3,26
148,generateWithRecursiveQuery,select.c,2635,12.42,1,17,16,111,3,0,2,32
149,fstreeNext,test_fs.c,444,12.41,0,5,2,14,1,0,1,39
150,windowReturnOneRow,window.c,1922,12.40,2,13,9,65,1,0,4,30
151,flushStmtCache,tclsqlite.c,569,12.39,3,1,2,11,1,0,1,39
152,freePage2,btree.c,6756,12.39,2,27,18,85,3,0,4,26
153,fstreeConnect,test_fs.c,347,12.37,0,4,2,20,6,0,1,39
154,fsdirConnect,test_fs.c,141,12.37,0,4,2,19,6,0,1,39
155,sqlite3BtreeTripAllCursors,btree.c,4425,12.36,2,6,6,26,3,0,5,30
156,sqlite3WindowRewrite,window.c,958,12.35,1,27,16,121,2,0,3,28
157,echoFilter,test8.c,663,12.34,0,7,4,31,5,0,1,38
158,sqlite3_mprintf,printf.c,1246,12.34,112,4,2,9,2,0,1,14
159,intarrayCreate,test_intarray.c,84,12.31,0,3,2,18,6,0,1,39
160,sqlite3VdbeReset,vdbeaux.c,3567,12.31,3,3,5,19,1,0,2,36
161,sqlite3BtreeCopyFile,backup.c,718,12.30,1,11,4,33,2,0,2,35
162,jsonEachConnect,json.c,4809,12.28,0,2,2,25,6,0,1,39
163,sqlite3SubqueryColumnTypes,select.c,2324,12.27,3,13,18,86,4,0,5,26
164,pragmaVtabNext,pragma.c,2926,12.24,0,2,2,12,1,0,1,39
165,sqlite3RefillIndex,build.c,3728,12.22,3,19,7,70,3,0,1,34
166,sqlite3RollbackAll,main.c,1461,12.17,0,9,6,31,2,0,3,33
167,sqlite3ProcessJoin,select.c,496,12.17,1,22,21,107,2,0,6,22
168,vdbeCloseStatement,vdbeaux.c,3203,12.16,1,4,10,39,2,0,3,33
169,DbDeleteCmd,tclsqlite.c,657,12.16,0,1,1,4,1,0,0,41
170,btreeBeginTrans,btree.c,3583,12.15,1,13,30,111,3,0,4,26
171,pragmaVtabCursorClear,pragma.c,2906,12.14,3,2,2,10,1,0,1,38
172,wherePartIdxExpr,where.c,3793,12.13,2,7,12,48,6,0,5,28
173,sqlite3_exec,legacy.c,30,12.11,19,11,20,95,5,0,6,20
174,sqlite3UpsertAnalyzeTarget,upsert.c,90,12.10,1,9,21,105,4,0,5,26
175,sqlite3AlterRenameTable,alter.c,124,12.08,0,23,16,109,3,0,2,30
176,sqlite3BtreeCommitPhaseOne,btree.c,4267,12.08,2,6,5,20,2,0,3,33
177,sqlite3BtreeIncrVacuum,btree.c,4119,12.05,1,11,6,30,1,0,3,32
178,reindexDatabases,build.c,5520,12.05,2,1,3,15,2,0,2,36
179,pushOntoSorter,select.c,697,12.04,5,17,15,106,7,0,2,30
180,sqlite3VdbeFinalize,vdbeaux.c,3658,11.98,5,2,2,12,1,0,1,37
181,sqlite3ExprDup,expr.c,1810,11.96,68,1,2,4,3,0,0,25
182,sqlite3PagerOpen,pager.c,4703,11.94,0,30,29,202,7,0,5,20
183,tclClose,test_bestindex.c,260,11.94,0,2,2,8,1,0,1,38
184,sqlite3Analyze,analyze.c,1456,11.93,0,11,12,40,3,0,4,29
185,sqlite3ComputeGeneratedColumns,insert.c,282,11.92,5,7,14,74,3,0,4,28
186,fillInCell,btree.c,6994,11.91,1,17,19,117,4,0,4,26
187,sqlite3BtreeSavepoint,btree.c,4572,11.90,3,6,6,25,3,0,3,32
188,sqlite3_snprintf,printf.c,1286,11.85,109,4,2,11,4,0,1,13
189,sqlite3GenerateRowIndexDelete,delete.c,899,11.85,3,4,6,32,6,0,2,34
190,sqlite3GenerateIndexKey,delete.c,964,11.84,4,5,9,48,8,0,2,33
191,sqlite3PagerCommitPhaseOne,pager.c,6432,11.77,4,15,16,73,3,0,4,26
192,vdbeCommit,vdbeaux.c,2907,11.72,1,21,21,162,2,0,4,24
193,codeReturningTrigger,trigger.c,1030,11.71,1,12,9,74,4,0,4,28
194,clearDatabasePage,btree.c,10121,11.70,3,16,15,52,4,0,3,28
195,tclvarConnect,test_tclvar.c,76,11.67,0,2,1,22,6,0,0,39
196,whereLoopAddBtreeIndex,where.c,3112,11.66,1,3,6,279,4,0,2,32
197,sqlite3WhereFindTerm,where.c,544,11.65,3,2,5,25,6,0,3,32
198,pragmaVtabClose,pragma.c,2918,11.63,0,2,1,6,1,0,0,39
199,removeUnindexableInClauseTerms,wherecode.c,571,11.61,1,8,13,63,4,0,5,26
200,valueFromValueList,vdbeapi.c,1014,11.60,2,10,8,49,3,0,3,30
201,fsNext,test_fs.c,675,11.58,0,1,1,7,1,0,0,39
202,substExpr,select.c,3861,11.58,7,18,15,94,2,0,3,26
203,dbFreeStmt,tclsqlite.c,556,11.53,3,2,1,4,1,0,0,38
204,windowFullScan,window.c,1816,11.52,1,22,11,78,1,0,2,29
205,sqlite3BtreeRollback,btree.c,4476,11.49,1,9,8,38,3,0,2,32
206,exprDup,expr.c,1589,11.49,3,17,20,114,4,0,4,24
207,saveCursorsOnList,btree.c,823,11.48,1,2,5,21,3,0,4,30
208,sqlite3ExprCodeExprList,expr.c,5700,11.47,9,5,8,48,5,0,4,27
209,groupConcatStep,func.c,2080,11.47,0,12,14,62,3,0,6,23
210,vdbeColumnFromOverflow,vdbe.c,717,11.43,1,12,9,67,7,0,3,29
211,fsClose,test_fs.c,667,11.39,0,3,1,7,1,0,0,38
212,fstreeClose,test_fs.c,433,11.39,0,3,1,7,1,0,0,38
213,filterPullDown,wherecode.c,1396,11.28,2,5,6,45,5,0,2,32
214,relocatePage,btree.c,3898,11.28,3,11,11,56,6,0,3,28
215,btree_insert,test3.c,613,11.26,0,14,4,37,4,0,1,33
216,sqlite3BtreeClearTable,btree.c,10188,11.21,4,5,3,15,3,0,2,32
217,sqlite3FkLocateIndex,fkey.c,183,11.19,7,7,24,72,5,0,7,18
218,sqlite3PagerWrite,pager.c,6201,11.14,39,3,5,17,1,0,2,24
219,jsonEachColumn,json.c,5050,11.11,0,13,21,95,3,0,3,26
220,saveAllCursors,btree.c,806,11.11,9,1,5,11,3,0,2,31
221,balance_quick,btree.c,7922,11.11,1,12,9,54,3,0,3,28
222,whereLoopOutputAdjust,where.c,2948,11.09,0,2,17,58,3,0,5,25
223,sqlite3BtreeCommit,btree.c,4388,11.07,3,4,2,10,1,0,1,34
224,selectWindowRewriteExprCb,window.c,748,11.06,0,5,18,71,2,0,4,26
225,multiSelectValues,select.c,2811,11.06,1,2,7,29,3,0,2,32
226,sqlite3BtreeSetVersion,btree.c,11412,11.05,2,3,6,23,2,0,4,28
227,viewGetColumnNames,build.c,3043,11.04,1,10,9,71,2,0,3,28
228,sqlite3VdbeMemTranslate,utf.c,213,11.02,1,3,31,102,2,0,5,22
229,resolveOrderGroupBy,resolve.c,1786,10.99,2,8,10,46,4,0,3,28
230,whereScanInit,where.c,480,10.94,4,2,6,38,6,0,2,31
231,sqlite3ExprCodeTemp,expr.c,5603,10.94,22,5,3,22,3,0,2,27
232,jsonLookupStep,json.c,2814,10.93,11,33,48,182,4,0,5,11
233,openDatabase,main.c,3222,10.91,1,27,27,172,4,0,2,23
234,sqlite3ExprCodeGetColumn,expr.c,4345,10.91,1,2,4,19,6,0,2,32
235,resetAccumulator,select.c,6717,10.90,2,5,11,59,2,0,3,28
236,vtabCallConstructor,vtab.c,556,10.90,3,20,17,114,5,0,5,20
237,indexMightHelpWithOrderBy,where.c,3543,10.89,0,2,13,31,3,0,4,27
238,exprListAppendList,window.c,892,10.87,5,5,8,32,4,0,4,26
239,multiplexOpen,test_multiplex.c,469,10.87,0,11,21,106,5,0,8,16
240,clearCellOverflow,btree.c,6899,10.83,3,10,9,50,3,0,3,27
241,sqlite3FkActions,fkey.c,1415,10.83,3,4,5,20,6,0,4,27
242,multiSelectOrderByKeyInfo,select.c,2569,10.82,2,4,6,26,3,0,4,27
243,sqlite3ExprIsInteger,expr.c,2809,10.78,10,5,11,47,3,0,4,24
244,sqlite3VdbeCloseStatement,vdbeaux.c,3249,10.77,1,1,2,6,2,0,1,34
245,insertCell,btree.c,7251,10.71,3,8,8,63,6,0,3,27
246,vdbeSorterSetupMerge,vdbesort.c,2501,10.71,1,10,11,60,1,0,6,21
247,sqlite3StartTable,build.c,1184,10.70,2,21,24,133,7,0,3,22
248,exprMightBeIndexed2,whereexpr.c,1006,10.70,1,2,7,28,4,0,4,27
249,sqlite3ExpandReturning,trigger.c,921,10.67,1,6,8,36,3,0,4,26
250,sqlite3ParseUri,main.c,2967,10.65,0,25,28,172,6,0,6,15
251,jsonReturnString,json.c,791,10.65,10,10,9,40,3,0,4,23
252,parseModifier,date.c,726,10.63,0,49,74,286,5,0,5,5
253,sqlite3WindowCodeInit,window.c,1390,10.61,1,3,8,61,2,0,3,28
254,sqlite3_serialize,memdb.c,750,10.59,0,14,17,78,4,0,5,21
255,sqlite3VdbeMemCast,vdbemem.c,814,10.59,2,7,9,41,3,0,3,27
256,saveCursorKey,btree.c,714,10.58,2,6,4,26,1,0,3,28
257,applyAffinity,vdbe.c,398,10.57,6,3,9,27,3,0,4,25
258,jsonTranslateBlobToPrettyText,json.c,2374,10.57,3,20,11,68,2,0,4,22
259,ptrmapPut,btree.c,1060,10.53,16,11,8,42,5,0,2,25
260,whereLoopAddVirtualOne,where.c,4227,10.52,0,17,21,144,8,0,4,21
261,sqlite3_db_status,status.c,202,10.51,0,19,18,149,5,0,4,21
262,sqlite3CreateForeignKey,build.c,3573,10.51,0,18,21,116,5,0,4,21
263,substSelect,select.c,3975,10.49,3,7,6,27,3,0,3,27
264,sqlite3PagerMovepage,pager.c,7125,10.49,1,14,12,67,4,0,3,25
265,sqlite3AddColumn,build.c,1463,10.47,0,21,18,85,3,0,4,21
266,agginfoPersistExprCb,expr.c,6928,10.45,0,4,7,32,2,0,4,26
267,sqlite3ColumnDefault,update.c,61,10.44,2,3,4,22,4,0,2,30
268,lockBtree,btree.c,3267,10.43,0,17,21,106,1,0,4,21
269,balance_deeper,btree.c,8959,10.43,1,9,4,34,2,0,2,29
270,pager_incr_changecounter,pager.c,6284,10.42,3,5,7,39,2,0,4,25
271,sqlite3UpsertDoUpdate,upsert.c,267,10.41,2,11,6,57,5,0,3,26
272,exprMightBeIndexed,whereexpr.c,1034,10.41,2,1,6,29,4,0,3,28
273,sqlite3ExprListDup,expr.c,1814,10.41,25,5,7,43,3,0,4,20
274,propagateConstants,select.c,5015,10.40,0,4,4,38,2,0,2,30
275,sqlite3ExprCodeLoadIndexColumn,expr.c,4235,10.40,3,2,2,19,5,0,1,32
276,test_carray_bind,test1.c,4378,10.39,0,36,46,201,4,0,3,15
277,windowCodeRangeTest,window.c,2103,10.39,0,17,6,78,6,0,2,27
278,sqlite3ExprCodeRunJustOnce,expr.c,5543,10.38,4,6,9,46,3,0,3,26
279,sqlite3ColumnsFromExprList,select.c,2205,10.38,4,17,21,96,4,0,4,20
280,countOfViewOptimization,select.c,7186,10.38,1,8,23,67,2,0,2,26
281,testFunc,test1.c,2160,10.37,0,17,9,31,3,0,3,25
282,insertCellFast,btree.c,7348,10.36,1,5,7,55,4,0,3,27
283,sqlite3DbMallocZero,malloc.c,614,10.35,61,2,2,7,2,0,1,19
284,sqlite3VdbeIdxRowid,vdbeaux.c,5168,10.33,1,9,7,49,3,0,1,30
285,sqlite3_vtab_rhs_value,where.c,4463,10.33,0,3,4,29,3,0,2,30
286,renameEditSql,alter.c,1186,10.31,0,19,11,78,5,0,5,20
287,exprImpliesNotNull,expr.c,6379,10.31,8,9,9,71,5,0,2,26
288,sqlite3BtreePutData,btree.c,11353,10.30,0,2,4,25,4,0,1,32
289,sqlite3BtreeDropTable,btree.c,10323,10.28,1,3,1,7,3,0,0,34
290,sqlite3ResolveOrderGroupBy,resolve.c,1708,10.28,3,3,6,28,4,0,3,27
291,jsonArrayCompute,json.c,4610,10.26,2,9,7,33,2,0,4,24
292,jsonObjectCompute,json.c,4730,10.26,2,9,7,33,2,0,4,24
293,renameResolveTrigger,alter.c,1306,10.25,4,5,3,93,1,0,1,30
294,sqlite3IndexedExprLookup,expr.c,4617,10.25,1,7,9,51,3,0,3,26
295,btreeRestoreCursorPosition,btree.c,896,10.24,2,3,6,25,1,0,2,29
296,test_create_function_v2,test1.c,1946,10.24,0,19,21,77,4,0,2,24
297,jsonEachFilter,json.c,5224,10.24,0,21,12,96,5,0,4,21
298,sqlite3TriggerStepSrc,trigger.c,860,10.24,4,7,5,35,2,0,3,26
299,fstreeColumn,test_fs.c,560,10.21,0,7,5,25,3,0,4,25
300,resolveAlias,resolve.c,68,10.21,2,8,6,38,5,0,3,26
301,sqlite3_aggregate_context,vdbeapi.c,1130,10.21,46,1,2,10,2,0,1,22
302,createCollation,main.c,2755,10.20,2,6,9,51,6,0,5,22
303,btreeOverwriteOverflowCell,btree.c,9218,10.19,1,8,7,41,2,0,3,26
304,sqlite3SrcListDup,expr.c,1866,10.17,6,12,14,64,3,0,4,21
305,constInsert,select.c,4811,10.16,2,5,8,36,4,0,2,28
306,sqlite3SrcListAppendFromTerm,build.c,5024,10.16,2,7,12,58,7,0,3,25
307,codeDistinct,select.c,900,10.15,2,7,5,52,6,0,3,26
308,sqlite3LoadExtension,loadext.c,543,10.15,1,23,25,111,4,0,3,20
309,sqlite3OpenTableAndIndices,insert.c,2873,10.11,0,5,9,52,8,0,2,28
310,computeLimitRegisters,select.c,2486,10.10,5,7,7,39,3,0,3,25
311,xferCompatibleIndex,insert.c,2954,10.10,2,3,10,33,2,0,3,26
312,sqlite3PagerSharedLock,pager.c,5221,10.09,3,17,23,104,1,0,5,17
313,propagateConstantExprRewrite,select.c,4944,10.07,0,4,5,19,2,0,3,27
314,exprNodeIsConstantOrGroupBy,expr.c,2700,10.07,0,4,5,18,2,0,3,27
315,sumStep,func.c,1823,10.06,0,14,6,35,3,0,4,23
316,copyNodeContent,btree.c,8078,10.06,2,5,6,27,3,0,2,28
317,findIndexCol,where.c,578,10.06,1,3,4,24,5,0,3,27
318,sqlite3BtreeCreateTable,btree.c,10109,10.05,2,3,1,7,3,0,0,33
319,btreeOverwriteContent,btree.c,9174,10.05,4,6,9,31,5,0,3,25
320,codeEqualityTerm,wherecode.c,808,10.04,4,3,4,29,6,0,1,30
321,sqlite3_test_control,main.c,4100,10.03,3,19,13,184,2,0,3,21
322,sqlite3BtreeIntegrityCheck,btree.c,11051,10.01,0,30,22,117,8,0,4,17
323,sqlite3CorruptError,main.c,3853,9.99,147,0,1,4,1,0,0,1
324,btreeMoveto,btree.c,860,9.98,2,6,3,27,5,0,2,28
325,whereIsCoveringIndexWalkCallback,where.c,3657,9.97,0,1,6,25,2,0,3,27
326,sqlite3FinishTrigger,trigger.c,317,9.96,0,21,13,84,3,0,4,20
327,sqlite3VdbeIdxKeyCompare,vdbeaux.c,5247,9.95,1,6,3,27,4,0,1,30
328,test_translate,test5.c,121,9.95,0,10,6,48,4,0,2,27
329,sqlite3KeyInfoFromExprList,select.c,1580,9.93,9,2,3,22,4,0,2,27
330,checkTreePage,btree.c,10765,9.93,3,32,29,188,4,0,3,16
331,sqlite3KeyInfoOfIndex,build.c,5613,9.91,4,4,8,32,2,0,3,25
332,sqlite3GetVdbe,select.c,2450,9.89,54,1,3,11,1,0,1,19
333,sqlite3ViewGetColumnNames,build.c,3166,9.89,10,1,2,5,2,0,1,29
334,sqlite3VdbeSorterWrite,vdbesort.c,1768,9.89,1,6,14,81,2,0,3,24
335,sqlite3BtreePayloadChecked,btree.c,5318,9.88,0,2,2,8,4,0,1,31
336,sqlite3BtreeClearTableOfCursor,btree.c,10214,9.87,1,1,1,3,1,0,0,33
337,sqlite3VdbeMemFromBtree,vdbemem.c,1271,9.86,2,3,4,23,4,0,2,28
338,sqlite3ExprForVectorField,expr.c,570,9.85,3,1,5,31,4,0,3,26
339,findConstInWhere,select.c,4859,9.85,3,4,7,25,2,0,1,29
340,whereUsablePartialIndex,where.c,3579,9.84,1,2,6,28,4,0,2,28
341,sqlite3LeaveMutexAndCloseZombie,main.c,1342,9.84,4,24,14,71,1,0,3,20
342,setChildPtrmaps,btree.c,3789,9.83,3,6,5,24,1,0,2,27
343,getPageNormal,pager.c,5502,9.83,1,11,14,82,4,0,4,21
344,whereRangeVectorLen,where.c,3038,9.80,0,6,8,40,5,0,2,27
345,jsonParseFuncArg,json.c,3413,9.79,1,21,20,96,3,0,3,20
346,sqlite3ExprListAppendVector,expr.c,2082,9.79,0,5,9,41,4,0,2,27
347,sqlite3ExprIfFalseDup,expr.c,6163,9.77,2,3,2,8,4,0,1,30
348,sqlite3BitvecBuiltinTest,bitvec.c,337,9.74,1,16,14,59,2,0,4,20
349,trimFunc,func.c,1474,9.74,0,8,25,79,3,0,5,18
350,sqlite3WhereExprAnalyze,whereexpr.c,1848,9.73,3,1,2,9,2,0,1,30
351,exprCodeBetween,expr.c,5775,9.72,3,10,3,48,5,0,2,26
352,valueToText,vdbemem.c,1326,9.72,2,3,8,32,2,0,3,25
353,sqlite3_vtab_collation,where.c,4423,9.72,1,2,4,14,2,0,2,28
354,sqlite3VdbeMemFromBtreeZeroOffset,vdbemem.c,1294,9.72,6,2,2,20,3,0,1,29
355,accessPayloadChecked,btree.c,5304,9.71,1,2,3,14,4,0,1,30
356,fsColumn,test_fs.c,717,9.69,0,8,4,34,3,0,3,25
357,sqlite3WindowUpdate,window.c,659,9.66,1,8,6,70,4,0,2,26
358,sqlite3LocateTable,build.c,403,9.66,9,7,11,42,4,0,3,22
359,windowExprGtZero,window.c,2439,9.65,0,3,2,11,2,0,1,30
360,sqlite3BinaryCompareCollSeq,expr.c,420,9.64,7,4,4,19,3,0,2,26
361,replaceFunc,func.c,1381,9.62,0,16,12,85,3,0,5,18
362,sqlite3SelectDup,expr.c,1945,9.60,12,12,5,41,3,0,2,23
363,whereCombineDisjuncts,whereexpr.c,541,9.59,1,5,10,36,4,0,2,26
364,sqlite3AddCollateType,build.c,1912,9.59,0,5,6,22,2,0,3,25
365,sqlite3AlterRenameColumn,alter.c,596,9.59,0,16,10,63,4,0,2,24
366,sqlite3AddDefaultValue,build.c,1701,9.58,0,10,5,37,4,0,2,26
367,sqlite3VdbeMemSetStr,vdbemem.c,1161,9.57,6,9,16,79,5,0,3,21
368,whereAddIndexedExpr,where.c,6484,9.57,1,6,8,40,4,0,2,26
369,ptrmapPutOvflPtr,btree.c,1577,9.57,6,4,4,16,4,0,2,26
370,sqlite3RunParser,tokenize.c,599,9.56,4,15,24,114,2,0,4,17
371,propagateConstantExprRewriteOne,select.c,4894,9.56,3,2,10,32,3,0,2,26
372,sqlite3AlterFinishAddColumn,alter.c,313,9.55,0,19,17,119,2,0,3,20
373,saveCursorPosition,btree.c,756,9.54,2,2,4,21,1,0,1,29
374,allocateIndexInfo,where.c,1401,9.54,0,7,37,177,5,0,5,15
375,sqlite3BtreeCloseCursor,btree.c,4784,9.52,2,7,6,32,1,0,4,22
376,convertCompoundSelectToSubquery,select.c,5572,9.51,0,4,10,50,2,0,2,26
377,sqlite3DbMallocRawNN,malloc.c,665,9.51,43,2,9,37,2,0,2,17
378,strftimeFunc,date.c,1400,9.48,0,43,46,157,3,0,3,11
379,walFrames,wal.c,3994,9.48,1,17,29,155,6,0,4,16
380,exprCompareVariable,expr.c,6188,9.47,1,6,7,28,3,0,3,24
381,sqlite3BtreeBeginTrans,btree.c,3759,9.46,9,3,4,18,3,0,1,27
382,sqlite3demo_superlock,test_superlock.c,182,9.45,1,6,6,38,5,0,3,24
383,sqlite3ExprIsIIF,expr.c,6474,9.45,1,2,11,22,2,0,2,26
384,bindText,vdbeapi.c,1678,9.42,0,7,6,31,6,0,3,24
385,termIsEquivalence,whereexpr.c,942,9.41,1,5,6,17,2,0,1,28
386,sqlite3OomFault,malloc.c,849,9.41,46,1,5,19,1,0,3,15
387,sqlite3ValueBytes,vdbemem.c,2038,9.40,0,1,6,19,2,0,2,27
388,makeSorterRecord,select.c,676,9.39,2,1,2,16,5,0,1,29
389,allocateCursor,vdbe.c,253,9.39,7,6,8,43,4,0,2,24
390,analyzeTable,analyze.c,1425,9.36,2,6,2,17,3,0,1,28
391,sumInverse,func.c,1859,9.34,1,10,6,28,3,0,3,23
392,closeAllCursors,vdbeaux.c,2826,9.34,1,5,5,19,1,0,2,26
393,sqlite3BitvecSet,bitvec.c,169,9.34,8,7,12,56,2,0,4,19
394,btree_begin_transaction,test3.c,117,9.34,0,7,3,23,4,0,1,28
395,sqlite3VdbeSetP4KeyInfo,vdbeaux.c,1624,9.33,14,2,2,8,2,0,1,26
396,selectAddSubqueryTypeInfo,select.c,6458,9.32,0,1,4,19,2,0,2,27
397,sqlite3_result_value,vdbeapi.c,628,9.31,21,4,2,10,2,0,1,24
398,sqlite3FindFunction,callback.c,403,9.30,10,10,10,60,5,0,3,20
399,jsonAppendString,json.c,665,9.30,4,6,18,62,3,0,4,19
400,resolveOrderByTermToExprList,resolve.c,1520,9.29,2,3,4,32,3,0,2,26
401,sqlite3VdbeRecordCompareWithSkip,vdbeaux.c,4711,9.29,6,22,44,192,4,0,5,9
402,sqlite3TriggerUpdateStep,trigger.c,538,9.28,0,6,3,33,8,0,2,26
403,analyzeDatabase,analyze.c,1393,9.27,2,4,2,21,2,0,1,28
404,vdbeSorterFlushPMA,vdbesort.c,1698,9.26,2,5,8,42,1,0,3,23
405,exprVectorRegister,expr.c,655,9.26,2,2,4,26,6,0,1,28
406,whereApplyPartialIndexConstraints,wherecode.c,1360,9.25,2,2,5,20,3,0,2,26
407,pager_write,pager.c,6015,9.25,1,5,9,46,1,0,3,23
408,test_extract,test_func.c,469,9.24,0,10,4,33,3,0,2,25
409,indexInAffinityOk,where.c,314,9.24,1,3,4,23,3,0,1,28
410,sqlite3ResultSetOfSelect,select.c,2417,9.23,3,5,5,27,3,0,1,27
411,jsonAppendSqlValue,json.c,736,9.23,5,19,5,45,2,0,2,22
412,sqlite3ExprCompareCollSeq,expr.c,448,9.23,8,2,2,7,2,0,1,27
413,sqlite3_vtab_in_first,vdbeapi.c,1069,9.22,0,1,1,3,2,0,0,31
414,sqlite3_vtab_in_next,vdbeapi.c,1077,9.22,0,1,1,3,2,0,0,31
415,windowIfNewPeer,window.c,2057,9.22,2,3,2,22,5,0,1,28
416,jsonAppendChar,json.c,583,9.22,40,1,2,7,2,0,1,20
417,codeCompare,expr.c,459,9.21,4,5,3,25,9,0,1,27
418,vdbeSorterListToPMA,vdbesort.c,1549,9.20,3,9,7,40,2,0,3,22
419,groupConcatInverse,func.c,2151,9.20,1,6,7,38,3,0,3,23
420,sqlite3_get_table,table.c,116,9.19,2,10,13,61,6,0,3,21
421,nth_valueStepFunc,window.c,210,9.19,0,7,7,39,3,0,3,23
422,sqlite3BtreeUpdateMeta,btree.c,10382,9.18,5,4,3,21,3,0,2,25
423,whereLoopAddAll,where.c,4807,9.16,2,6,11,61,1,0,4,20
424,jsonObjectFunc,json.c,4178,9.16,0,14,4,33,3,0,2,24
425,sqlite3SrcItemAttachSubquery,build.c,4975,9.15,6,5,6,31,4,0,2,24
426,sqlite3DbStrDup,malloc.c,783,9.15,41,3,3,13,2,0,1,19
427,pager_commit,test2.c,132,9.15,0,8,4,26,4,0,1,27
428,renameUnmapSelectCb,alter.c,877,9.15,0,5,9,31,2,0,3,23
429,sqlite3VdbeList,vdbeaux.c,2397,9.15,1,15,7,64,1,0,3,21
430,sqlite3WindowChain,window.c,1276,9.13,1,5,8,29,3,0,4,21
431,test_open_v2,test1.c,5244,9.13,0,5,6,63,4,0,2,25
432,sqlite3ExprCodeGeneratedColumn,expr.c,4260,9.12,3,3,5,23,4,0,1,27
433,freePage,btree.c,6890,9.11,7,1,2,5,2,0,1,27
434,sqlite3_mutex_leave,mutex.c,339,9.08,124,0,2,6,1,0,1,1
435,sqlite3ExprCodeFactorable,expr.c,5672,9.08,5,3,2,7,3,0,1,27
436,sqlite3VdbeNextOpcode,vdbeaux.c,2253,9.08,1,1,16,77,6,0,4,20
437,jsonGroupInverse,json.c,4657,9.06,1,2,9,34,3,0,3,23
438,sqlite3ExprImpliesExpr,expr.c,6528,9.06,2,3,5,25,4,0,1,27
439,whereScanInitIndexExpr,where.c,456,9.05,1,2,1,4,1,0,0,30
440,sqlite3TriggerDeleteStep,trigger.c,578,9.04,0,2,3,22,5,0,2,26
441,exprIsCoveredByIndex,where.c,3613,9.04,1,1,3,15,3,0,2,26
442,sqlite3PagerFlush,pager.c,4654,9.04,0,2,4,15,1,0,3,24
443,sqlite3OpenTable,insert.c,26,9.03,2,4,3,28,5,0,1,27
444,renameWalkWith,alter.c,828,9.01,3,6,7,26,2,0,3,22
445,sqlite3_value_text,vdbeapi.c,227,9.00,132,0,1,3,1,0,0,1
446,getPageMMap,pager.c,5608,9.00,0,6,10,54,4,0,3,22
447,sumFinalize,func.c,1892,9.00,0,6,5,17,1,0,3,23
448,minmaxFunc,func.c,49,8.99,0,4,6,25,3,0,2,25
449,multiplexFileControl,test_multiplex.c,919,8.98,0,14,20,90,3,0,5,15
450,sqlite3ExprDelete,expr.c,1421,8.96,81,1,2,3,2,0,1,10
451,codeDeferredSeek,wherecode.c,1281,8.95,1,3,6,32,4,0,4,21
452,sqlite3ValueFromExpr,vdbemem.c,1782,8.95,4,1,2,9,5,0,0,29
453,btreeOverwriteCell,btree.c,9269,8.95,2,3,3,15,2,0,1,27
454,sqlite3ExprNNCollSeq,expr.c,317,8.94,9,1,2,6,2,0,1,26
455,sqlite3VdbeGetBoundValue,vdbeaux.c,5343,8.94,3,2,4,17,3,0,3,23
456,test_get_table_printf,test1.c,568,8.94,0,16,9,55,4,0,2,22
457,sqlite3SrcListLookup,delete.c,31,8.93,6,3,4,16,2,0,2,24
458,sqlite3VdbeJumpHere,vdbeaux.c,1316,8.92,121,1,1,3,2,0,0,3
459,copyPayload,btree.c,5029,8.92,2,3,3,18,5,0,2,25
460,exprNodeIsConstant,expr.c,2451,8.92,1,2,11,62,2,0,3,22
461,testSqllog,test_sqllog.c,468,8.92,0,12,15,57,4,0,4,18
462,setResultStrOrError,vdbeapi.c,387,8.91,3,6,4,23,5,0,2,24
463,sqlite3VdbeDisplayP4,vdbeaux.c,1900,8.90,2,19,27,107,2,0,3,16
464,minMaxValueFinalize,func.c,2031,8.89,2,2,4,10,2,0,2,25
465,whereLoopAddVirtual,where.c,4551,8.89,1,2,16,99,3,0,4,19
466,finalizeAggFunctions,select.c,6782,8.87,2,8,9,52,2,0,4,19
467,whereAddLimitExpr,whereexpr.c,1598,8.86,0,2,5,33,5,0,2,25
468,ntileValueFunc,window.c,453,8.86,0,4,4,20,1,0,3,23
469,statInit,analyze.c,400,8.83,0,8,3,35,3,0,1,26
470,sqlite3UpsertDup,upsert.c,41,8.82,3,6,2,10,2,0,1,26
471,sqlite3RowSetTest,rowset.c,442,8.82,1,6,12,50,3,0,4,19
472,sqlite3AlterBeginAddColumn,alter.c,483,8.81,0,13,9,56,2,0,1,24
473,ntileStepFunc,window.c,422,8.81,0,3,4,20,3,0,3,23
474,avgFinalize,func.c,1909,8.79,0,3,4,14,1,0,3,23
475,totalFinalize,func.c,1923,8.79,0,3,4,14,1,0,3,23
476,jsonErrorFunc,json.c,4541,8.79,0,11,8,39,3,0,5,17
477,sqlite3ExprCodeCopy,expr.c,5659,8.79,5,3,2,6,3,0,1,26
478,readsTable,insert.c,229,8.79,1,4,8,28,3,0,4,20
479,echoBestIndex,test8.c,799,8.78,0,12,22,123,2,0,4,16
480,sqlite3PagerSetJournalMode,pager.c,7328,8.77,3,8,12,56,2,0,4,18
481,jsonAppendPathName,json.c,4931,8.77,2,4,6,29,1,0,4,20
482,echoUpdate,test8.c,965,8.76,0,23,22,105,4,0,3,16
483,whereClauseInsert,whereexpr.c,60,8.76,13,6,6,33,3,0,3,19
484,checkList,btree.c,10630,8.75,2,15,12,55,4,0,5,15
485,multiSelectCollSeq,select.c,2543,8.73,4,2,3,13,3,0,1,26
486,winShmMap,os_win.c,4217,8.72,1,9,17,103,5,0,4,17
487,sqlite3VdbeResolveLabel,vdbeaux.c,639,8.72,77,1,2,13,2,0,1,10
488,jsonReturnParse,json.c,3530,8.72,0,6,4,26,2,0,2,24
489,vdbeSorterSort,vdbesort.c,1403,8.71,2,5,9,41,2,0,3,21
490,test_test_control,test1.c,7807,8.71,0,10,9,93,4,0,2,22
491,jsonEachNext,json.c,4962,8.69,0,6,9,63,1,0,3,21
492,sqlite3TriggerInsertStep,trigger.c,493,8.69,0,5,4,36,8,0,2,24
493,jsonFunctionArgToBlob,json.c,3198,8.68,0,26,11,76,3,0,3,17
494,test_collate_needed_cb,test1.c,3411,8.68,0,1,3,16,4,0,2,25
495,sqlite3VdbeSorterRewind,vdbesort.c,2583,8.68,1,4,4,27,2,0,2,24
496,sqlite3AddCheckConstraint,build.c,1878,8.68,0,5,5,27,4,0,3,22
497,sqlite3VdbeSorterInit,vdbesort.c,929,8.68,1,8,11,74,3,0,3,20
498,minmaxStep,func.c,1993,8.67,0,9,6,28,3,0,2,23
499,whereLoopInsert,where.c,2811,8.66,1,8,12,51,2,0,3,20
500,pagerStress,pager.c,4577,8.65,1,6,7,39,2,0,2,23
501,btreeEndTransaction,btree.c,4294,8.65,2,3,4,21,1,0,3,22
502,test_auxdata,test_func.c,185,8.64,0,9,6,31,3,0,3,21
503,newDatabase,btree.c,3495,8.63,3,6,3,35,1,0,1,25
504,sqlite3VdbeMemGrow,vdbemem.c,242,8.63,7,9,8,43,3,0,3,19
505,execSql,vacuum.c,32,8.61,3,5,6,23,3,0,3,21
506,sqlite3VdbeFrameDelete,vdbeaux.c,2365,8.60,1,4,3,12,1,0,2,24
507,sqlite3_backup_init,backup.c,140,8.60,0,7,6,41,4,0,2,23
508,sqlite3DropIndex,build.c,4550,8.59,0,14,10,60,3,0,2,21
509,test_create_function,test1.c,1079,8.58,0,7,17,88,4,0,2,21
510,jsonObjectStep,json.c,4705,8.58,0,9,4,25,3,0,2,23
511,unhexFunc,func.c,1301,8.57,0,10,10,44,3,0,4,18
512,sqlite3BtreeCommitPhaseTwo,btree.c,4356,8.57,3,6,3,22,2,0,2,23
513,sqlite3VdbeMemMakeWriteable,vdbemem.c,377,8.56,7,1,4,14,1,0,3,21
514,sqlite3FkOldmask,fkey.c,1093,8.55,3,2,9,24,2,0,4,19
515,sqlite3InitCallback,prepare.c,95,8.53,1,13,16,74,4,0,4,16
516,sqlite3ValueText,vdbemem.c,1369,8.52,1,1,4,14,2,0,1,26
517,createFunctionApi,main.c,2001,8.52,3,9,4,40,11,0,2,22
518,sqlite3GenerateColumnNames,select.c,2119,8.51,2,5,10,59,2,0,3,20
519,closeCursorsInFrame,vdbeaux.c,2784,8.50,2,1,3,10,1,0,2,24
520,sqlite3TableAffinity,insert.c,179,8.50,7,7,7,39,3,0,2,21
521,openStatTable,analyze.c,166,8.49,3,6,7,58,5,0,3,20
522,sqlite3VdbeChangeP4,vdbeaux.c,1561,8.48,14,3,8,31,4,0,2,20
523,innerLoopLoadRow,select.c,655,8.48,2,1,1,8,3,0,0,28
524,c_collation_test,test9.c,25,8.47,0,6,4,31,4,0,1,25
525,jsonInsertIntoBlob,json.c,3304,8.46,2,13,13,58,4,0,3,18
526,page_write,test2.c,465,8.46,0,7,3,25,4,0,1,25
527,havingToWhereExprCb,select.c,7052,8.46,0,2,4,21,2,0,3,22
528,sqlite3ExprAnd,expr.c,1135,8.46,13,2,4,19,3,0,2,21
529,sqlite3CheckCollSeq,callback.c,79,8.44,1,1,3,12,2,0,2,24
530,md5step,test_md5.c,409,8.43,0,5,6,16,3,0,2,23
531,sqlite3ExprListToValues,expr.c,1088,8.43,0,3,8,37,3,0,3,21
532,sqlite3CreateFunc,main.c,1866,8.43,2,6,11,96,11,0,2,21
533,sqlite3ExprListSetName,expr.c,2182,8.42,3,3,4,23,4,0,3,21
534,sqlite3_errmsg16,main.c,2678,8.42,2,8,5,30,1,0,2,22
535,winGetTempname,os_win.c,4754,8.41,0,32,16,112,2,0,3,14
536,sqlite3WithDup,expr.c,1738,8.41,3,3,4,18,2,0,3,21
537,groupConcatValue,func.c,2208,8.40,1,4,5,17,1,0,2,23
538,sqlite3UnlinkAndDeleteTrigger,trigger.c,712,8.37,2,3,6,23,3,0,5,17
539,test_errmsg16,test1.c,4820,8.37,0,5,4,24,4,0,2,23
540,jsonbValidityCheck,json.c,1303,8.36,5,8,76,191,4,0,5,4
541,jsonArrayStep,json.c,4591,8.35,0,5,4,19,3,0,2,23
542,windowAggFinal,window.c,1777,8.35,3,6,5,31,2,0,3,20
543,jsonMergePatch,json.c,3994,8.35,3,21,30,134,4,0,4,11
544,t1CountStep,test1.c,1241,8.35,0,4,5,20,3,0,2,23
545,sqlite3VdbeMemStringify,vdbemem.c,458,8.34,6,3,3,22,3,0,1,24
546,sqlite3VdbeMemCopy,vdbemem.c,1105,8.34,6,3,4,14,2,0,2,22
547,destroyTable,build.c,3271,8.33,1,2,6,26,2,0,3,21
548,sqlite3ExprListAppend,expr.c,2053,8.32,29,2,3,17,3,0,1,19
549,sqlite3Close,main.c,1233,8.31,2,10,7,30,2,0,2,21
550,sqllogCopydb,test_sqllog.c,291,8.31,2,13,10,60,3,0,4,16
551,sqlite3_result_text64,vdbeapi.c,573,8.31,1,3,3,20,5,0,1,25
552,subjournalPage,pager.c,4514,8.30,1,2,5,31,1,0,3,21
553,findBtree,backup.c,82,8.29,2,6,5,22,3,0,2,22
554,instrFunc,func.c,243,8.28,0,19,11,63,3,0,3,17
555,sqlite3NestedParse,build.c,288,8.28,27,8,4,27,3,0,1,18
556,sqlite3demo_superunlock,test_superlock.c,151,8.27,2,2,3,15,1,0,2,23
557,sqlite3MPrintf,printf.c,1209,8.27,60,3,1,8,3,0,0,14
558,sqlite3WhereAddExplainText,wherecode.c,117,8.27,3,14,19,83,5,0,4,14
559,sqlite3VdbeChangeEncoding,vdbemem.c,203,8.27,11,1,3,20,2,0,1,23
560,sqlite3ExprAlloc,expr.c,927,8.27,1,4,9,40,4,0,4,18
561,sqlite3VdbeExpandSql,vdbetrace.c,72,8.26,2,20,20,93,2,0,5,11
562,last_valueStepFunc,window.c,488,8.26,0,4,3,18,3,0,2,23
563,sqlite3GetToken,tokenize.c,273,8.25,4,1,85,305,2,0,6,1
564,sqlite3BtreePayload,btree.c,5291,8.25,3,1,1,6,4,0,0,27
565,renameTableSelectCb,alter.c,1676,8.24,0,2,5,22,2,0,2,23
566,autoIncBegin,insert.c,406,8.24,3,2,7,41,3,0,3,20
567,sqlite3CompleteInsertion,insert.c,2785,8.24,2,3,13,63,9,0,3,19
568,sqlite3ExprListDelete,expr.c,2267,8.23,61,1,2,3,2,0,1,12
569,sqlite3_mutex_enter,mutex.c,313,8.23,111,0,2,6,1,0,1,1
570,t1CountFinalize,test1.c,1263,8.23,0,3,4,11,1,0,2,23
571,findCollSeqEntry,callback.c,107,8.22,1,7,4,30,3,0,3,20
572,sqlite3AddGenerated,build.c,1945,8.22,0,7,11,42,3,0,2,21
573,sqlite3ExprCollSeqMatch,expr.c,327,8.22,1,3,1,5,3,0,0,27
574,sqlite3ExprIsNotTrue,expr.c,6455,8.21,1,1,4,8,1,0,1,25
575,sqlite3_backup_finish,backup.c,571,8.21,1,9,7,36,1,0,2,21
576,sqlite3TwoPartName,build.c,960,8.21,8,3,4,28,4,0,2,21
577,first_valueStepFunc,window.c,263,8.20,0,3,3,16,3,0,2,23
578,sqlite3WithAdd,build.c,5708,8.19,0,5,7,36,3,0,3,20
579,percent_rankValueFunc,window.c,352,8.19,0,3,3,13,1,0,2,23
580,test_create_collation_v2,test1.c,1871,8.19,0,7,3,33,4,0,1,24
581,sqlite3StrICmp,util.c,416,8.19,91,0,5,19,2,0,3,1
582,nullifFunc,func.c,979,8.18,0,3,2,11,3,0,1,25
583,first_valueFinalizeFunc,window.c,279,8.17,0,3,2,9,1,0,1,25
584,last_valueFinalizeFunc,window.c,530,8.17,0,3,2,9,1,0,1,25
585,nth_valueFinalizeFunc,window.c,251,8.17,0,3,2,9,1,0,1,25
586,sqlite3AnalysisLoad,analyze.c,1951,8.17,2,6,8,42,2,0,2,21
587,sqlite3BtreeTableMoveto,btree.c,5727,8.17,5,10,24,121,4,0,5,11
588,sqlite3_blob_reopen,vdbeblob.c,489,8.17,0,6,5,24,2,0,2,22
589,jsonPrettyFunc,json.c,4374,8.16,0,7,3,22,3,0,1,24
590,sqlite3LocateCollSeq,callback.c,256,8.16,5,2,2,11,2,0,1,24
591,last_valueInvFunc,window.c,506,8.15,0,2,3,17,3,0,2,23
592,sqlite3ResetAllSchemasOfConnection,build.c,634,8.14,10,5,5,20,1,0,3,18
593,sqlite3LocateTableItem,build.c,466,8.14,9,2,2,15,3,0,1,23
594,dense_rankValueFunc,window.c,189,8.13,0,2,3,11,1,0,2,23
595,sqlite3_result_blob64,vdbeapi.c,451,8.13,0,2,2,14,4,0,1,25
596,pragmaVtabColumn,pragma.c,3002,8.13,0,2,2,14,3,0,1,25
597,sqlite3ExprCompareSkip,expr.c,6366,8.12,4,3,1,6,3,0,0,26
598,last_valueValueFunc,window.c,523,8.11,0,2,2,7,1,0,1,25
599,sqlite3VtabFinishParse,vtab.c,447,8.11,0,14,6,54,2,0,2,20
600,sqlite3SrcListAppend,build.c,4835,8.11,6,7,6,43,4,0,2,20
601,jsonArrayFunc,json.c,3734,8.11,0,7,2,17,3,0,1,24
602,concatFuncCore,func.c,1561,8.11,2,7,7,38,5,0,4,17
603,sqlite3BtreeClose,btree.c,2910,8.10,10,9,5,24,1,0,2,19
604,sqlite3FindCollSeq,callback.c,159,8.10,9,1,3,17,4,0,2,21
605,rankStepFunc,window.c,297,8.09,0,1,3,16,3,0,2,23
606,jsonConvertTextToBlob,json.c,1981,8.09,0,7,10,33,2,0,4,17
607,windowInitAccum,window.c,1999,8.09,1,3,6,26,2,0,3,20
608,sqlite3AutoLoadExtensions,loadext.c,884,8.09,1,3,5,30,1,0,2,22
609,sqlite3ValueApplyAffinity,vdbe.c,451,8.09,5,1,1,7,3,0,0,26
610,winOpenSharedMemory,os_win.c,3926,8.08,1,19,11,73,1,0,3,16
611,sqlite3VdbeFreeCursorNN,vdbeaux.c,2753,8.08,5,3,5,25,2,0,1,23
612,sqlite3BeginTransaction,build.c,5195,8.07,0,3,7,29,2,0,3,20
613,sqlite3DropTrigger,trigger.c,623,8.07,0,6,9,35,3,0,2,21
614,c_misuse_test,test9.c,107,8.06,0,5,8,59,4,0,1,23
615,sqlite3GetCollSeq,callback.c,205,8.05,3,5,5,26,4,0,1,23
616,sqlite3WithPush,select.c,5699,8.05,3,1,5,15,3,0,3,20
617,btree_close_cursor,test3.c,245,8.04,0,10,3,29,4,0,1,23
618,test_atomic_batch_write,test1.c,2849,8.04,0,9,4,32,4,0,1,23
619,sqlite3WindowDup,window.c,2384,8.03,2,8,3,28,3,0,2,21
620,test_collate,test1.c,3300,8.03,0,8,5,46,4,0,2,21
621,jsonAppendRawNZ,json.c,556,8.03,21,2,2,9,3,0,1,20
622,jsonReturnTextJsonFromBlob,json.c,3004,8.01,1,4,2,15,3,0,1,24
623,detachFunc,attach.c,277,8.01,0,9,10,49,3,0,2,20
624,sqlite3VdbeMemHandleBom,utf.c,408,8.00,0,2,6,27,1,0,2,22
625,jsonValidFunc,json.c,4455,8.00,0,13,12,59,3,0,3,17
626,echoCreate,test8.c,461,7.99,0,8,5,30,6,0,2,21
627,sqlite3WalClose,wal.c,2479,7.99,3,8,8,45,5,0,4,16
628,exprINAffinity,expr.c,3372,7.98,3,5,5,22,2,0,3,19
629,walIndexRecover,wal.c,1377,7.97,1,22,22,151,1,0,4,11
630,sqlite3CodeDropTable,build.c,3343,7.96,1,10,6,39,4,0,1,22
631,jsonRemoveFunc,json.c,4220,7.96,0,8,11,46,3,0,3,18
632,sqlite3VtabCallCreate,vtab.c,769,7.95,1,7,4,23,4,0,2,21
633,sqlite3BtreeNewDb,btree.c,3539,7.93,1,3,1,8,1,0,0,26
634,callCollNeeded,callback.c,23,7.93,1,6,5,19,3,0,2,21
635,triggerSpanDup,trigger.c,422,7.92,2,1,4,6,3,0,3,20
636,synthCollSeq,callback.c,52,7.91,1,2,3,15,2,0,2,22
637,generateSortTail,select.c,1655,7.91,1,17,25,158,5,0,2,15
638,sqlite3VdbeTransferError,vdbeaux.c,3517,7.90,3,4,4,17,1,0,2,21
639,minMaxQuery,select.c,5436,7.90,1,4,6,36,3,0,2,21
640,sqlite3VtabCallConnect,vtab.c,696,7.90,1,6,4,27,2,0,2,21
641,sqlite3VtabBeginParse,vtab.c,385,7.89,0,7,3,32,5,0,1,23
642,sqlite3IdListAppend,build.c,4664,7.88,1,5,5,23,3,0,2,21
643,sqlite3NameFromToken,build.c,885,7.87,23,2,2,10,2,0,1,19
644,valueBytes,vdbemem.c,2035,7.86,1,1,2,3,2,0,0,26
645,sqlite3WindowListDup,window.c,2417,7.85,1,1,3,11,2,0,2,22
646,sqlite3VdbeFreeCursor,vdbeaux.c,2738,7.82,1,1,2,3,2,0,1,24
647,minMaxValue,func.c,2042,7.81,1,1,1,3,1,0,0,26
648,sqlite3ErrorWithMsg,util.c,192,7.80,9,6,3,15,4,0,1,21
649,sqlite3VdbeExplain,vdbeaux.c,519,7.80,34,3,3,23,4,0,2,14
650,sqllogFindFile,test_sqllog.c,175,7.79,1,11,10,38,1,0,4,15
651,sqlite3_open16,main.c,3603,7.79,1,3,5,27,2,0,2,21
652,triggerStepAllocate,trigger.c,460,7.78,0,5,4,24,5,0,2,21
653,jsonReturnStringAsBlob,json.c,2026,7.77,3,7,3,21,1,0,1,22
654,computeNumericType,vdbe.c,465,7.77,1,2,6,23,1,0,2,21
655,patternCompare,func.c,729,7.76,6,17,41,112,4,0,7,2
656,sqlite3VtabEponymousTableInit,vtab.c,1256,7.76,1,12,6,33,2,0,1,21
657,minMaxFinalize,func.c,2048,7.75,0,1,1,3,1,0,0,26
658,jsonArrayLengthFunc,json.c,3760,7.74,0,6,9,37,3,0,3,18
659,sqlite3DropTriggerPtr,trigger.c,674,7.73,3,5,5,28,2,0,2,20
660,winFileControl,os_win.c,3518,7.72,0,6,12,111,3,0,4,15
661,sqlite3WhereTabFuncArgs,whereexpr.c,1865,7.72,1,4,8,46,3,0,2,20
662,sqlite3_create_collation16,main.c,3680,7.72,0,5,2,20,5,0,1,23
663,sqlite3_set_auxdata,vdbeapi.c,1182,7.70,2,3,8,35,4,0,2,20
664,pagerWalFrames,pager.c,3162,7.70,3,3,7,37,4,0,3,18
665,exprNodeIsConstantFunction,expr.c,2392,7.70,1,2,4,30,2,0,2,21
666,test_load_extension,test1.c,2035,7.70,0,7,5,38,4,0,1,22
667,jsonTypeFunc,json.c,4327,7.70,0,6,8,34,3,0,3,18
668,walTryBeginRead,wal.c,2987,7.69,2,18,20,116,4,0,3,13
669,test_isolation,test_func.c,272,7.68,0,5,1,11,3,0,0,25
670,sqlite3RenameTokenMap,alter.c,775,7.68,7,1,3,19,3,0,2,20
671,createAggContext,vdbeapi.c,1108,7.68,1,3,3,16,2,0,2,21
672,sqlite3VdbeFrameRestore,vdbeaux.c,2800,7.67,2,2,1,17,1,0,0,25
673,walIndexReadHdr,wal.c,2632,7.64,2,8,15,64,2,0,5,12
674,sqlite3_get_table_cb,table.c,42,7.63,0,7,12,51,4,0,4,15
675,sqlite3TableAffinityStr,insert.c,122,7.62,1,1,5,16,2,0,3,19
676,freeP4,vdbeaux.c,1372,7.62,4,8,17,46,3,0,2,17
677,sqlite3_stmt_status,vdbeapi.c,2072,7.62,0,3,3,20,3,0,2,21
678,loadExt,func.c,1718,7.61,0,7,4,19,3,0,1,22
679,sqlite3_result_blob,vdbeapi.c,435,7.60,2,1,1,10,4,0,0,25
680,sqlite3VdbeDelete,vdbeaux.c,3759,7.59,5,2,3,16,1,0,2,20
681,sqlite3ParserAddCleanup,prepare.c,624,7.59,16,4,3,23,3,0,1,19
682,sqlite3VdbeClearObject,vdbeaux.c,3715,7.58,1,9,7,21,2,0,2,19
683,windowReadPeerValues,window.c,1621,7.58,5,1,4,17,3,0,2,20
684,groupConcatFinalize,func.c,2197,7.58,0,3,2,8,1,0,1,23
685,multiplexSubOpen,test_multiplex.c,289,7.58,0,3,9,48,5,0,4,16
686,sqlite3ValueNew,vdbemem.c,1405,7.58,15,1,2,8,1,0,1,20
687,sqlite3SchemaGet,callback.c,529,7.58,4,7,4,18,2,0,1,21
688,test_setsubtype,test_func.c,653,7.57,0,3,1,8,3,0,0,25
689,sqlite_test_close,test1.c,684,7.55,0,2,2,18,4,0,1,23
690,sqlite_test_close_v2,test1.c,708,7.55,0,2,2,18,4,0,1,23
691,sqlite3PagerClose,pager.c,4146,7.54,2,14,4,41,2,0,2,18
692,countInverse,func.c,1975,7.54,1,1,2,7,3,0,1,23
693,cume_distValueFunc,window.c,397,7.53,0,2,2,8,1,0,1,23
694,rankValueFunc,window.c,313,7.53,0,2,2,8,1,0,1,23
695,sqlite3ResetOneSchema,build.c,610,7.53,4,1,5,17,2,0,3,18
696,tclColumn,test_bestindex.c,382,7.52,0,2,1,9,3,0,0,25
697,addIntTypeFunction,test1.c,1005,7.52,0,2,1,9,3,0,0,25
698,addRealTypeFunction,test1.c,1014,7.52,0,2,1,9,3,0,0,25
699,addTextTypeFunction,test1.c,996,7.52,0,2,1,9,3,0,0,25
700,sqlite3PagerSavepoint,pager.c,6974,7.52,5,2,9,31,3,0,4,15
701,sqlite3VdbeSorterCompare,vdbesort.c,2733,7.51,1,4,4,32,4,0,2,20
702,sqlite3DbMallocRaw,malloc.c,658,7.50,22,2,2,7,2,0,1,18
703,winOpen,os_win.c,5027,7.49,0,21,26,178,5,0,4,9
704,cume_distStepFunc,window.c,373,7.49,0,1,2,13,3,0,1,23
705,percent_rankStepFunc,window.c,328,7.49,0,1,2,13,3,0,1,23
706,dense_rankStepFunc,window.c,178,7.48,0,1,2,11,3,0,1,23
707,row_numberStepFunc,window.c,147,7.48,0,1,2,10,3,0,1,23
708,renameColumnSelectCb,alter.c,995,7.48,0,1,2,9,2,0,1,23
709,countStep,func.c,1953,7.48,0,1,2,9,3,0,1,23
710,vtabBestIndex,where.c,1659,7.48,0,4,5,26,3,0,2,20
711,fixSelectCb,attach.c,469,7.47,0,6,10,40,2,0,4,15
712,sqlite3VdbeAppendP4,vdbeaux.c,1604,7.47,22,1,2,15,3,0,1,18
713,jsonArrayFinal,json.c,4646,7.46,0,1,1,3,1,0,0,25
714,jsonArrayValue,json.c,4643,7.46,0,1,1,3,1,0,0,25
715,jsonObjectFinal,json.c,4766,7.46,0,1,1,3,1,0,0,25
716,jsonObjectValue,json.c,4763,7.46,0,1,1,3,1,0,0,25
717,sqlite3SelectNew,select.c,126,7.45,8,3,5,51,9,0,1,20
718,sqlite3VdbeMemExpandBlob,vdbemem.c,401,7.45,11,2,3,23,1,0,1,20
719,vdbeFreeOpArray,vdbeaux.c,1430,7.44,2,2,5,13,3,0,3,18
720,jsonStringGrow,json.c,510,7.44,7,5,6,24,2,0,2,18
721,subjournalPageIfRequired,pager.c,4550,7.43,3,2,2,7,1,0,1,22
722,test_open16,test1.c,5315,7.42,0,5,2,20,4,0,1,22
723,sqlite3VdbeAddOp3,vdbeaux.c,272,7.42,21,1,2,22,5,0,1,18
724,jsonAppendRaw,json.c,547,7.41,11,2,3,9,3,0,1,20
725,test_complete16,test1.c,5347,7.41,0,5,2,15,4,0,1,22
726,walCheckpoint,wal.c,2187,7.41,1,13,22,129,7,0,5,9
727,addToSavepointBitvecs,pager.c,1804,7.40,3,1,3,13,2,0,2,20
728,invalidateTempStorage,pragma.c,159,7.38,1,3,3,16,1,0,2,20
729,btreeSetHasContent,btree.c,651,7.37,1,2,4,14,2,0,2,20
730,sqlite3PagerCloseWal,pager.c,7632,7.34,0,6,7,27,2,0,3,17
731,winFullPathnameNoMutex,os_win.c,5602,7.34,0,26,13,81,4,0,2,14
732,jsonQuoteFunc,json.c,3715,7.34,0,4,1,12,3,0,0,24
733,lowerFunc,func.c,524,7.33,0,3,4,18,3,0,3,18
734,upperFunc,func.c,505,7.33,0,3,4,18,3,0,3,18
735,numericType,vdbe.c,496,7.33,2,1,2,15,1,0,1,22
736,sqlite3VtabCallDestroy,vtab.c,925,7.32,1,5,6,32,3,0,3,17
737,sqlite3ValueSetStr,vdbemem.c,2011,7.32,2,1,2,9,5,0,1,22
738,sqlite3VdbeMakeReady,vdbeaux.c,2638,7.31,2,6,7,70,2,0,2,18
739,sqlite3Savepoint,build.c,5253,7.31,0,3,3,13,3,0,2,20
740,sqlite3_create_collation,main.c,3642,7.31,2,1,1,9,5,0,0,24
741,sqlite3_open_v2,main.c,3590,7.30,2,1,1,8,4,0,0,24
742,jsonCacheInsert,json.c,381,7.30,0,7,3,31,2,0,1,21
743,sqlite3VdbeAddOp4Dup8,vdbeaux.c,476,7.30,0,3,2,13,7,0,1,22
744,sqlite3_intarray_create,test_intarray.c,229,7.30,1,6,3,23,3,0,1,21
745,computeIndexAffStr,insert.c,75,7.28,1,3,7,28,2,0,2,19
746,sqlite3CodeVerifyNamedSchema,build.c,5329,7.28,5,2,3,10,2,0,2,19
747,test_collate_func,test1.c,3250,7.28,0,6,3,45,5,0,1,21
748,fixDistinctOpenEph,select.c,984,7.28,3,3,4,22,4,0,2,19
749,createTableStmt,build.c,2086,7.26,1,13,5,64,2,0,1,19
750,sqlite3WalCheckpoint,wal.c,4257,7.25,1,16,16,75,10,0,3,13
751,tclConnect,test_bestindex.c,174,7.23,0,14,7,50,6,0,3,15
752,sqlite3VdbeAddOp4Int,vdbeaux.c,317,7.23,9,1,2,26,6,0,1,20
753,sqlite3_close,main.c,1330,7.22,10,1,1,1,1,0,0,22
754,fpnum_compare,test1.c,6006,7.22,0,36,48,92,4,0,5,1
755,sqlite3SrcListIndexedBy,build.c,5088,7.22,0,1,3,18,3,0,2,20
756,sqlite3VtabOverloadFunction,vtab.c,1152,7.21,2,4,8,41,4,0,1,20
757,windowCheckValue,window.c,1482,7.21,1,10,2,42,3,0,1,20
758,sqlite3RowidConstraint,build.c,5456,7.21,3,3,2,18,3,0,1,21
759,sqlite3ExprListSetSpan,expr.c,2217,7.21,0,1,3,17,4,0,2,20
760,btree_varint_test,test3.c,428,7.20,0,15,11,69,4,0,3,14
761,pagerAddPageToRollbackJournal,pager.c,5958,7.20,1,3,4,33,1,0,1,21
762,jsonAppendSeparator,json.c,615,7.19,4,1,3,7,1,0,1,21
763,sqlite3VtabCreateModule,vtab.c,39,7.19,0,9,5,42,5,0,2,18
764,sqlite3ExpandSubquery,select.c,5935,7.19,1,4,3,23,2,0,1,21
765,sqlite3_create_collation_v2,main.c,3655,7.19,2,4,1,16,6,0,0,23
766,sqllogOpenlog,test_sqllog.c,367,7.18,1,13,7,25,1,0,3,15
767,sqlite3_complete16,complete.c,269,7.18,1,4,3,17,1,0,1,21
768,sqlite3ClearStatTables,build.c,3320,7.16,2,3,3,19,4,0,2,19
769,echoRename,test8.c,1257,7.16,0,5,3,16,2,0,1,21
770,jsonPrettyIndent,json.c,2350,7.15,4,1,2,6,1,0,1,21
771,vdbeChangeP4Full,vdbeaux.c,1542,7.14,1,3,4,19,4,0,2,19
772,execSqlF,vacuum.c,62,7.13,5,5,1,12,4,0,0,22
773,generateOutputSubroutine,select.c,3269,7.12,2,16,10,84,8,0,2,15
774,sqlite3AddReturning,build.c,1413,7.12,0,7,5,41,2,0,1,20
775,echoDestroy,test8.c,531,7.11,0,4,3,15,1,0,1,21
776,sqlite3_result_error_nomem,vdbeapi.c,694,7.10,34,2,1,6,1,0,0,16
777,sqlite3RowSetInsert,rowset.c,210,7.10,2,1,4,19,2,0,2,19
778,unlockBtreeIfUnused,btree.c,3478,7.09,3,1,2,11,1,0,1,21
779,setPragmaResultColumnNames,pragma.c,198,7.09,2,1,4,15,2,0,2,19
780,sqlite3WindowAlloc,window.c,1177,7.07,0,6,5,46,7,0,1,20
781,sqlite3VdbeSorterRowkey,vdbesort.c,2700,7.07,1,3,2,14,2,0,1,21
782,sqlite3VtabUsesAllSchemas,where.c,4513,7.07,1,2,4,12,1,0,2,19
783,sqlite3ExprAddFunctionOrderBy,expr.c,1209,7.04,0,5,6,38,3,0,1,20
784,btree_close,test3.c,82,7.04,0,7,4,28,4,0,1,20
785,sqlite3_str_appendf,printf.c,1373,7.04,95,3,1,6,3,0,0,2
786,sqlite3UpsertNew,upsert.c,55,7.03,2,6,2,27,6,0,1,20
787,sqlite3WindowAssemble,window.c,1249,7.03,0,3,3,19,5,0,2,19
788,sqlite3TriggerSelectStep,trigger.c,436,7.01,0,3,2,17,4,0,1,21
789,sqlite3DequoteNumber,util.c,332,7.01,0,1,6,28,2,0,4,15
790,destroyRootPage,build.c,3241,7.00,2,6,2,12,3,0,1,20
791,sqlite3ExprAssignVarNumber,expr.c,1300,7.00,0,6,11,58,3,0,3,15
792,exprNodeCanReturnSubtype,expr.c,4575,6.99,0,1,4,17,2,0,1,21
793,md5finalize,test_md5.c,425,6.99,0,3,1,9,1,0,0,23
794,test_agg_errmsg16_final,test_func.c,163,6.98,0,3,1,7,1,0,0,23
795,test_exec_nr,test1.c,468,6.97,0,2,2,19,4,0,1,21
796,countFinalize,func.c,1969,6.97,0,2,2,5,1,0,0,23
797,row_numberValueFunc,window.c,157,6.96,0,2,2,4,1,0,0,23
798,sqlite3PagerCheckpoint,pager.c,7477,6.96,0,1,3,21,5,0,1,21
799,sqlite3VdbeMemNulTerminate,vdbemem.c,432,6.96,1,1,2,11,1,0,1,21
800,sqlite3_create_function_v2,main.c,2065,6.96,1,1,1,14,9,0,0,23
801,sqlite3_create_function,main.c,2052,6.96,1,1,1,13,8,0,0,23
802,sqlite3WhereSplit,whereexpr.c,1574,6.94,2,4,3,12,3,0,1,20
803,sqlite3VdbeMemClearAndResize,vdbemem.c,305,6.93,5,1,2,11,2,0,1,20
804,sqlite3ForceNotReadOnly,build.c,1159,6.91,4,2,2,8,1,0,1,20
805,syncJournal,pager.c,4254,6.91,2,6,14,64,2,0,5,10
806,sqlite3DbStrNDup,malloc.c,796,6.90,12,2,3,12,3,0,1,18
807,sqlite3_create_window_function,main.c,2079,6.90,0,1,1,15,10,0,0,23
808,string_concat,test8.c,723,6.89,12,5,6,25,4,0,2,15
809,cume_distInvFunc,window.c,386,6.89,0,1,1,11,3,0,0,23
810,ntileInvFunc,window.c,442,6.89,0,1,1,11,3,0,0,23
811,percent_rankInvFunc,window.c,341,6.89,0,1,1,11,3,0,0,23
812,contextMalloc,func.c,484,6.88,6,4,3,17,2,0,2,17
813,sqlite3ResolveSelfReference,resolve.c,2285,6.88,5,3,5,31,5,0,2,17
814,sqlite3IdListDup,expr.c,1930,6.88,2,2,4,15,2,0,1,20
815,vdbeSorterFlushThread,vdbesort.c,1684,6.88,0,1,1,8,1,0,0,23
816,roundFunc,func.c,443,6.87,0,6,9,27,3,0,2,17
817,sqlite3VdbeChangeToNoop,vdbeaux.c,1467,6.87,13,1,2,11,2,0,1,18
818,sqlite3CteNew,build.c,5657,6.85,1,4,2,22,5,0,1,20
819,sqlite3ColumnSetExpr,build.c,667,6.85,2,2,3,20,4,0,1,20
820,sqlite3_str_append,printf.c,1030,6.84,47,2,3,13,3,0,1,10
821,sqlite3_initialize,main.c,193,6.84,25,13,15,67,0,0,3,7
822,sqlite3BtreeSchema,btree.c,11290,6.84,2,3,2,10,3,0,1,20
823,pagerUnlockAndRollback,pager.c,2177,6.83,2,6,5,26,1,0,2,17
824,sqlite3SetTextEncoding,callback.c,181,6.83,3,2,1,6,2,0,0,22
825,sqlite3_create_function16,main.c,2096,6.83,0,5,1,21,8,0,0,22
826,getColumnNames,test8.c,155,6.82,1,5,7,51,4,0,3,15
827,codeInteger,expr.c,4197,6.82,2,4,8,27,4,0,3,15
828,pager_playback,pager.c,2784,6.82,3,10,23,108,2,0,4,9
829,sqlite3VdbeAddOp4,vdbeaux.c,414,6.81,7,2,1,13,7,0,0,21
830,extendFJMatch,resolve.c,208,6.81,3,1,2,16,4,0,1,20
831,sqlite3HaltConstraint,build.c,5398,6.79,1,3,2,18,6,0,1,20
832,sqlite3HexToBlob,util.c,1501,6.79,1,3,3,13,3,0,2,18
833,renameParseSql,alter.c,1133,6.77,5,6,5,29,5,0,1,18
834,jsonPrintf,json.c,568,6.77,4,5,2,8,4,0,1,19
835,sqlite3AllocateIndexObject,build.c,3834,6.77,2,1,2,26,4,0,1,20
836,sqlite3VtabImportErrmsg,vdbeaux.c,5431,6.77,1,3,2,9,2,0,1,20
837,rowSetEntryAlloc,rowset.c,185,6.75,2,1,3,16,1,0,2,18
838,hexFunc,func.c,1239,6.75,0,3,3,25,3,0,2,18
839,valueNew,vdbemem.c,1436,6.74,7,1,1,4,2,0,0,21
840,sqllogFindAttached,test_sqllog.c,225,6.74,0,6,6,37,4,0,3,15
841,sqlite3TableLock,build.c,85,6.73,14,2,3,11,5,0,1,17
842,vdbeMemAddTerminator,vdbemem.c,360,6.73,2,1,2,10,1,0,1,20
843,renameTestSchema,alter.c,53,6.73,5,2,2,28,5,0,1,19
844,vdbeSortAllocUnpacked,vdbesort.c,1333,6.73,2,1,2,9,1,0,1,20
845,loadAnalysis,analyze.c,1383,6.72,2,1,2,6,2,0,1,20
846,sqlite3PagerRollback,pager.c,6735,6.72,4,6,6,30,1,0,2,16
847,sqlite3IndexAffinityStr,insert.c,111,6.72,2,1,2,4,2,0,1,20
848,sqlite3OpenSchemaTable,build.c,900,6.71,1,2,2,8,2,0,1,20
849,sqlite3Strlen30,util.c,92,6.70,87,1,2,4,1,0,1,1
850,sqlite3FindTable,build.c,332,6.70,23,18,17,53,3,0,4,4
851,jsonPatchFunc,json.c,4144,6.70,0,6,5,26,3,0,2,17
852,sqlite3_win32_set_directory8,os_win.c,1916,6.69,1,3,7,37,2,0,3,15
853,sqlite3ResolveExprListNames,resolve.c,2195,6.68,7,2,5,39,2,0,2,16
854,whereLoopXfer,where.c,2564,6.68,1,5,4,17,3,0,1,19
855,sqlite3ExprCheckIN,expr.c,3873,6.67,3,3,4,13,2,0,2,17
856,sqlite3KeyInfoAlloc,select.c,1516,6.66,8,3,2,16,3,0,1,18
857,sqlite3EndTransaction,build.c,5231,6.66,0,1,3,16,2,0,1,20
858,save_prng_state,test1.c,7227,6.65,0,2,1,13,4,0,0,22
859,sqlite3JoinType,select.c,262,6.64,0,2,8,52,4,0,3,15
860,sqlite3HasExplicitNulls,build.c,3867,6.64,4,1,5,15,2,0,3,15
861,sqlite3_close_v2,main.c,1331,6.63,1,1,1,1,1,0,0,22
862,sqlite3_delete_database,test_delete.c,95,6.62,1,7,7,46,1,0,3,14
863,corruptSchema,prepare.c,22,6.62,7,5,7,32,3,0,2,15
864,sqlite3VdbeAddFunctionCall,vdbeaux.c,438,6.60,5,5,2,33,7,0,1,18
865,sqlite3_load_extension,loadext.c,704,6.59,2,4,1,13,4,0,0,21
866,test_prepare_v2,test1.c,4914,6.59,0,16,9,58,4,0,2,14
867,sqlite3VdbeAllocUnpackedRecord,vdbeaux.c,4210,6.58,4,1,2,14,1,0,1,19
868,sqlite3_log,printf.c,1332,6.58,25,3,2,8,3,0,1,14
869,sqlite3SetString,malloc.c,830,6.57,8,2,1,5,3,0,0,20
870,sqlite3BeginWriteOperation,build.c,5353,6.56,17,1,2,6,3,0,0,18
871,charFunc,func.c,1197,6.56,0,2,6,37,3,0,2,17
872,getIndexArray,test8.c,235,6.55,1,6,12,59,4,0,2,15
873,counterFunc,test_func.c,239,6.55,0,5,3,19,3,0,2,17
874,jsonSetFunc,json.c,4303,6.55,0,3,5,14,3,0,1,19
875,test_prepare_v3,test1.c,4988,6.55,0,16,8,57,4,0,2,14
876,sqlite3MemCompare,vdbeaux.c,4557,6.54,7,4,20,67,3,0,4,9
877,echoSelectList,test8.c,759,6.54,0,4,5,17,2,0,3,15
878,sqlite3DbSpanDup,malloc.c,814,6.54,3,1,3,7,3,0,1,19
879,jsonStringTerminate,json.c,606,6.53,3,2,1,5,1,0,0,21
880,sqlite3SchemaClear,callback.c,495,6.53,4,9,4,28,1,0,1,17
881,page_get,test2.c,305,6.51,0,8,4,30,4,0,1,18
882,sqlite3DeleteTable,build.c,841,6.50,16,1,3,6,2,0,1,16
883,memdbFileControl,memdb.c,464,6.50,0,3,5,24,3,0,3,15
884,renameFixQuotes,alter.c,90,6.50,2,2,2,16,3,0,1,19
885,jsonStringExpandAndAppend,json.c,537,6.49,2,2,2,10,3,0,1,19
886,sqlite3CodeVerifySchema,build.c,5320,6.49,16,1,2,3,2,0,0,18
887,winGetLastErrorMsg,os_win.c,1994,6.49,3,14,5,47,3,0,2,14
888,addArgumentToVtab,vtab.c,434,6.48,2,2,2,8,1,0,1,19
889,sqlite3_deserialize,memdb.c,839,6.48,1,7,9,61,6,0,2,15
890,sqlite3VdbeSetNumCols,vdbeaux.c,2854,6.48,4,4,3,13,2,0,1,18
891,jsonBadPathError,json.c,3281,6.45,8,4,3,14,2,0,1,17
892,sqlite3CodeVerifySchemaAtToplevel,build.c,5308,6.45,2,1,3,12,2,0,2,17
893,tclvarUpdate,test_tclvar.c,412,6.44,0,9,8,44,4,0,2,15
894,sqlite3FkDelete,fkey.c,1447,6.44,2,4,6,25,2,0,3,14
895,sqlite3PExpr,expr.c,1033,6.43,2,6,2,20,4,0,1,18
896,sqlite3WhereRealloc,where.c,269,6.42,1,2,2,10,3,0,1,19
897,sqlite3CheckObjectName,build.c,1015,6.42,2,9,5,32,4,0,2,15
898,sqlite3VdbeSetSql,vdbeaux.c,70,6.41,1,1,3,9,4,0,1,19
899,sqlite3VdbeAddOpList,vdbeaux.c,1140,6.41,0,1,4,31,4,0,2,17
900,deleteTable,build.c,783,6.41,1,10,5,33,2,0,2,15
901,jsonReplaceFunc,json.c,4277,6.40,0,2,3,12,3,0,1,19
902,printfTempBuf,printf.c,143,6.40,0,1,4,13,2,0,1,19
903,checkAppendMsg,btree.c,10526,6.39,21,7,5,24,3,0,1,13
904,test_function_utf16be,test1.c,3572,6.39,0,2,1,27,3,0,0,21
905,addOp4IntSlow,vdbeaux.c,233,6.38,1,1,2,16,6,0,1,19
906,test_function_utf8,test1.c,3526,6.38,0,2,1,24,3,0,0,21
907,sqlite3ExprDeferredDelete,expr.c,1451,6.38,6,1,1,3,2,0,0,20
908,test_function_utf16le,test1.c,3550,6.38,0,2,1,22,3,0,0,21
909,walRestartLog,wal.c,3831,6.37,0,5,6,31,1,0,3,14
910,resizeIndexObject,build.c,2157,6.37,2,5,1,23,3,0,0,20
911,sqlite3VtabMakeWritable,vtab.c,1222,6.37,3,2,5,17,2,0,2,16
912,sqlite3TestInit,test_tclsh.c,51,6.37,0,43,2,94,1,0,1,11
913,jsonAppendCharExpand,json.c,579,6.36,1,1,2,4,2,0,1,19
914,sqlite3OpenTempDatabase,build.c,5273,6.35,3,2,4,27,1,0,2,16
915,sqlite3ResolveExprNames,resolve.c,2152,6.35,12,2,3,29,2,0,1,16
916,releasePageOne,btree.c,2423,6.33,5,1,1,10,1,0,0,20
917,demoDelete,test_demovfs.c,456,6.33,0,6,4,24,3,0,3,14
918,resolveAttachExpr,attach.c,35,6.32,0,1,3,12,2,0,2,17
919,winDelete,os_win.c,5350,6.32,0,11,16,82,3,0,4,9
920,whereLoopResize,where.c,2548,6.32,3,3,2,12,3,0,1,18
921,pagerBeginReadTransaction,pager.c,3229,6.32,2,4,3,13,1,0,2,16
922,pagerPlaybackSavepoint,pager.c,3389,6.31,1,6,13,65,2,0,2,14
923,sqlite3AuthReadCol,auth.c,104,6.31,1,4,4,21,4,0,2,16
924,multiplexSubFilename,test_multiplex.c,256,6.31,3,6,5,25,2,0,2,15
925,tclSqlFunc,tclsqlite.c,1000,6.30,0,9,16,118,3,0,4,9
926,sqlite3VdbeDeletePriorOpcode,vdbeaux.c,1483,6.30,0,1,2,7,2,0,1,19
927,sqlite3Get4byte,util.c,1441,6.29,89,2,1,5,1,0,0,1
928,test_destructor,test_func.c,95,6.29,0,5,3,21,3,0,1,18
929,test_destructor16,test_func.c,118,6.29,0,5,3,21,3,0,1,18
930,jsonEachPathLength,json.c,5028,6.29,2,2,6,20,1,0,4,12
931,lockTable,build.c,49,6.28,1,2,5,34,5,0,2,16
932,vdbeSorterCompareInt,vdbesort.c,846,6.28,0,1,15,57,6,0,4,11
933,test_prepare,test1.c,4857,6.26,0,12,7,45,4,0,2,14
934,sqlite3SrcListAppendList,build.c,5114,6.26,1,4,3,15,3,0,2,16
935,test_exec,test1.c,421,6.25,0,8,5,40,4,0,2,15
936,sqlite3Reprepare,prepare.c,883,6.25,2,2,3,28,1,0,2,16
937,winLock,os_win.c,3243,6.23,0,13,17,94,2,0,3,10
938,sqlite3FindDb,build.c,935,6.23,2,3,1,8,2,0,0,20
939,sqlite3UnlinkAndDeleteIndex,build.c,550,6.22,1,2,5,21,3,0,3,14
940,sqlite3VdbeAddOp2,vdbeaux.c,269,6.22,8,1,1,3,4,0,0,19
941,sqlite3WhereMalloc,where.c,258,6.21,3,1,2,11,2,0,1,18
942,sqlite3DeleteTrigger,trigger.c,605,6.21,8,6,2,9,2,0,1,16
943,vlogNext,test_osinst.c,965,6.21,0,8,6,45,1,0,5,9
944,sqlite3VdbeCreate,vdbeaux.c,25,6.20,1,2,3,23,1,0,1,18
945,sqlite3BtreeLeave,btmutex.c,143,6.20,61,1,3,10,1,0,2,3
946,pagerUnlockIfUnused,pager.c,5438,6.19,2,2,2,6,1,0,1,18
947,echoBegin,test8.c,1108,6.18,0,2,5,21,1,0,2,16
948,sqlite3SrcListEnlarge,build.c,4746,6.17,3,2,6,40,4,0,2,15
949,sqlite3WhereClauseClear,whereexpr.c,1725,6.17,3,3,7,25,1,0,4,11
950,sqlite3DeleteColumnNames,build.c,744,6.15,3,3,6,23,2,0,3,13
951,substrFunc,func.c,347,6.15,0,6,26,82,3,0,5,6
952,sqlite3ExprListAppendNew,expr.c,2012,6.15,1,2,2,18,2,0,1,18
953,sqlite3RowSetInit,rowset.c,130,6.14,1,2,2,16,1,0,1,18
954,sqlite3_malloc,malloc.c,337,6.14,59,2,3,4,1,0,1,5
955,echoSync,test8.c,1139,6.13,0,2,4,18,1,0,2,16
956,tclFilter,test_bestindex.c,287,6.13,0,3,6,84,5,0,2,15
957,fsFilter,test_fs.c,685,6.13,0,7,4,29,5,0,2,15
958,checkPtrmap,btree.c,10601,6.12,7,4,4,21,4,0,2,14
959,concatwsFunc,func.c,1619,6.12,0,3,2,10,3,0,1,18
960,test_textarray_addr,test1.c,3911,6.12,0,7,4,23,4,0,2,15
961,sqlite3AuthCheck,auth.c,193,6.11,7,3,4,27,5,0,1,16
962,pager_open_journal,pager.c,5798,6.09,1,6,10,53,1,0,4,10
963,sqlite3QuoteValue,func.c,1094,6.09,1,14,6,54,2,0,3,11
964,sqlite3_stmt_explain,vdbeapi.c,2004,6.08,0,3,7,28,2,0,1,17
965,sqlite3IsReadOnly,delete.c,119,6.07,3,3,3,13,3,0,1,17
966,jsonEachOpenEach,json.c,4859,6.06,0,2,1,11,2,0,0,20
967,test_autovacuum_pages,test1.c,8716,6.05,0,9,6,40,4,0,2,14
968,sqlite3WhereExplainOneScan,wherecode.c,243,6.05,3,2,4,22,4,0,2,15
969,callStatGet,analyze.c,934,6.03,5,1,1,6,4,0,0,19
970,testContextMalloc,test_func.c,29,6.02,4,2,2,7,2,0,1,17
971,vdbeRecordCompareString,vdbeaux.c,5044,6.02,0,4,12,59,3,0,4,10
972,test_eval,test_func.c,290,6.02,0,5,4,26,3,0,2,15
973,sqlite3ExprFunction,expr.c,1159,6.02,1,3,4,29,4,0,1,17
974,tclBestIndex,test_bestindex.c,624,6.01,0,3,3,86,2,0,2,15
975,pager_rollback,test2.c,105,6.00,0,5,3,21,4,0,1,17
976,sqlite3VtabArgInit,vtab.c,530,5.99,0,1,1,5,1,0,0,20
977,win32_file_lock,test1.c,7987,5.99,0,16,8,56,4,0,1,14
978,sqlite3ErrorIfNotEmpty,alter.c,293,5.97,4,1,1,11,4,0,0,19
979,tclvarColumn,test_tclvar.c,246,5.97,0,1,7,40,3,0,2,15
980,windowFind,window.c,631,5.95,2,2,4,10,3,0,2,15
981,sqlite3UniqueConstraint,build.c,5420,5.95,3,6,4,30,3,0,3,12
982,testHexToUtf16be,test_func.c,350,5.95,0,5,2,19,3,0,1,17
983,testHexToUtf16le,test_func.c,404,5.95,0,5,2,19,3,0,1,17
984,testHexToUtf8,test_func.c,377,5.95,0,5,2,19,3,0,1,17
985,testSqllogStmt,test_sqllog.c,404,5.94,1,4,2,10,2,0,1,17
986,sqlite3VtabEponymousTableClear,vtab.c,1294,5.91,3,1,2,8,2,0,1,17
987,simulateVtabError,test8.c,93,5.91,11,2,2,11,2,0,1,15
988,sqlite3NotPureFunc,vdbeaux.c,5386,5.90,0,3,4,21,1,0,2,15
989,sqlite3VdbeMemSetRowSet,vdbemem.c,1007,5.90,2,2,1,13,1,0,0,19
990,sqlite3CodeChangeCount,delete.c,51,5.89,3,1,1,6,3,0,0,19
991,sqlite3WhereExplainBloomFilter,wherecode.c,278,5.88,3,9,5,39,3,0,3,11
992,btree_cursor,test3.c,196,5.88,0,17,5,40,4,0,1,14
993,sqlite3ExprFunctionUsable,expr.c,1261,5.87,2,1,3,15,3,0,2,15
994,sqlite3WindowAttach,window.c,1310,5.86,1,2,3,17,3,0,2,15
995,sqlite3_file_control,main.c,4047,5.85,8,10,9,45,4,0,3,9
996,test_exec_hex,test1.c,337,5.85,0,6,5,39,4,0,2,14
997,pagerUndoCallback,pager.c,3095,5.85,1,7,4,20,2,0,3,12
998,sqlite3PExprAddSelect,expr.c,1058,5.83,1,2,2,10,3,0,1,17
999,explainAppendTerm,wherecode.c,43,5.83,2,11,10,25,6,0,2,12
1000,sqlite3VdbeError,vdbeaux.c,59,5.83,22,4,1,7,3,0,0,14
1001,growOp3,vdbeaux.c,227,5.83,1,2,2,6,5,0,1,17
1002,jsonStringOom,json.c,501,5.82,1,2,2,5,1,0,1,17
1003,sqlite3PagerUnrefPageOne,pager.c,5766,5.82,1,2,1,9,1,0,0,19
1004,sqlite3VdbeAddOp0,vdbeaux.c,263,5.82,2,1,1,3,2,0,0,19
1005,sqlite3BtreeEnter,btmutex.c,71,5.82,59,1,3,15,1,0,1,4
1006,lengthFunc,func.c,116,5.82,0,6,6,35,3,0,4,10
1007,btreeParseCellPtr,btree.c,1258,5.79,0,1,13,66,3,0,8,2
1008,likeFunc,func.c,908,5.78,0,13,9,43,3,0,3,10
1009,fixExprCb,attach.c,452,5.78,0,1,4,13,2,0,2,15
1010,sqlite3Expr,expr.c,975,5.76,0,2,1,10,3,0,0,19
1011,pager_stmt_rollback,test2.c,191,5.76,0,6,3,22,4,0,1,16
1012,sqlite3VdbeAddOp1,vdbeaux.c,266,5.76,1,1,1,3,3,0,0,19
1013,explainIndexRange,wherecode.c,87,5.72,1,7,7,22,2,0,2,13
1014,sqlite3BtreeCount,btree.c,10414,5.71,0,8,8,35,3,0,4,9
1015,addModuleArgument,vtab.c,359,5.70,7,2,3,19,3,0,1,15
1016,pager_stmt_commit,test2.c,219,5.70,0,5,3,21,4,0,1,16
1017,sqlite3_complete,complete.c,104,5.70,2,6,38,120,1,0,5,2
1018,tabIsReadOnly,delete.c,98,5.68,1,3,4,13,2,0,1,16
1019,growOpArray,vdbeaux.c,164,5.67,2,2,3,20,2,0,1,16
1020,sqlite3FkClearTriggerCache,fkey.c,704,5.67,2,2,4,13,2,0,2,14
1021,sqlite3ExprSetHeightAndFlags,expr.c,868,5.66,3,2,2,5,2,0,1,16
1022,sqlite3WalkExprNN,walker.c,64,5.65,4,5,12,32,2,0,5,6
1023,sqlite3_win32_set_directory16,os_win.c,1960,5.65,1,3,3,14,2,0,1,16
1024,sqlite3DeleteTriggerStep,trigger.c,19,5.64,2,8,2,14,2,0,1,15
1025,test_bind,test1.c,3178,5.64,0,10,8,41,4,0,1,14
1026,moveToRoot,btree.c,5500,5.62,8,8,12,63,1,0,3,8
1027,sqlite3ExprIsSingleTableConstraint,expr.c,2663,5.61,3,1,10,31,4,0,4,9
1028,sqliteViewResetAll,build.c,3177,5.61,2,1,4,12,2,0,2,14
1029,dbMallocRawFinish,malloc.c,626,5.60,2,2,2,9,2,0,1,16
1030,sqlite3VdbeChangeP5,vdbeaux.c,1295,5.60,71,0,2,4,2,0,1,1
1031,sqlite3VectorErrorMsg,expr.c,3423,5.60,2,2,2,8,2,0,1,16
1032,make_fts3record,test_hexio.c,394,5.59,0,4,9,54,4,0,4,9
1033,sqlite3_stricmp,util.c,408,5.58,64,1,4,8,2,0,1,2
1034,btreeNext,btree.c,6250,5.58,2,8,14,51,1,0,3,9
1035,btree_open,test3.c,36,5.57,0,12,4,40,4,0,1,14
1036,renameWalkTrigger,alter.c,1419,5.56,3,9,6,26,2,0,4,8
1037,sqlite3ParseObjectInit,prepare.c,660,5.56,5,3,2,9,2,0,1,15
1038,exprCodeInlineFunction,expr.c,4441,5.55,1,10,10,81,4,0,2,11
1039,notValidImpl,resolve.c,916,5.54,4,2,4,15,5,0,1,15
1040,echoCommit,test8.c,1167,5.53,0,3,2,13,1,0,1,16
1041,sqlite3RegisterPerConnectionBuiltinFunctions,func.c,2234,5.53,1,2,2,7,1,0,1,16
1042,releaseMemArray,vdbeaux.c,2170,5.53,6,3,8,28,2,0,4,8
1043,echoColumn,test8.c,611,5.53,0,2,3,14,3,0,1,16
1044,pager_end_transaction,pager.c,2027,5.52,5,13,15,75,3,0,4,5
1045,sqlite3ExprDeleteNN,expr.c,1370,5.51,6,7,8,39,2,0,3,9
1046,test_exec_printf,test1.c,299,5.48,0,6,3,30,4,0,1,15
1047,sqlite3_mprintf_hexdouble,test1.c,1608,5.47,0,6,3,27,4,0,1,15
1048,sqlite3WalFrames,wal.c,4231,5.47,2,5,1,15,6,0,0,17
1049,echoOpen,test8.c,553,5.47,0,2,2,9,2,0,1,16
1050,multiplexDelete,test_multiplex.c,611,5.46,0,3,8,41,3,0,4,9
1051,sqlite3ExprCheckHeight,expr.c,788,5.46,5,1,2,11,2,0,1,15
1052,btree_pager_stats,test3.c,146,5.46,0,11,3,32,4,0,1,14
1053,releasePage,btree.c,2420,5.45,50,1,2,3,1,0,1,5
1054,MD5DigestToBase10x8,test_md5.c,295,5.43,0,1,3,11,2,0,2,14
1055,walIndexAppend,wal.c,1288,5.43,2,7,6,28,3,0,3,10
1056,btreePrevious,btree.c,6344,5.42,1,8,12,49,1,0,3,9
1057,echoRowid,test8.c,631,5.42,0,1,2,8,2,0,1,16
1058,walLimitSize,wal.c,2375,5.41,2,3,3,13,2,0,1,15
1059,jsonBlobChangePayloadSize,json.c,1217,5.41,3,3,14,63,3,0,3,9
1060,test_delete_database,test1.c,2827,5.41,0,6,2,17,4,0,1,15
1061,sqlite3ParseObjectReset,prepare.c,571,5.41,7,5,6,22,1,0,1,13
1062,sqlite3FkRequired,fkey.c,1143,5.41,6,5,10,33,4,0,5,5
1063,concatFunc,func.c,1604,5.41,0,1,1,7,3,0,0,18
1064,sqlite3_mprintf_str,test1.c,1462,5.40,0,4,4,21,4,0,1,15
1065,defragmentPage,btree.c,1608,5.39,2,16,19,106,2,0,5,2
1066,btree_next,test3.c,287,5.39,0,9,4,31,4,0,1,14
1067,jsonBlobEdit,json.c,2515,5.39,10,3,5,21,5,0,3,9
1068,vdbePmaReaderIncrMergeInit,vdbesort.c,2191,5.39,3,3,8,37,2,0,3,10
1069,sqlite3_set_clientdata,main.c,3768,5.38,0,12,8,41,4,0,3,9
1070,sqlite3IndexedByLookup,select.c,5530,5.38,2,2,3,19,2,0,1,15
1071,sqlite3_mprintf_long,test1.c,1431,5.36,0,4,3,25,4,0,1,15
1072,sqlite3SelectWrongNumTermsError,select.c,3239,5.36,2,3,2,9,2,0,1,15
1073,sqlite3_mprintf_double,test1.c,1523,5.36,0,4,3,23,4,0,1,15
1074,memjrnlWrite,memjournal.c,187,5.36,0,5,10,55,4,0,5,6
1075,sqlite3_mprintf_scaled,test1.c,1554,5.36,0,4,3,22,4,0,1,15
1076,printExplainQueryPlan,test1.c,7647,5.35,0,4,3,21,1,0,1,15
1077,sqlite3_mprintf_int,test1.c,1371,5.35,0,4,3,21,4,0,1,15
1078,explainSimpleCount,select.c,7023,5.35,2,1,4,14,3,0,1,15
1079,sqlite3ExprAttachSubtrees,expr.c,992,5.35,1,2,5,29,4,0,3,11
1080,winAccess,os_win.c,5458,5.35,1,8,9,72,4,0,3,9
1081,sqlite3_malloc64,malloc.c,343,5.34,34,2,2,4,1,0,1,8
1082,btree_first,test3.c,326,5.34,0,9,3,27,4,0,1,14
1083,isRealTable,alter.c,566,5.34,1,1,5,17,3,0,1,15
1084,sqlite3VdbeCheckFk,vdbeaux.c,3268,5.33,3,1,2,13,2,0,1,15
1085,sqlite3VdbeMakeLabel,vdbeaux.c,612,5.33,76,0,1,3,1,0,0,1
1086,makeColumnPartOfPrimaryKey,build.c,1771,5.32,3,1,2,9,2,0,1,15
1087,sqlite3SubselectError,expr.c,3404,5.32,3,1,2,6,3,0,1,15
1088,winLogErrorAtLine,os_win.c,2085,5.31,1,2,3,21,5,0,1,15
1089,sqlite3_mprintf_stronly,test1.c,1584,5.30,0,4,2,17,4,0,1,15
1090,sqlthread_open,test_thread.c,268,5.30,0,4,1,21,4,0,0,17
1091,timediffFunc,date.c,1608,5.30,0,13,16,90,3,0,3,7
1092,vtabIsReadOnly,delete.c,77,5.30,1,2,3,14,2,0,1,15
1093,ptrChngFunction,test1.c,914,5.30,0,21,14,42,3,0,1,10
1094,sqlite3ReleaseTempReg,expr.c,7275,5.29,57,0,3,8,2,0,2,1
1095,isAsteriskTerm,trigger.c,901,5.29,1,1,4,13,2,0,1,15
1096,vdbePmaReaderNext,vdbesort.c,678,5.27,5,5,7,31,1,0,3,9
1097,sqlite3_snprintf_str,test1.c,1489,5.27,0,6,5,28,4,0,1,14
1098,walFindFrame,wal.c,3488,5.26,2,7,8,45,3,0,3,9
1099,absFunc,func.c,194,5.25,0,7,5,28,3,0,3,10
1100,cannotBeFunction,select.c,5641,5.25,2,1,2,7,2,0,1,15
1101,disconnectAllVtab,main.c,1188,5.25,1,5,7,22,1,0,4,8
1102,sqlite3SrcListDelete,build.c,4927,5.25,20,10,11,29,2,0,2,6
1103,test_mprintf_z,test1.c,495,5.25,0,3,2,15,4,0,1,15
1104,sqlite3UnlinkAndDeleteTable,build.c,857,5.24,1,2,1,13,3,0,0,17
1105,optimizeAggregateUseOfIndexedExpr,select.c,6608,5.24,1,1,4,23,4,0,3,11
1106,test_bind_blob,test1.c,4207,5.24,0,5,5,40,4,0,1,14
1107,pager_open,test2.c,41,5.24,0,7,3,31,4,0,1,14
1108,isAlterableTable,alter.c,31,5.24,0,3,2,12,2,0,1,15
1109,checkRef,btree.c,10582,5.24,3,4,3,12,2,0,1,14
1110,test_prepare_tkt3134,test1.c,5059,5.23,0,6,4,32,4,0,1,14
1111,hasHotJournal,pager.c,5101,5.22,1,5,13,55,2,0,6,3
1112,file_control_win32_set_handle,test1.c,6908,5.22,0,6,4,27,4,0,1,14
1113,sqlite3WalkExpr,walker.c,98,5.22,46,1,2,3,2,0,0,7
1114,sqlite3_randomness,random.c,59,5.21,15,6,8,44,2,0,2,8
1115,vdbeMergeEngineInit,vdbesort.c,2115,5.20,2,3,5,26,3,0,2,12
1116,sqlite3VdbeCurrentAddr,vdbeaux.c,1063,5.20,74,0,1,4,1,0,0,1
1117,sqlite3ExprListCheckLength,expr.c,2239,5.20,1,1,2,12,3,0,1,15
1118,sqlite3SchemaToIndex,prepare.c,541,5.18,46,0,4,14,2,0,3,1
1119,pager_stats,test2.c,246,5.18,0,6,3,27,4,0,1,14
1120,btree_eof,test3.c,361,5.18,0,7,2,22,4,0,1,14
1121,btree_payload_size,test3.c,390,5.18,0,7,2,22,4,0,1,14
1122,winGetLastError,os_win.c,6056,5.17,0,2,2,6,3,0,1,15
1123,file_control_data_version,test1.c,6659,5.16,0,4,5,29,4,0,1,14
1124,createIncrblobChannel,tclsqlite.c,451,5.15,0,5,3,38,7,0,1,14
1125,sqlite3UpsertDelete,upsert.c,33,5.15,5,1,2,3,2,0,1,14
1126,walBeginShmUnreliable,wal.c,2759,5.13,1,9,15,84,2,0,2,9
1127,btree_from_db,test3.c,512,5.12,0,4,4,30,4,0,1,14
1128,disallowAggregatesInOrderByCb,window.c,942,5.12,0,1,2,8,2,0,1,15
1129,vfslogFileControl,test_osinst.c,395,5.12,0,1,2,8,3,0,1,15
1130,file_control_win32_av_retry,test1.c,6843,5.12,0,5,3,25,4,0,1,14
1131,page_lookup,test2.c,342,5.12,0,5,3,24,4,0,1,14
1132,file_control_persist_wal,test1.c,6944,5.12,0,5,3,24,4,0,1,14
1133,file_control_powersafe_overwrite,test1.c,6976,5.12,0,5,3,24,4,0,1,14
1134,file_control_win32_get_handle,test1.c,6876,5.12,0,5,3,24,4,0,1,14
1135,get_sqlite_pointer,test1.c,85,5.12,0,5,3,23,4,0,1,14
1136,sqlite3VMPrintf,printf.c,1189,5.12,7,3,2,15,3,0,1,13
1137,sqlite3_win32_set_directory,os_win.c,1982,5.11,0,1,1,6,2,0,0,17
1138,sqlite3TestErrCode,test1.c,147,5.11,0,6,2,14,3,0,1,14
1139,sqlite3DeleteTableGeneric,build.c,848,5.10,0,1,1,3,2,0,0,17
1140,sqlite3SystemError,util.c,155,5.10,4,4,7,22,2,0,4,7
1141,sqlite3WalkSelect,walker.c,206,5.10,30,4,7,19,2,0,2,5
1142,sqlite3PagerSetPagesize,pager.c,3737,5.09,7,8,9,44,3,0,3,7
1143,sqlite3StrAccumEnlarge,printf.c,952,5.08,4,6,9,44,2,0,3,8
1144,fkTriggerDelete,fkey.c,688,5.08,5,5,2,10,2,0,1,13
1145,sqlite3_auto_extension,loadext.c,784,5.08,5,2,6,33,1,0,3,9
1146,sqlite3_bind_value,vdbeapi.c,1826,5.07,2,1,4,34,3,0,2,12
1147,quotaStrglob,test_quota.c,256,5.07,4,2,28,66,2,0,5,2
1148,pager_pagecount,test2.c,279,5.07,0,5,2,20,4,0,1,14
1149,readDbPage,pager.c,3004,5.07,2,4,7,35,1,0,2,11
1150,page_number,test2.c,441,5.06,0,5,2,18,4,0,1,14
1151,sqlite3FreeIndex,build.c,530,5.06,4,6,2,8,2,0,1,13
1152,test_snprintf_int,test1.c,542,5.06,0,5,2,16,4,0,1,14
1153,rankfunc,test_func.c,846,5.06,0,6,8,38,3,0,3,9
1154,sqlite3_quota_file,test_quota.c,891,5.05,1,6,7,37,1,0,4,7
1155,multiplexFilename,test_multiplex.c,219,5.02,1,3,2,16,5,0,1,14
1156,get_autocommit,test1.c,5903,5.01,0,4,2,18,4,0,1,14
1157,test_last_rowid,test1.c,632,5.01,0,4,2,17,4,0,1,14
1158,renameColumnParseError,alter.c,1061,5.00,3,5,2,17,5,0,0,15
1159,jsonBlobAppendNode,json.c,1176,5.00,16,2,6,38,4,0,1,10
1160,sqlite3ExprAffinity,expr.c,45,4.99,29,5,10,47,1,0,2,4
1161,vdbeSorterCompareText,vdbesort.c,804,4.97,0,4,8,33,6,0,2,11
1162,tclvarBestIndex,test_tclvar.c,347,4.97,0,6,13,48,2,0,4,6
1163,sqlite3AtoF,util.c,529,4.95,4,7,41,141,4,0,3,2
1164,dbPrepareAndBind,tclsqlite.c,1373,4.92,1,15,22,138,4,0,4,2
1165,walBeginReadTransaction,wal.c,3340,4.91,1,1,2,14,2,0,1,14
1166,sqlite3Realloc,malloc.c,524,4.90,13,8,9,45,2,0,3,5
1167,Md5_Register,test_md5.c,434,4.89,1,1,1,10,3,0,0,16
1168,DbBusyHandler,tclsqlite.c,666,4.89,0,2,2,11,2,0,1,14
1169,hex16Func,test1.c,764,4.89,0,2,2,11,3,0,1,14
1170,hex8Func,test1.c,752,4.89,0,2,2,11,3,0,1,14
1171,quotaDelete,test_quota.c,486,4.87,0,6,5,28,3,0,4,7
1172,upsertDelete,upsert.c,21,4.87,1,6,2,12,2,0,1,13
1173,sqlite3WalBeginReadTransaction,wal.c,3459,4.87,2,5,1,8,2,0,0,15
1174,demoFullPathname,test_demovfs.c,533,4.85,0,1,2,17,4,0,1,14
1175,modifyPagePointer,btree.c,3834,4.83,1,14,13,49,4,0,5,2
1176,sqlite3_errmsg,main.c,2637,4.83,33,7,6,22,1,0,2,3
1177,sqlite3AffinityType,build.c,1623,4.82,4,1,17,55,2,0,5,3
1178,sqlite3DbRealloc,malloc.c,721,4.82,10,2,7,14,3,0,3,7
1179,json5Whitespace,json.c,953,4.81,4,0,30,90,1,0,5,1
1180,sqlite3PagerDirectReadOk,pager.c,802,4.81,1,2,6,17,2,0,2,11
1181,triggersReallyExist,trigger.c,770,4.81,1,2,13,56,5,0,5,4
1182,exprListDeleteNN,expr.c,2255,4.79,2,3,3,12,2,0,2,11
1183,resolveOutOfRangeError,resolve.c,1567,4.78,3,2,1,12,5,0,0,15
1184,sqlite3FixTriggerStep,attach.c,584,4.78,1,8,5,28,2,0,3,8
1185,pagerRollbackWal,pager.c,3130,4.78,2,3,2,13,1,0,1,13
1186,bytelengthFunc,func.c,155,4.78,0,10,3,32,3,0,2,10
1187,sqlite3WindowOffsetExpr,window.c,1165,4.78,2,3,3,8,2,0,2,11
1188,sqlite3_overload_function,main.c,2158,4.78,2,3,1,16,3,0,0,15
1189,writeJournalHdr,pager.c,1433,4.77,2,10,6,42,1,0,2,9
1190,memdbOpen,memdb.c,542,4.77,0,14,10,75,5,0,3,6
1191,sqlite3ReportError,main.c,3848,4.77,3,2,1,5,3,0,0,15
1192,jsonWrongNumArgs,json.c,1068,4.76,2,3,1,9,2,0,0,15
1193,sqlite3_vmprintf,printf.c,1222,4.76,1,4,2,10,2,0,1,13
1194,sqlite3DbNNFreeNN,malloc.c,479,4.74,36,2,5,29,2,0,2,3
1195,sqlite3ExprListAppendGrow,expr.c,2031,4.74,1,3,2,22,3,0,1,13
1196,exprListIsNoAffinity,insert.c,612,4.71,1,2,4,11,2,0,2,11
1197,vdbeRecordCompareInt,vdbeaux.c,4949,4.71,0,3,14,73,3,0,1,11
1198,sqlite3VdbeMemSetInt64,vdbemem.c,937,4.70,25,1,2,8,2,0,1,8
1199,sqlite3InvalidFunction,main.c,2132,4.70,0,4,1,13,3,0,0,15
1200,test_mprintf_n,test1.c,518,4.70,0,4,1,13,4,0,0,15
1201,echoTransactionCall,test8.c,1098,4.69,0,4,1,10,2,0,0,15
1202,vdbeUnbind,vdbeapi.c,1636,4.68,6,9,6,28,2,0,1,10
1203,sqlite3_vtab_config,vtab.c,1331,4.68,0,7,4,39,3,0,2,10
1204,vdbePmaReaderIncrInit,vdbesort.c,2279,4.68,1,2,3,15,2,0,2,11
1205,sqlite3WalUndo,wal.c,3734,4.67,2,6,3,20,3,0,2,10
1206,sqlite3HashInsert,hash.c,238,4.66,16,6,8,32,3,0,2,6
1207,sqlite3_vsnprintf,printf.c,1271,4.65,1,2,2,8,4,0,1,13
1208,sqlite3ExprOrderByAggregateError,expr.c,1193,4.65,2,1,1,5,2,0,0,15
1209,sqliteAuthBadReturnCode,auth.c,90,4.65,2,1,1,4,1,0,0,15
1210,explainTempTable,select.c,1629,4.65,2,1,1,3,2,0,0,15
1211,winUnlock,os_win.c,3422,4.64,0,5,6,30,2,0,2,10
1212,sqlite3VdbeMemSetNull,vdbemem.c,881,4.64,33,1,2,7,1,0,1,6
1213,hexio_get_int,test_hexio.c,196,4.63,0,9,7,45,4,0,2,9
1214,winDlError,os_win.c,5842,4.63,1,2,1,4,3,0,0,15
1215,errlogFunc,func.c,1027,4.63,0,3,1,9,3,0,0,15
1216,sqlite3SelectDelete,select.c,184,4.63,24,1,2,3,2,0,1,8
1217,sqlite3ValueFree,vdbemem.c,2024,4.62,23,2,2,5,1,0,1,8
1218,sqlite3_value_dup,vdbeapi.c,345,4.60,5,4,6,21,1,0,2,9
1219,sqlite3WalkExprList,walker.c,106,4.60,32,1,4,10,2,0,3,2
1220,whereInfoFree,where.c,2594,4.59,2,4,3,16,2,0,1,12
1221,jsonParseFree,json.c,860,4.58,22,2,3,10,1,0,2,6
1222,sqlite3_str_appendall,printf.c,1047,4.57,18,2,1,3,2,0,0,11
1223,sqlite3ExprImpliesNonNullRow,expr.c,6712,4.57,2,3,5,21,3,0,3,8
1224,analyzeAggFuncArgs,select.c,6573,4.57,2,3,4,25,2,0,2,10
1225,vdbeIncrSwap,vdbesort.c,1956,4.56,1,3,6,26,1,0,3,8
1226,sqlite3SrcListFuncArgs,build.c,5134,4.54,0,1,2,12,3,0,1,13
1227,test_autovacuum_pages_callback,test1.c,8679,4.54,0,6,1,27,5,0,0,14
1228,sqlite3MallocZero,malloc.c,602,4.53,35,2,2,7,1,0,1,5
1229,sqlite3_result_int,vdbeapi.c,496,4.52,27,1,1,4,2,0,0,9
1230,out2Prerelease,vdbe.c,670,4.52,22,1,2,13,2,0,1,8
1231,sqlite3VdbeSorterNext,vdbesort.c,2635,4.52,1,3,4,30,2,0,2,10
1232,memdbClose,memdb.c,210,4.50,0,8,8,36,1,0,5,3
1233,sqlite3_quota_fopen,test_quota.c,936,4.50,1,15,9,41,2,0,2,7
1234,sqlite3PagerBegin,pager.c,5889,4.50,1,6,8,36,3,0,4,5
1235,pageFreeArray,btree.c,7722,4.49,2,2,11,53,4,0,5,3
1236,test_wal_checkpoint_v2,test1.c,7470,4.48,0,7,5,45,4,0,1,11
1237,sqlite3VdbeFinishMoveto,vdbeaux.c,3782,4.48,2,2,3,12,1,0,1,12
1238,impliesNotNullRow,expr.c,6581,4.45,0,4,7,80,2,0,2,9
1239,vlogColumn,test_osinst.c,1033,4.45,0,3,4,28,3,0,2,10
1240,sqlite3BtreeSetPageSize,btree.c,3062,4.44,6,5,6,28,4,0,2,8
1241,statPush,analyze.c,701,4.44,0,3,4,24,3,0,2,10
1242,quoteFunc,func.c,1163,4.41,0,4,2,14,3,0,1,12
1243,sqlite3_strnicmp,util.c,435,4.41,50,0,6,12,3,0,1,1
1244,sqlite3FpDecode,util.c,1017,4.41,1,7,22,100,4,0,4,2
1245,editPage,btree.c,7792,4.40,1,13,15,74,5,0,3,4
1246,walCleanupHash,wal.c,1226,4.38,3,3,5,25,1,0,2,9
1247,exprListIsConstant,insert.c,600,4.38,2,1,3,7,2,0,2,10
1248,exprSelectUsage,whereexpr.c,965,4.38,3,8,7,27,2,0,4,4
1249,unixepochFunc,date.c,1165,4.37,0,3,3,15,3,0,2,10
1250,freeSpace,btree.c,1913,4.37,3,9,17,73,3,0,4,2
1251,whereOmitNoopJoin,where.c,6347,4.37,1,3,14,66,2,0,4,4
1252,sqlite3ExprUnmapAndDelete,expr.c,1458,4.37,1,2,3,8,2,0,2,10
1253,dstrAppend,test1.c,789,4.37,2,5,4,19,3,0,2,9
1254,getOverflowPage,btree.c,4961,4.36,2,6,9,42,4,0,3,6
1255,fsOpen,test_onefile.c,584,4.35,0,7,16,87,5,0,4,3
1256,sqlite3_expanded_sql,vdbeapi.c,2118,4.35,0,3,2,11,1,0,1,12
1257,sqlite3TestMakePointerStr,test1.c,187,4.35,2,1,1,4,3,0,0,14
1258,test_alloc_mutex,test_mutex.c,310,4.35,0,3,1,13,4,0,0,14
1259,quotaOpen,test_quota.c,424,4.35,0,5,6,44,5,0,3,7
1260,sqlite3BtreeNext,btree.c,6304,4.33,7,3,4,19,2,0,1,10
1261,sqlite3_wal_checkpoint_v2,main.c,2479,4.32,1,7,7,39,5,0,1,10
1262,pcache1TruncateUnsafe,pcache1.c,648,4.31,2,2,7,38,2,0,4,5
1263,execFuncCallback,test1.c,812,4.31,0,2,3,12,4,0,2,10
1264,readJournalHdr,pager.c,1574,4.31,2,3,10,55,5,0,2,8
1265,DbProfileHandler,tclsqlite.c,797,4.30,0,2,1,13,3,0,0,14
1266,pagerOpenWalIfPresent,pager.c,3322,4.29,2,2,7,27,1,0,4,5
1267,pager_playback_one_page,pager.c,2273,4.29,2,10,19,104,5,0,2,5
1268,sqlite3_result_int64,vdbeapi.c,503,4.26,23,1,1,4,2,0,0,9
1269,clearSelect,select.c,82,4.26,3,11,6,24,3,0,2,7
1270,memdbFullPathname,memdb.c,657,4.24,0,1,1,10,4,0,0,14
1271,jsonbPayloadSize,json.c,2053,4.24,34,0,11,57,3,0,2,1
1272,sqlite3ExprListDeleteGeneric,expr.c,2270,4.23,0,1,2,3,2,0,1,12
1273,sqlite3GetTempReg,expr.c,7264,4.23,50,0,2,6,1,0,1,1
1274,quotaFindFile,test_quota.c,347,4.23,4,5,5,25,3,0,3,6
1275,ptrToText,test_blob.c,33,4.22,0,1,1,5,1,0,0,14
1276,demoDlError,test_demovfs.c,568,4.22,0,1,1,4,3,0,0,14
1277,sqlite3ExprAnalyzeAggList,expr.c,7251,4.22,4,1,3,9,2,0,2,9
1278,btreeCursor,btree.c,4643,4.22,2,3,8,53,5,0,2,8
1279,vdbeCompareMemString,vdbeaux.c,4436,4.21,2,8,3,30,4,0,2,8
1280,sqlite3_value_bytes,vdbeapi.c,195,4.21,59,0,1,3,1,0,0,1
1281,computeMxChoice,where.c,5493,4.18,1,0,21,64,1,0,5,1
1282,sqlite3_bind_pointer,vdbeapi.c,1772,4.17,0,4,3,19,5,0,1,11
1283,renderLogMsg,printf.c,1319,4.16,1,4,1,8,3,0,0,13
1284,sqlite3VdbeMemRelease,vdbemem.c,597,4.14,21,1,2,6,1,0,1,7
1285,btreeLast,btree.c,5673,4.14,1,2,4,18,2,0,2,9
1286,pcache1ResizeHash,pcache1.c,539,4.14,2,4,7,30,1,0,3,6
1287,sqlite3ResolvePartIdxLabel,delete.c,1026,4.14,3,1,2,5,2,0,1,11
1288,dbReallocFinish,malloc.c,737,4.14,1,7,5,25,3,0,3,6
1289,pager_delsuper,pager.c,2520,4.13,1,4,13,73,2,0,3,5
1290,resolveP2Values,vdbeaux.c,873,4.13,2,1,9,73,2,0,4,4
1291,vfs_unlink_test,test1.c,6415,4.13,0,1,4,63,4,0,2,9
1292,sqlite3_bind_int64,vdbeapi.c,1751,4.13,1,3,2,11,3,0,1,11
1293,sqlite3_bind_zeroblob,vdbeapi.c,1860,4.13,1,3,2,11,3,0,1,11
1294,decodeIntArray,analyze.c,1519,4.11,5,6,12,47,5,0,3,4
1295,pager_unlock,pager.c,1836,4.10,4,8,10,50,1,0,2,6
1296,sqlite3TriggerColmask,trigger.c,1526,4.10,4,2,6,34,7,0,4,4
1297,sqlite3VdbeRecordUnpack,vdbeaux.c,4230,4.10,5,5,6,36,4,0,2,7
1298,test_decode,test_func.c,517,4.10,0,11,5,71,3,0,3,5
1299,resizeResolveLabel,vdbeaux.c,621,4.09,1,2,3,14,3,0,2,9
1300,hexio_write,test_hexio.c,147,4.09,0,11,5,40,4,0,1,9
1301,sqlite3_quota_fclose,test_quota.c,1054,4.09,1,7,4,22,1,0,3,6
1302,sqlite3Error,util.c,130,4.08,11,1,2,9,2,0,1,9
1303,tmpWrite,test_onefile.c,283,4.08,0,2,4,20,4,0,2,9
1304,pcache1FetchStage2,pcache1.c,876,4.07,1,7,8,56,3,0,2,7
1305,sqlite3_bind_double,vdbeapi.c,1737,4.06,0,3,2,11,3,0,1,11
1306,callFinaliser,vtab.c,969,4.05,0,2,4,20,2,0,3,7
1307,sqlite3BtreeCursor,btree.c,4723,4.05,5,2,2,13,5,0,1,10
1308,sqlite3DeleteReturning,build.c,1388,4.04,0,3,1,8,2,0,0,13
1309,sqlite3DbFreeNN,malloc.c,440,4.03,16,2,6,31,2,0,3,3
1310,pcache1FetchNoMutex,pcache1.c,1004,4.03,3,2,5,21,3,0,2,8
1311,vfs_unregister_all,test1.c,6545,4.03,0,2,3,15,4,0,2,9
1312,vdbeSorterCompare,vdbesort.c,785,4.01,0,2,2,13,6,0,1,11
1313,sqlite3WalSavepointUndo,wal.c,3791,4.01,2,4,3,19,2,0,1,10
1314,sqlite3_bind_null,vdbeapi.c,1762,4.00,0,2,2,10,2,0,1,11
1315,whereIndexedExprCleanup,where.c,3756,4.00,0,2,2,9,2,0,1,11
1316,walIndexPageRealloc,wal.c,747,4.00,1,2,7,44,3,0,3,6
1317,walHashGet,wal.c,1160,4.00,4,1,4,21,3,0,2,8
1318,sqlite3Put4byte,util.c,1459,3.99,54,2,1,4,2,0,0,1
1319,sqlite3ExprDataType,expr.c,105,3.99,4,3,7,59,1,0,3,5
1320,test_decode_hexdb,test1.c,8601,3.99,0,13,15,65,4,0,4,1
1321,test_doublearray_addr,test1.c,3877,3.97,0,6,4,24,4,0,3,6
1322,test_intarray_addr,test1.c,3807,3.97,0,6,4,24,4,0,3,6
1323,fkParentIsModified,fkey.c,825,3.97,3,1,7,23,4,0,5,2
1324,whereIsCoveringIndex,where.c,3709,3.96,0,2,8,37,3,0,3,6
1325,setAllPagerFlags,pragma.c,241,3.96,4,1,4,19,1,0,3,6
1326,opendir,test_windirent.c,50,3.95,1,12,7,32,1,0,2,6
1327,pager_write_pagelist,pager.c,4397,3.94,3,3,8,48,2,0,3,5
1328,sqlite3BtreePrevious,btree.c,6396,3.93,4,1,2,15,2,0,1,10
1329,sqlite3_quota_set,test_quota.c,838,3.93,1,11,7,48,5,0,2,6
1330,test_int64array_addr,test1.c,3841,3.93,0,5,4,26,4,0,3,6
1331,jsonBlobAppendOneByte,json.c,1144,3.91,4,1,2,7,2,0,1,10
1332,file_control_test,test1.c,6587,3.90,0,5,2,25,4,0,1,10
1333,Sqlitetest_func_Init,test_func.c,932,3.90,1,4,2,19,1,0,1,10
1334,sqlite3_hard_heap_limit64,malloc.c,137,3.89,1,3,4,15,1,0,2,8
1335,columnTypeImpl,select.c,1901,3.88,5,3,10,69,2,0,4,2
1336,jsonUnescapeOneChar,json.c,2585,3.87,5,7,24,70,3,0,2,3
1337,moveToRightmost,btree.c,5611,3.87,2,2,3,17,1,0,2,8
1338,getAndInitPage,btree.c,2369,3.86,5,7,5,34,4,0,2,6
1339,signFunc,func.c,2522,3.86,0,3,4,14,3,0,1,10
1340,sqlite3ClearOnOrUsing,expr.c,1431,3.86,1,2,4,8,2,0,1,10
1341,sqlite3BtreeLast,btree.c,5691,3.85,3,1,2,10,2,0,1,10
1342,bothImplyNotNullRow,expr.c,6558,3.85,2,2,3,9,3,0,2,8
1343,vdbeSorterCompareTail,vdbesort.c,758,3.85,2,2,2,13,6,0,1,10
1344,vdbePmaReaderInit,vdbesort.c,725,3.84,0,3,3,24,5,0,1,10
1345,sqlite3_result_double,vdbeapi.c,471,3.84,21,1,1,4,2,0,0,8
1346,sqlite3BitvecClear,bitvec.c,240,3.83,3,2,9,33,3,0,5,1
1347,autoinstall_test_funcs,test_func.c,717,3.83,0,4,2,14,4,0,1,10
1348,zeroblobFunc,func.c,1358,3.82,0,3,3,16,3,0,1,10
1349,vdbeMergeEngineFree,vdbesort.c,1201,3.82,6,2,3,9,1,0,2,7
1350,sqlite3_value_int,vdbeapi.c,204,3.80,34,1,1,3,1,0,0,5
1351,wherePathMatchSubqueryOB,where.c,4928,3.80,1,0,13,49,7,0,5,1
1352,sqlite3_str_appendchar,printf.c,1002,3.79,6,1,3,7,3,0,1,9
1353,pagerOpenSavepoint,pager.c,6886,3.79,1,4,6,38,2,0,2,7
1354,winGetReadLock,os_win.c,3161,3.78,3,4,3,20,1,0,1,9
1355,sqlite3LockAndPrepare,prepare.c,835,3.78,0,7,4,31,7,0,2,7
1356,sqlite3_db_release_memory,main.c,884,3.78,2,6,3,15,1,0,2,7
1357,computeJD,date.c,256,3.78,25,1,7,38,1,0,2,2
1358,randStr,test_func.c,41,3.78,0,4,7,29,3,0,1,9
1359,jsonBlobMakeEditable,json.c,1112,3.78,4,2,4,16,2,0,1,9
1360,isDate,date.c,1097,3.78,1,7,9,36,4,0,2,6
1361,vdbePmaReaderSeek,vdbesort.c,631,3.77,2,3,6,38,4,0,3,5
1362,vdbeIncrFree,vdbesort.c,1215,3.77,2,5,5,11,1,0,3,5
1363,compileoptionusedFunc,func.c,1043,3.77,0,3,2,12,3,0,1,10
1364,unicodeFunc,func.c,1182,3.76,0,3,2,9,3,0,1,10
1365,sqlite3_quota_shutdown,test_quota.c,798,3.76,1,6,3,19,0,0,1,9
1366,sqlite3Malloc,malloc.c,317,3.76,26,3,3,14,1,0,1,4
1367,testpcacheFetch,test_pcache.c,191,3.74,0,14,14,66,3,0,3,2
1368,sqlite3BtreeFirst,btree.c,5634,3.74,4,2,3,16,2,0,1,9
1369,sqlite3VtabBegin,vtab.c,1041,3.73,1,2,9,32,2,0,4,3
1370,sqlite3VdbeDeleteAuxData,vdbeaux.c,3687,3.72,4,2,4,19,4,0,3,5
1371,sqlite3VtabUnlockList,vtab.c,310,3.71,5,1,3,13,1,0,2,7
1372,memdbLock,memdb.c,368,3.71,0,2,9,50,2,0,4,3
1373,vdbePmaReadVarint,vdbesort.c,581,3.71,2,4,5,21,2,0,4,3
1374,sqlite3TestTextToPtr,test1.c,57,3.69,33,3,4,21,1,0,1,2
1375,sqlite3BtreeLockTable,btree.c,11323,3.68,2,4,3,16,3,0,2,7
1376,doWalCallbacks,vdbeapi.c,723,3.68,1,5,4,17,1,0,3,5
1377,sqlite3VtabUnlock,vtab.c,203,3.67,8,2,3,16,1,0,2,6
1378,pcache1Create,pcache1.c,766,3.67,0,2,7,42,3,0,2,7
1379,vdbeIncrPopulate,vdbesort.c,1880,3.67,3,6,5,28,1,0,2,6
1380,sqlite3VtabClear,vtab.c,340,3.67,2,3,5,12,2,0,3,5
1381,sqlite3Atoi64,util.c,798,3.66,3,1,23,80,4,0,3,2
1382,jsonLabelCompareEscaped,json.c,2664,3.66,1,4,10,51,6,0,3,4
1383,statGet,analyze.c,817,3.66,0,5,4,23,3,0,2,7
1384,intarrayColumn,test_intarray.c,131,3.65,0,1,2,8,3,0,1,10
1385,c_realloc_test,test9.c,66,3.65,0,4,4,28,4,0,1,9
1386,winDlOpen,os_win.c,5801,3.65,1,4,3,18,2,0,1,9
1387,pager_stmt_begin,test2.c,164,3.64,0,5,3,21,4,0,1,9
1388,sqlite3ExprDeleteGeneric,expr.c,1424,3.64,0,1,2,3,2,0,1,10
1389,pageFindSlot,btree.c,1742,3.64,2,4,9,44,3,0,4,2
1390,unsetJoinExpr,select.c,451,3.64,5,2,8,23,3,0,4,2
1391,winConvertFromUtf8Filename,os_win.c,4702,3.63,7,3,2,10,1,0,1,8
1392,sqlite3_bind_int,vdbeapi.c,1748,3.63,0,1,1,3,3,0,0,12
1393,demoOpen,test_demovfs.c,393,3.63,0,4,10,51,5,0,2,6
1394,sqlite3ReferencesSrcList,expr.c,6881,3.62,1,6,6,33,3,0,1,8
1395,sqlite3FindDbName,build.c,915,3.61,11,2,5,11,2,0,3,3
1396,sqlite3SelectPrep,select.c,6513,3.61,9,3,5,15,3,0,1,7
1397,sqlite3WalOpen,wal.c,1634,3.61,1,4,7,68,6,0,2,6
1398,mallocWithAlarm,malloc.c,246,3.60,1,2,6,32,2,0,4,3
1399,sqlite3VdbeSorterReset,vdbesort.c,1232,3.59,2,6,4,27,2,0,1,8
1400,btreeGetUnusedPage,btree.c,2443,3.59,5,4,3,19,4,0,2,6
1401,moveToChild,btree.c,5400,3.59,8,4,4,28,2,0,1,7
1402,sqlite3BtreeEnterAll,btmutex.c,200,3.58,17,1,2,3,1,0,1,6
1403,sqlite3_config,main.c,421,3.58,14,6,7,147,2,0,2,2
1404,sqlite3SetJoinExpr,select.c,416,3.57,6,2,5,20,3,0,4,2
1405,sqlite3_drop_modules,vtab.c,140,3.55,1,2,5,14,2,0,3,5
1406,allocateSpace,btree.c,1814,3.55,2,5,11,61,3,0,3,3
1407,sqlite3RCStrNew,printf.c,1423,3.55,3,1,2,6,1,0,1,9
1408,termCanDriveIndex,where.c,896,3.55,2,3,7,24,3,0,1,8
1409,sqlite3_result_zeroblob64,vdbeapi.c,649,3.55,2,2,2,11,2,0,1,9
1410,btreeComputeFreeSpace,btree.c,2085,3.55,8,5,9,53,1,0,3,2
1411,sqlite3VdbeSorterClose,vdbesort.c,1265,3.54,1,3,2,11,2,0,1,9
1412,compoundHasDifferentAffinities,select.c,4151,3.53,1,2,4,23,1,0,3,5
1413,vdbeSorterAddToTree,vdbesort.c,2366,3.53,0,2,3,39,5,0,1,9
1414,sqlite3_value_blob,vdbeapi.c,182,3.53,24,1,4,13,1,0,2,2
1415,sqlite3VtabDisconnect,vtab.c,272,3.51,2,1,3,14,2,0,2,7
1416,jsonBlobExpand,json.c,1087,3.50,5,1,4,16,2,0,1,8
1417,winShmPurge,os_win.c,3846,3.49,2,8,7,41,2,0,3,3
1418,sqlite3RCStrResize,printf.c,1434,3.49,1,2,2,14,2,0,1,9
1419,sqlite3WalFindFrame,wal.c,3600,3.49,4,4,1,12,3,0,0,10
1420,sqlite3BtreeLeaveAll,btmutex.c,212,3.48,20,1,2,3,1,0,1,5
1421,sqlite3_realloc,malloc.c,584,3.48,5,2,3,5,2,0,1,8
1422,sqlite3ColumnSetColl,build.c,704,3.48,2,5,3,21,3,0,1,8
1423,jsonBlobExpandAndAppendNode,json.c,1156,3.48,1,2,2,9,4,0,1,9
1424,freeIndexInfo,where.c,1628,3.48,0,3,2,14,2,0,1,9
1425,sqlite3TransferBindings,vdbeapi.c,1930,3.48,0,3,2,13,2,0,1,9
1426,tclLoadStaticExtensionCmd,test1.c,8126,3.48,0,7,8,93,4,0,3,3
1427,enlargeAndAppend,printf.c,1018,3.47,1,2,2,7,3,0,1,9
1428,sqlite3_soft_heap_limit64,malloc.c,95,3.47,2,4,4,23,1,0,1,8
1429,renameTokenFind,alter.c,966,3.47,19,0,5,23,3,0,3,1
1430,sqlite3ExprIsConstant,expr.c,2555,3.47,11,1,1,3,2,0,0,9
1431,shellStrtod,test1.c,1031,3.47,0,3,2,10,3,0,1,9
1432,indexColumnIsBeingUpdated,update.c,97,3.45,1,1,2,17,4,0,1,9
1433,sqlite3DbReallocOrFree,malloc.c,767,3.45,5,2,2,8,3,0,1,8
1434,jsonAfterEditSizeAdjust,json.c,2489,3.45,5,2,1,12,2,0,0,10
1435,moveToLeftmost,btree.c,5586,3.44,4,3,2,13,1,0,1,8
1436,memjrnlCreateFile,memjournal.c,145,3.44,2,2,7,28,1,0,3,4
1437,vdbeMergeEngineStep,vdbesort.c,1613,3.44,2,2,7,40,2,0,4,2
1438,sqlite3_realloc64,malloc.c,591,3.44,5,2,2,4,2,0,1,8
1439,jsonBlobExpandAndAppendOneByte,json.c,1131,3.43,1,1,2,10,2,0,1,9
1440,exprTableRegister,fkey.c,471,3.43,3,3,4,26,4,0,3,4
1441,indexWhereClauseMightChange,update.c,127,3.43,1,1,2,9,3,0,1,9
1442,pcache1AllocPage,pcache1.c,433,3.42,1,4,5,25,2,0,2,6
1443,juliandayFunc,date.c,1147,3.42,0,2,2,11,3,0,1,9
1444,intarrayOpen,test_intarray.c,107,3.42,0,2,2,11,2,0,1,9
1445,vdbeIncrMergerNew,vdbesort.c,1995,3.41,2,3,4,20,3,0,1,8
1446,sqlite3ExprReferencesUpdatedColumn,insert.c,1721,3.41,4,2,2,21,3,0,1,8
1447,pageInsertArray,btree.c,7656,3.41,3,3,9,53,7,0,3,3
1448,pagerWriteLargeSector,pager.c,6107,3.41,1,2,6,59,1,0,3,4
1449,init_wrapper_install,test_init.c,187,3.41,0,5,5,23,4,0,2,6
1450,test_zeroblob,test_func.c,610,3.40,0,2,1,8,3,0,0,11
1451,sqlite3VdbeMemSetZeroBlob,vdbemem.c,897,3.40,5,1,2,9,2,0,1,8
1452,pcache1Cachesize,pcache1.c,816,3.39,0,1,3,19,2,0,2,7
1453,sqlite3Prepare16,prepare.c,980,3.39,0,8,7,40,6,0,2,5
1454,setSharedCacheTableLock,btree.c,401,3.39,2,1,6,33,3,0,2,6
1455,sqlite3_vfs_unregister,os.c,436,3.38,4,2,2,10,1,0,1,8
1456,sqlite3ThreadCreate,threads.c,146,3.38,3,7,4,28,3,0,2,5
1457,sqlite3VdbeRecordCompare,vdbeaux.c,4932,3.38,5,1,1,6,3,0,0,10
1458,sqlite3WhereExprUsageFull,whereexpr.c,1790,3.37,1,7,7,24,2,0,2,5
1459,multiplexControlFunc,test_multiplex.c,375,3.37,0,3,6,34,3,0,2,6
1460,sqlite3_result_error,vdbeapi.c,478,3.37,46,0,1,5,3,0,0,1
1461,sqlite3_str_new,printf.c,1174,3.36,0,1,2,10,1,0,1,9
1462,sqlite3CompareAffinity,expr.c,338,3.36,8,1,4,13,2,0,2,5
1463,sqlite3_soft_heap_limit,malloc.c,120,3.35,0,1,2,4,1,0,1,9
1464,vdbePmaReaderBgIncrInit,vdbesort.c,2258,3.35,0,1,1,8,1,0,0,11
1465,vdbeSorterMergeTreeBuild,vdbesort.c,2422,3.34,1,2,3,56,2,0,1,8
1466,sqlite3VdbeMemShallowCopy,vdbemem.c,1089,3.33,7,2,3,11,3,0,1,7
1467,exprPartidxExprLookup,expr.c,4691,3.33,1,3,5,22,3,0,3,4
1468,sqlite3PagerOpenSavepoint,pager.c,6932,3.33,4,1,2,9,2,0,1,8
1469,sqlite3WindowLink,window.c,1334,3.33,2,2,5,16,2,0,3,4
1470,updateRangeAffinityStr,wherecode.c,492,3.33,2,3,3,15,3,0,2,6
1471,parseHhMmSs,date.c,203,3.33,3,3,8,36,2,0,3,3
1472,sqlite3WithDelete,build.c,5755,3.33,3,2,3,9,2,0,2,6
1473,codeExprOrVector,wherecode.c,1325,3.32,5,4,4,25,4,0,3,3
1474,ptrmapGet,btree.c,1119,3.31,9,8,5,26,4,0,1,5
1475,quotaGroupDeref,test_quota.c,229,3.31,5,4,4,9,1,0,2,5
1476,pcache1Alloc,pcache1.c,345,3.31,2,6,5,29,1,0,2,5
1477,pagerAcquireMapPage,pager.c,4040,3.31,1,3,3,35,4,0,2,6
1478,sqlite3RowSetNext,rowset.c,408,3.31,1,2,5,20,2,0,2,6
1479,srclistRenumberCursors,select.c,4050,3.30,2,1,6,24,4,0,4,2
1480,winWrite,os_win.c,2800,3.30,0,5,8,65,4,0,3,3
1481,winShmLock,os_win.c,4076,3.29,1,2,16,81,4,0,3,2
1482,findOrCreateAggInfoColumn,expr.c,7011,3.29,2,1,10,58,3,0,3,3
1483,analysisLoader,analyze.c,1602,3.29,0,6,7,41,4,0,2,5
1484,winMbcsToUtf8,os_win.c,1785,3.28,6,3,2,11,2,0,1,7
1485,sqlite3_multiplex_initialize,test_multiplex.c,1150,3.27,1,2,1,48,2,0,0,10
1486,sqlite3WhereAddLimit,whereexpr.c,1651,3.27,1,0,14,40,2,0,4,1
1487,rebuildPage,btree.c,7564,3.27,2,6,10,60,4,0,3,2
1488,quotaClose,test_quota.c,528,3.26,0,4,3,19,1,0,2,6
1489,isAuxiliaryVtabOperator,whereexpr.c,373,3.26,1,0,11,89,5,0,4,1
1490,demoWrite,test_demovfs.c,255,3.25,0,2,6,36,4,0,4,2
1491,windowRemoveExprFromSelect,resolve.c,1755,3.25,2,2,2,9,2,0,1,8
1492,sqlite3_quota_fwrite,test_quota.c,998,3.24,1,5,6,48,4,0,3,3
1493,test_db_release_memory,test1.c,6151,3.24,0,4,2,17,4,0,1,8
1494,test_db_cacheflush,test1.c,6174,3.24,0,3,3,21,4,0,1,8
1495,pcache1InitBulk,pcache1.c,301,3.23,1,4,7,33,1,0,2,5
1496,btreeReleaseAllCursorPages,btree.c,690,3.23,6,2,3,10,1,0,2,5
1497,sqlite3FindIndex,build.c,510,3.23,9,2,5,15,3,0,2,4
1498,fake_big_file,test2.c,501,3.23,0,11,5,50,4,0,1,6
1499,memjrnlTruncate,memjournal.c,260,3.22,0,2,5,25,2,0,3,4
1500,statusFunc,test_loadext.c,32,3.22,0,14,9,58,3,0,3,1
1501,sqlite3_value_double,vdbeapi.c,201,3.21,25,1,1,3,1,0,0,5
1502,sqlite3_value_free,vdbeapi.c,371,3.21,7,1,1,3,1,0,0,9
1503,hexio_read,test_hexio.c,97,3.20,0,10,6,41,4,0,1,6
1504,sqlite3CollapseDatabaseArray,build.c,583,3.20,3,3,5,21,1,0,2,5
1505,incrAggFunctionDepth,resolve.c,39,3.19,1,2,2,9,2,0,1,8
1506,aggregateConvertIndexedExprRefToColumn,select.c,6674,3.19,1,2,2,9,1,0,1,8
1507,vdbePmaReadBlob,vdbesort.c,486,3.18,3,3,10,62,3,0,3,2
1508,selectRefEnter,expr.c,6807,3.18,0,1,4,20,2,0,1,8
1509,sqlite3ErrorFinish,util.c,120,3.17,1,2,2,4,2,0,1,8
1510,sqlite3_win32_unicode_to_utf8,os_win.c,1835,3.17,1,2,2,4,1,0,1,8
1511,fsdirFilter,test_fs.c,248,3.17,0,7,3,25,5,0,1,7
1512,quota_utf8_to_mbcs,test_quota.c,377,3.17,1,9,6,22,1,0,1,6
1513,sqlite3VdbeGoto,vdbeaux.c,369,3.16,43,0,1,3,2,0,0,1
1514,setupLookaside,main.c,767,3.16,2,4,15,83,4,0,2,3
1515,changes,func.c,627,3.16,0,3,1,9,3,0,0,10
1516,last_insert_rowid,func.c,607,3.16,0,3,1,9,3,0,0,10
1517,total_changes,func.c,641,3.16,0,3,1,9,3,0,0,10
1518,sqlite3_win32_mbcs_to_utf8,os_win.c,1851,3.16,0,3,2,4,1,0,1,8
1519,sqlite3_win32_utf8_to_mbcs,os_win.c,1883,3.16,0,3,2,4,1,0,1,8
1520,fromClauseTermCanBeCoroutine,select.c,7315,3.16,1,1,16,33,4,0,2,4
1521,sqlite3TableColumnToStorage,build.c,1133,3.16,23,0,5,14,2,0,2,1
1522,vdbeMergeEngineLevel0,vdbesort.c,2309,3.16,0,2,2,25,4,0,1,8
1523,winMutexAlloc,mutex_w32.c,212,3.16,0,2,3,20,1,0,2,6
1524,sqlite3VdbeMemReleaseMalloc,vdbemem.c,607,3.15,6,1,2,4,1,0,1,7
1525,sqlite3_vfs_find,os.c,362,3.15,28,1,2,14,1,0,1,2
1526,sqlite3_result_zeroblob,vdbeapi.c,646,3.15,1,1,2,3,2,0,0,10
1527,sqlite3WalkSelectExpr,walker.c,133,3.15,1,7,9,19,2,0,2,4
1528,sqlite3ExprIsTableConstant,expr.c,2601,3.14,1,1,2,14,3,0,1,8
1529,winUnicodeToUtf8,os_win.c,1702,3.14,6,4,4,19,1,0,1,6
1530,sqlite3Utf16to8,utf.c,500,3.13,0,2,2,15,4,0,1,8
1531,sqlite3LogEst,util.c,1711,3.13,22,0,6,12,1,0,2,1
1532,sqlite3_clear_bindings,vdbeapi.c,149,3.13,0,1,3,18,1,0,1,8
1533,sqlite3ErrorClear,util.c,144,3.13,1,1,2,6,1,0,1,8
1534,sqlite3InRhsIsConstant,expr.c,3050,3.13,1,1,1,10,2,0,0,10
1535,real2hex,test_func.c,432,3.12,0,1,3,28,3,0,2,6
1536,jsonCacheSearch,json.c,425,3.12,0,5,12,40,2,0,3,2
1537,sqlite3_free_table,table.c,185,3.11,4,2,4,12,1,0,3,3
1538,sqlite3_win32_mbcs_to_utf8_v2,os_win.c,1867,3.11,0,2,2,4,2,0,1,8
1539,sqlite3_win32_utf8_to_mbcs_v2,os_win.c,1899,3.11,0,2,2,4,2,0,1,8
1540,sqlite3_win32_utf8_to_unicode,os_win.c,1819,3.11,0,2,2,4,1,0,1,8
1541,subtypeFunc,func.c,104,3.11,0,2,1,8,3,0,0,10
1542,test_getsubtype,test_func.c,623,3.11,0,2,1,7,3,0,0,10
1543,testBestIndexObj,test_bestindex.c,514,3.10,0,7,19,99,4,0,2,2
1544,sqlite3VtabModuleUnref,vtab.c,162,3.10,4,2,3,11,2,0,2,5
1545,legacyCountFinalize,test1.c,1284,3.10,0,2,1,3,1,0,0,10
1546,tableAndColumnIndex,select.c,358,3.09,3,2,4,29,7,0,3,3
1547,test_initialize,test_mutex.c,175,3.08,0,1,2,15,4,0,1,8
1548,winUtf8ToMbcs,os_win.c,1803,3.08,3,3,2,11,2,0,1,7
1549,whereLoopClearUnion,where.c,2516,3.07,2,3,4,13,2,0,2,5
1550,schemaNext,test_schema.c,172,3.07,0,10,11,49,1,0,3,1
1551,sqlite3GetTempRange,expr.c,7287,3.06,26,1,3,14,2,0,1,2
1552,nondeterministicFunction,test1.c,968,3.06,0,1,1,8,3,0,0,10
1553,clearAllSharedCacheTableLocks,btree.c,467,3.05,2,1,6,29,1,0,3,3
1554,sqlite3SelectDeleteGeneric,select.c,187,3.05,0,1,2,3,2,0,1,8
1555,test_destructor_count,test_func.c,141,3.05,0,1,1,7,3,0,0,10
1556,sqlite3StrAccumFinish,printf.c,1070,3.05,13,1,3,9,1,0,2,3
1557,sqlite3PcacheTruncate,pcache.c,696,3.05,2,1,6,25,2,0,3,3
1558,backupUpdate,backup.c,661,3.04,1,4,4,21,3,0,3,3
1559,sqlite3RenameExprlistUnmap,alter.c,929,3.04,2,3,4,15,2,0,3,3
1560,datetimeFunc,date.c,1186,3.04,1,1,5,54,3,0,2,5
1561,sqlite3VdbeEnter,vdbeaux.c,2057,3.04,3,1,4,15,1,0,2,5
1562,walIndexPage,wal.c,796,3.03,3,2,2,11,3,0,1,7
1563,sqlite3_quota_remove,test_quota.c,1229,3.03,1,7,4,43,1,0,1,6
1564,sqlite3VdbeMemSetDouble,vdbemem.c,980,3.02,3,2,2,7,2,0,1,7
1565,sqlite3MarkAllShadowTablesOf,build.c,2499,3.02,2,3,9,25,2,0,2,4
1566,filterHash,vdbe.c,688,3.01,2,1,5,16,2,0,2,5
1567,sqlite3IndexAffinityOk,expr.c,383,3.01,3,1,3,10,2,0,1,7
1568,winUtf8ToUnicode,os_win.c,1676,3.01,4,4,4,19,1,0,1,6
1569,pcache1Destroy,pcache1.c,1173,3.00,0,5,2,17,1,0,1,7
1570,renameColumnElistNames,alter.c,1086,3.00,2,2,4,19,4,0,3,3
1571,quotaWrite,test_quota.c,565,2.99,0,3,5,32,4,0,3,3
1572,renameColumnIdlistNames,alter.c,1111,2.99,2,2,4,16,4,0,3,3
1573,jsonLabelCompare,json.c,2720,2.98,2,2,3,16,6,0,2,5
1574,test_sqlite3_db_config,test1.c,8378,2.98,0,10,7,56,4,0,1,5
1575,sqlite3WalkSelectFrom,walker.c,166,2.98,1,2,5,21,2,0,3,3
1576,applyNumericAffinity,vdbe.c,354,2.98,5,3,4,16,2,0,2,4
1577,sqlite3PrimaryKeyIndex,build.c,1053,2.98,31,0,2,5,1,0,1,1
1578,functionDestroy,main.c,1171,2.97,2,2,3,12,2,0,2,5
1579,sqlite3ApiExit,malloc.c,909,2.97,21,1,2,8,2,0,1,3
1580,sqlite3_vfslog_new,test_osinst.c,710,2.97,1,6,4,42,3,0,1,6
1581,whereInterstageHeuristic,where.c,6121,2.97,1,0,8,21,1,0,4,1
1582,testpcacheUnpin,test_pcache.c,290,2.96,0,8,5,31,3,0,3,2
1583,sqlite3WindowCompare,window.c,1356,2.96,2,5,12,27,4,0,2,3
1584,allocateTempSpace,btree.c,2862,2.96,1,3,2,15,1,0,1,7
1585,resolveAsName,resolve.c,1478,2.95,2,1,4,21,3,0,3,3
1586,sqlite3_value_int64,vdbeapi.c,207,2.95,21,1,1,3,1,0,0,5
1587,jsonParseReset,json.c,840,2.95,10,2,3,15,1,0,1,5
1588,getDigits,date.c,112,2.95,9,2,5,34,3,0,3,1
1589,sqlite3_value_numeric_type,vdbe.c,437,2.95,10,3,2,9,1,0,1,5
1590,sqlite3ExprIsConstantNotJoin,expr.c,2572,2.95,3,1,1,3,2,0,0,9
1591,sqlite3BtreeBeginStmt,btree.c,4541,2.94,1,3,1,13,2,0,0,9
1592,btreeCursorWithLock,btree.c,4710,2.94,1,3,1,13,5,0,0,9
1593,sqlite3IdListDelete,build.c,4691,2.94,10,2,3,9,2,0,1,5
1594,pcacheSortDirtyList,pcache.c,783,2.94,1,4,8,28,1,0,3,2
1595,sqlite3AuthRead,auth.c,136,2.93,2,1,9,45,4,0,3,2
1596,sqlite3TriggersExist,trigger.c,833,2.92,1,2,3,16,5,0,2,5
1597,vdbeSorterJoinAll,vdbesort.c,1148,2.92,2,1,3,10,2,0,2,5
1598,sqlite3RowidAlias,expr.c,2964,2.91,1,1,5,15,1,0,3,3
1599,vdbeSortSubtaskCleanup,vdbesort.c,1048,2.91,1,6,4,17,2,0,1,6
1600,multiplexRead,test_multiplex.c,714,2.90,0,0,7,39,4,0,4,1
1601,whereLoopAddBtree,where.c,3882,2.89,1,2,5,220,2,0,2,3
1602,sqlite3ResultStrAccum,printf.c,1084,2.89,4,3,3,11,2,0,1,6
1603,tclDequote,test_bestindex.c,132,2.89,1,0,6,22,1,0,4,1
1604,jsonCacheDelete,json.c,363,2.89,1,2,2,7,1,0,1,7
1605,sqlite3BitvecDestroy,bitvec.c,277,2.88,9,2,4,10,1,0,2,3
1606,vdbePmaReaderClear,vdbesort.c,469,2.88,3,5,2,7,1,0,1,6
1607,dequote,test_osinst.c,839,2.88,1,0,6,19,1,0,4,1
1608,sqlite3_multiplex_shutdown,test_multiplex.c,1211,2.88,1,2,1,8,1,0,0,9
1609,sqlite3WindowDelete,window.c,1133,2.88,4,9,2,13,2,0,1,5
1610,columnIsGoodIndexCandidate,where.c,869,2.88,1,0,6,17,2,0,4,1
1611,sqlite3BitvecCreate,bitvec.c,115,2.87,6,1,2,9,1,0,1,6
1612,sqlite3BlobCompare,vdbeaux.c,4486,2.87,1,3,8,21,2,0,3,2
1613,sqlite3PagerUnref,pager.c,5763,2.86,15,1,2,3,1,0,1,4
1614,btreeEnterAll,btmutex.c,186,2.86,1,1,3,14,1,0,2,5
1615,sqlite3Utf8Read,utf.c,146,2.86,19,0,4,16,1,0,2,1
1616,winMbcsToUnicode,os_win.c,1729,2.86,1,4,5,21,2,0,1,6
1617,sqlite3ExprSkipCollateAndLikely,expr.c,216,2.86,19,0,4,15,1,0,2,1
1618,sqlite3GetInt32,util.c,935,2.86,6,2,13,46,2,0,2,2
1619,winUnicodeToMbcs,os_win.c,1758,2.86,1,4,5,20,2,0,1,6
1620,renameReloadSchema,alter.c,111,2.85,4,3,3,8,3,0,2,4
1621,sqlite3WhereExprListUsage,whereexpr.c,1828,2.85,10,1,3,10,2,0,2,3
1622,multiplexWrite,test_multiplex.c,759,2.85,0,0,6,35,4,0,4,1
1623,sqlite3BtreeCheckpoint,btree.c,11245,2.85,0,2,3,14,4,0,2,5
1624,sqlite3WalExclusiveMode,wal.c,4422,2.84,4,1,5,26,2,0,3,2
1625,pcache1EnforceMaxPage,pcache1.c,623,2.84,3,3,3,17,1,0,1,6
1626,sqlite3WhereExprUsageNN,whereexpr.c,1816,2.84,4,2,3,9,2,0,1,6
1627,sqlite3MatchEName,resolve.c,125,2.83,2,4,10,32,5,0,2,3
1628,sqlite3Dequote,util.c,298,2.83,8,0,7,22,1,0,3,1
1629,md5file_cmd,test_md5.c,340,2.83,0,9,6,47,4,0,2,3
1630,sqlite3BtreeClearCache,btree.c,11467,2.83,1,1,2,6,1,0,1,7
1631,pcacheManageDirtyList,pcache.c,195,2.83,5,0,10,51,2,0,3,1
1632,btree_set_cache_size,test3.c,583,2.83,0,7,2,22,4,0,1,6
1633,tkt2213Function,test1.c,866,2.82,0,7,2,21,3,0,1,6
1634,pushDownWindowCheck,select.c,5077,2.82,1,1,1,6,3,0,0,9
1635,sqlite3SrcListAssignCursors,build.c,4882,2.82,4,1,5,17,2,0,3,2
1636,sqlite3ExprIsConstantOrFunction,expr.c,2776,2.82,1,1,1,4,2,0,0,9
1637,sqlite3MemSetArrayInt64,vdbemem.c,949,2.81,1,1,1,3,3,0,0,9
1638,sqlite3KeyInfoUnref,select.c,1536,2.81,5,1,3,8,1,0,2,4
1639,setDestPgsz,backup.c,112,2.81,0,2,1,5,1,0,0,9
1640,sqlite3DbMallocSize,malloc.c,376,2.80,9,0,5,16,2,0,3,1
1641,winRead,os_win.c,2720,2.80,0,8,6,55,4,0,2,3
1642,disableUnusedSubqueryResultColumns,select.c,5355,2.79,1,0,15,56,1,0,3,1
1643,walIteratorNext,wal.c,1751,2.79,0,0,5,27,3,0,4,1
1644,getSafetyLevel,pragma.c,72,2.79,2,3,4,19,3,0,2,4
1645,columnName,vdbeapi.c,1467,2.79,6,3,10,48,4,0,2,2
1646,removeElementGivenHash,hash.c,183,2.78,1,2,6,30,3,0,2,4
1647,pcache1Shrink,pcache1.c,841,2.78,0,1,2,13,1,0,1,7
1648,rehash,hash.c,106,2.78,1,8,5,22,2,0,1,5
1649,sqlite3_str_reset,printf.c,1139,2.78,9,1,2,9,1,0,1,5
1650,vdbeMemClear,vdbemem.c,576,2.77,3,2,3,10,1,0,1,6
1651,walkWindowList,walker.c,25,2.77,2,5,8,18,3,0,2,3
1652,sqlite3_txn_state,main.c,1295,2.77,1,3,5,19,2,0,2,4
1653,sqlite3PcacheFetchStress,pcache.c,445,2.77,0,2,8,29,3,0,3,2
1654,vdbeMergeEngineNew,vdbesort.c,1178,2.76,2,2,4,16,1,0,1,6
1655,reset_prng_state,test1.c,7255,2.76,0,1,1,9,4,0,0,9
1656,walRewriteChecksums,wal.c,3945,2.76,1,4,2,34,2,0,1,6
1657,comparisonAffinity,expr.c,360,2.75,1,3,4,16,1,0,1,6
1658,sqlite3BtreeGetMeta,btree.c,10352,2.75,6,4,2,14,3,0,1,5
1659,incrblobClose2,tclsqlite.c,262,2.75,0,2,6,29,3,0,1,6
1660,sqlite3FixSrcList,attach.c,555,2.74,3,2,2,13,2,0,1,6
1661,sqlite3PagerCommitPhaseTwo,pager.c,6669,2.73,2,2,3,21,1,0,1,6
1662,register_echo_module,test8.c,1368,2.73,0,4,3,31,4,0,1,6
1663,sqlite3VectorFieldSubexpr,expr.c,534,2.73,8,1,3,14,2,0,2,3
1664,sqlite3RenameExprUnmap,alter.c,913,2.72,3,2,1,11,2,0,0,8
1665,test_db_readonly,test1.c,6249,2.71,0,5,2,17,4,0,1,6
1666,sqlite3PagerOpenWal,pager.c,7593,2.71,2,2,3,23,2,0,2,4
1667,sqlite3VdbeIntValue,vdbemem.c,628,2.70,10,2,4,17,1,0,1,4
1668,removeFromSharingList,btree.c,2823,2.69,1,1,6,28,1,0,3,2
1669,sqlite3_result_null,vdbeapi.c,510,2.69,8,1,1,4,1,0,0,7
1670,cf2Destroy,test1.c,1932,2.69,0,7,7,12,1,0,2,3
1671,local_getline,tclsqlite.c,1246,2.68,0,5,9,32,2,0,3,1
1672,querySharedCacheTableLock,btree.c,329,2.68,6,0,6,30,3,0,3,1
1673,shellDtostr,test1.c,1049,2.68,0,3,4,13,3,0,1,6
1674,multiplexSubClose,test_multiplex.c,431,2.68,1,3,3,16,3,0,2,4
1675,isSelfJoinView,select.c,7119,2.68,2,1,10,32,4,0,2,3
1676,sqlite3ExprAnalyzeAggregates,expr.c,7233,2.67,3,1,1,11,2,0,0,8
1677,exprIsConst,expr.c,2528,2.67,3,1,1,9,3,0,0,8
1678,whereLoopClear,where.c,2534,2.67,2,2,2,10,2,0,1,6
1679,sqlite3ReleaseTempRange,expr.c,7301,2.66,20,1,3,11,3,0,1,2
1680,test_db_filename,test1.c,6225,2.66,0,4,2,17,4,0,1,6
1681,sqlite3VdbeMemMove,vdbemem.c,1128,2.65,2,2,1,9,2,0,0,8
1682,parseDateOrTime,date.c,416,2.65,0,6,6,23,3,0,1,5
1683,winRetryIoerr,os_win.c,2155,2.65,9,2,5,18,2,0,2,2
1684,sqlite3ShadowTableName,build.c,2535,2.64,4,3,4,12,2,0,1,5
1685,test_error,test_func.c,222,2.64,0,4,2,10,3,0,1,6
1686,findCompatibleInRhsSubrtn,expr.c,3440,2.64,1,3,8,35,3,0,2,3
1687,searchWith,select.c,5660,2.63,1,1,5,21,3,0,3,2
1688,tclvarFilter,test_tclvar.c,184,2.63,0,8,9,56,5,0,2,2
1689,whereLoopCheaperProperSubset,where.c,2636,2.63,2,0,11,31,2,0,3,1
1690,testpcacheCreate,test_pcache.c,133,2.63,0,2,3,32,3,0,1,6
1691,winMakeEndInDirSep,os_win.c,4721,2.62,1,1,5,15,2,0,3,2
1692,timeFunc,date.c,1246,2.62,1,1,3,36,3,0,2,4
1693,winIsDir,os_win.c,4992,2.62,3,4,4,20,1,0,2,3
1694,sqlite3VdbeBooleanValue,vdbemem.c,679,2.61,6,1,3,6,2,0,1,5
1695,sqlite3MultiValuesEnd,insert.c,585,2.61,1,2,3,10,2,0,2,4
1696,sqlite3SelectExpand,select.c,6428,2.61,1,2,2,14,2,0,1,6
1697,recomputeColumnsUsed,select.c,4021,2.61,1,2,2,13,2,0,1,6
1698,sqlite3ExprListCompare,expr.c,6347,2.61,8,1,7,14,3,0,2,2
1699,sqlite3RenameTokenRemap,alter.c,801,2.61,16,0,3,10,3,0,2,1
1700,sqlite3ExprIsConstantOrGroupBy,expr.c,2744,2.60,2,1,1,10,3,0,0,8
1701,columnMallocFailure,vdbeapi.c,1343,2.60,10,2,2,10,1,0,1,4
1702,sqlite3ExprCoveredByIndex,expr.c,6772,2.60,1,2,1,15,3,0,0,8
1703,pageReinit,btree.c,2472,2.60,0,3,3,12,1,0,2,4
1704,MD5Update,test_md5.c,188,2.60,3,8,5,30,3,0,2,2
1705,sqlite3_quota_ftruncate,test_quota.c,1127,2.60,1,6,4,23,2,0,2,3
1706,sqlite3PagerClearCache,pager.c,7462,2.60,2,1,2,4,1,0,1,6
1707,test_drop_modules,test1.c,1206,2.59,0,2,3,16,4,0,1,6
1708,btree_ismemdb,test3.c,552,2.59,0,8,2,24,4,0,1,5
1709,exprIsDeterministic,where.c,6275,2.59,1,2,1,9,1,0,0,8
1710,sqlite3ExprCanReturnSubtype,expr.c,4601,2.59,1,2,1,8,2,0,0,8
1711,havingToWhere,select.c,7096,2.59,1,2,1,8,2,0,0,8
1712,memdbWrite,memdb.c,294,2.58,0,6,5,27,4,0,2,3
1713,btreeGetPage,btree.c,2322,2.58,14,2,2,15,4,0,1,3
1714,subjRequiresPage,pager.c,1061,2.58,1,1,4,16,1,0,3,2
1715,memdbEnlarge,memdb.c,274,2.58,0,1,4,16,2,0,1,6
1716,whereSortingCost,where.c,5378,2.58,1,3,7,28,4,0,2,3
1717,sqlite3BeginBenignMalloc,fault.c,74,2.58,24,1,2,6,0,0,1,1
1718,sqlite3EndBenignMalloc,fault.c,80,2.58,24,1,2,6,0,0,1,1
1719,sqlite3BitvecTestNotNull,bitvec.c,130,2.57,4,0,7,23,2,0,3,1
1720,sqlite3BtreeSetAutoVacuum,btree.c,3187,2.57,3,2,5,14,2,0,1,5
1721,sqlite3StrAccumSetError,printf.c,111,2.57,0,2,3,6,2,0,1,6
1722,toLocaltime,date.c,604,2.57,0,6,3,41,2,0,1,5
1723,vdbeLeave,vdbeaux.c,2078,2.57,1,1,3,14,1,0,2,4
1724,sqlite3VdbeSerialGet,vdbeaux.c,4104,2.56,10,1,13,69,3,0,1,2
1725,sqlite3HashFind,hash.c,218,2.56,24,1,1,5,2,0,0,3
1726,walIndexTryHdr,wal.c,2562,2.56,2,8,5,28,2,0,1,4
1727,sqlite3SafetyCheckOk,util.c,1543,2.56,3,3,4,17,1,0,2,3
1728,btreeLeaveAll,btmutex.c,203,2.55,1,1,3,9,1,0,2,4
1729,sqlite3BtreeSecureDelete,btree.c,3166,2.55,4,2,3,14,2,0,1,5
1730,vdbeMemClearExternAndSetNull,vdbemem.c,553,2.55,4,2,3,14,1,0,1,5
1731,fsdirNext,test_fs.c,230,2.55,0,2,3,12,1,0,2,4
1732,sqlite3WindowListDelete,window.c,1150,2.54,1,1,2,7,2,0,1,6
1733,isValidSchemaTableName,resolve.c,228,2.54,2,6,8,22,3,0,2,2
1734,pager_truncate,pager.c,2638,2.54,3,1,6,31,2,0,3,1
1735,getDoubleArg,printf.c,125,2.53,1,1,2,4,1,0,1,6
1736,sqlite3_result_pointer,vdbeapi.c,517,2.53,0,2,1,13,4,0,0,8
1737,sqlite3MulInt64,util.c,1615,2.53,1,0,11,17,2,0,3,1
1738,sqlite3FixExpr,attach.c,575,2.53,1,1,1,6,2,0,0,8
1739,btreeLockCarefully,btmutex.c,105,2.52,1,3,6,22,1,0,2,3
1740,jsonStringReset,json.c,494,2.52,9,2,2,4,1,0,1,4
1741,dbEvalRowInfo,tclsqlite.c,1673,2.52,2,0,8,38,3,0,3,1
1742,sqlite3CloseSavepoints,main.c,1154,2.52,5,1,2,10,1,0,1,5
1743,pcache1Unpin,pcache1.c,1080,2.51,0,1,2,23,3,0,1,6
1744,sqlite3ProgressCheck,util.c,211,2.51,3,1,6,18,1,0,3,1
1745,sqlite3VdbeLeave,vdbeaux.c,2092,2.50,5,1,2,4,1,0,1,5
1746,sqlite3WalBeginWriteTransaction,wal.c,3659,2.50,2,7,5,24,1,0,1,4
1747,findSqlFunc,tclsqlite.c,533,2.49,1,5,3,19,2,0,2,3
1748,pcache1RemoveFromHash,pcache1.c,605,2.49,4,1,3,11,2,0,1,5
1749,walIndexClose,wal.c,1606,2.49,3,2,4,12,2,0,2,3
1750,selectWindowRewriteSelectCb,window.c,831,2.49,0,1,2,12,2,0,1,6
1751,whereOrInsert,where.c,203,2.48,1,0,9,32,4,0,3,1
1752,sameSrcAlias,select.c,7269,2.48,2,2,5,17,2,0,2,3
1753,fsWrite,test_onefile.c,439,2.48,0,0,10,41,4,0,3,1
1754,rowSetEntryMerge,rowset.c,241,2.48,3,0,6,29,2,0,3,1
1755,memdbRead,memdb.c,252,2.48,0,6,3,18,4,0,2,3
1756,pcache1Truncate,pcache1.c,1158,2.48,0,1,2,9,2,0,1,6
1757,sqlite3CreateColumnExpr,resolve.c,872,2.47,3,0,6,27,4,0,3,1
1758,sorter_test_sort4_helper,test1.c,8274,2.47,0,6,7,54,4,0,1,4
1759,sqlite3VdbeMemSetPointer,vdbemem.c,960,2.47,3,1,3,14,4,0,0,7
1760,dequoteString,test8.c,118,2.47,1,0,9,26,1,0,3,1
1761,getIntArg,printf.c,121,2.47,0,1,2,4,1,0,1,6
1762,sqlite3GetVarint32,util.c,1398,2.46,16,1,4,21,2,0,1,2
1763,test_sqlite3_txn_state,test1.c,8442,2.46,0,5,3,19,4,0,1,5
1764,sqlite3_shutdown,main.c,367,2.46,3,5,5,22,0,0,1,4
1765,jsonCacheDeleteGeneric,json.c,370,2.46,0,1,1,3,1,0,0,8
1766,sqlite3ErrStr,main.c,1623,2.45,12,0,3,56,1,0,2,1
1767,sqlite3_column_value,vdbeapi.c,1403,2.45,4,1,2,9,2,0,1,5
1768,sqlite3VdbeMultiLoad,vdbeaux.c,391,2.45,11,2,4,19,4,0,2,1
1769,sqlite3_context_db_handle,vdbeapi.c,967,2.45,32,0,1,4,1,0,0,1
1770,renameTokenFree,alter.c,948,2.44,4,1,2,8,2,0,1,5
1771,vdbeSorterMerge,vdbesort.c,1347,2.44,2,1,5,35,3,0,3,1
1772,winMapfile,os_win.c,4420,2.44,0,5,8,58,2,0,2,2
1773,invalidateIncrblobCursors,btree.c,591,2.44,4,0,4,19,4,0,3,1
1774,testpcacheTruncate,test_pcache.c,383,2.44,0,5,4,18,2,0,3,1
1775,sqlite3_mprintf_int64,test1.c,1398,2.43,0,4,4,25,4,0,2,3
1776,pcacheMergeDirtyList,pcache.c,746,2.43,3,0,5,25,2,0,3,1
1777,adjustOrderByCol,wherecode.c,523,2.43,2,0,7,17,2,0,3,1
1778,sqlite3_db_cacheflush,main.c,908,2.43,1,4,1,21,1,0,0,7
1779,pragmaLocate,pragma.c,311,2.43,2,1,5,16,1,0,2,3
1780,sqlite3IdListIndex,build.c,4705,2.42,8,1,3,8,2,0,2,2
1781,parseYyyyMmDd,date.c,331,2.42,1,4,8,30,2,0,1,4
1782,whereLoopDelete,where.c,2585,2.42,3,2,1,5,2,0,0,7
1783,sqlite3IntFloatCompare,vdbeaux.c,4529,2.41,5,1,7,16,2,0,2,2
1784,sqlite3IsShadowTableOf,build.c,2476,2.41,3,3,7,13,3,0,1,4
1785,sqlite3Utf8CharLen,utf.c,446,2.41,3,0,5,16,2,0,3,1
1786,next2,test_tclvar.c,135,2.40,0,4,4,26,3,0,3,1
1787,sqlite3DecOrHexToI64,util.c,901,2.40,3,3,7,21,2,0,2,2
1788,sqlite3ColumnExpr,build.c,693,2.39,20,0,5,7,2,0,1,1
1789,sqlite3_db_config,main.c,937,2.39,2,5,2,75,3,0,1,4
1790,jsonBytesToBypass,json.c,2547,2.39,1,0,7,28,2,0,3,1
1791,vdbePmaWriterInit,vdbesort.c,1454,2.39,2,2,2,17,4,0,1,5
1792,whereLoopAdjustCost,where.c,2682,2.39,1,2,11,22,2,0,2,2
1793,test_wal_autocheckpoint,test1.c,7526,2.39,0,8,4,27,4,0,1,4
1794,vdbeSorterRecordFree,vdbesort.c,1035,2.38,3,1,2,8,2,0,1,5
1795,freeTempSpace,btree.c,2899,2.38,3,1,2,7,1,0,1,5
1796,sqlite3DeleteUnlinkIfExists,test_delete.c,60,2.38,0,3,5,19,3,0,3,1
1797,sqlite3_blob_close,vdbeblob.c,361,2.37,1,3,2,16,1,0,1,5
1798,sqlite3CloseExtensions,loadext.c,722,2.37,2,2,2,8,1,0,1,5
1799,checkColumnOverlap,trigger.c,746,2.37,2,1,4,8,2,0,2,3
1800,multiplexFreeComponents,test_multiplex.c,451,2.36,2,2,2,7,1,0,1,5
1801,sqlite3VdbeMemNumerify,vdbemem.c,781,2.36,2,1,3,25,1,0,2,3
1802,freeCursorWithCache,vdbeaux.c,2741,2.36,1,3,2,12,2,0,1,5
1803,sqlite3VdbeMemAggValue,vdbemem.c,526,2.36,1,3,1,16,3,0,0,7
1804,sqlite3ValueSetNull,vdbemem.c,888,2.36,3,1,1,3,1,0,0,7
1805,inAnyUsingClause,select.c,5976,2.36,1,1,5,14,3,0,2,3
1806,walChecksumBytes,wal.c,847,2.36,8,0,7,55,5,0,2,1
1807,dateFunc,date.c,1288,2.35,1,1,4,31,3,0,2,3
1808,sqlite3VdbeFindCompare,vdbeaux.c,5114,2.35,1,0,6,31,1,0,3,1
1809,test_bind_value_from_select,test1.c,4310,2.35,0,6,5,38,4,0,1,4
1810,winTruncate,os_win.c,2909,2.34,0,4,8,41,2,0,2,2
1811,zeroJournalHdr,pager.c,1383,2.34,1,0,6,26,2,0,3,1
1812,sqlite3WhereGetMask,where.c,240,2.33,11,0,4,15,2,0,2,1
1813,sqlite3RowSetClear,rowset.c,152,2.33,2,1,2,14,1,0,1,5
1814,decodeFlags,btree.c,2022,2.32,2,2,6,57,2,0,2,2
1815,jsonEachCursorReset,json.c,4884,2.32,5,3,1,12,1,0,0,6
1816,transferParseError,trigger.c,1219,2.32,2,1,2,11,2,0,1,5
1817,jsonFuncArgMightBeBinary,json.c,2450,2.32,5,4,7,19,1,0,1,3
1818,sqlite3VdbeRealValue,vdbemem.c,658,2.32,5,1,4,15,1,0,1,4
1819,memdbUnlock,memdb.c,426,2.32,0,2,4,20,2,0,2,3
1820,sqlite3_init_sqllog,test_sqllog.c,546,2.31,0,3,4,11,0,0,3,1
1821,whereCheckIfBloomFilterIsUseful,where.c,6438,2.31,1,0,5,32,1,0,3,1
1822,page_unref,test2.c,395,2.31,0,3,2,16,4,0,1,5
1823,exprRefToSrcList,expr.c,6844,2.31,0,0,7,21,2,0,3,1
1824,winShmUnmap,os_win.c,4035,2.30,1,6,3,27,2,0,1,4
1825,winUnmapfile,os_win.c,4371,2.30,6,4,5,32,1,0,2,1
1826,recomputeColumnsNotIndexed,build.c,2276,2.30,2,0,4,15,1,0,3,1
1827,test_config,test_mutex.c,337,2.29,0,1,4,33,4,0,2,3
1828,rowSetEntrySort,rowset.c,276,2.29,2,3,6,21,1,0,2,2
1829,freeP4Mem,vdbeaux.c,1363,2.29,1,2,2,4,2,0,1,5
1830,winFetch,os_win.c,4518,2.28,0,1,5,25,4,0,3,1
1831,sqllogTraceDb,test_sqllog.c,426,2.28,0,2,4,20,1,0,3,1
1832,sqlite3StorageColumnToTable,build.c,1085,2.28,2,0,4,9,2,0,3,1
1833,tclFindFunction,test_bestindex.c,753,2.28,0,0,5,46,5,0,3,1
1834,sqlite3InsertBuiltinFuncs,callback.c,358,2.28,0,2,3,23,2,0,2,3
1835,sqlite3PagerExclusiveLock,pager.c,6390,2.27,1,1,3,15,1,0,2,3
1836,sqlite3VdbeGetOp,vdbeaux.c,1690,2.27,20,0,2,10,2,0,1,1
1837,sqlite3ExprSimplifiedAndOr,expr.c,2362,2.27,4,2,4,13,1,0,2,2
1838,sqlite3VdbeMemFinalize,vdbemem.c,493,2.27,2,5,2,23,2,0,1,4
1839,winMutexEnd,mutex_w32.c,149,2.26,0,2,4,12,0,0,3,1
1840,fsClose,test_onefile.c,375,2.26,0,2,3,16,1,0,2,3
1841,exprSetHeight,expr.c,846,2.26,2,3,5,13,1,0,1,4
1842,tableMayNotBeDropped,build.c,3432,2.26,1,4,6,14,2,0,2,2
1843,sqlite3GetVarint,util.c,1237,2.26,12,0,9,104,2,0,1,1
1844,sqlite3SrcListShiftJoinType,build.c,5169,2.26,0,0,6,19,2,0,3,1
1845,test_pager_refcounts,test1.c,6344,2.26,0,6,4,31,4,0,2,2
1846,btreeCellSizeCheck,btree.c,2167,2.26,1,4,5,30,1,0,2,2
1847,multiplexTruncate,test_multiplex.c,799,2.26,0,0,5,36,2,0,3,1
1848,sqlite3HashClear,hash.c,35,2.25,9,2,2,15,1,0,1,3
1849,translateColumnToCopy,where.c,711,2.25,2,2,6,25,5,0,2,2
1850,getAutoVacuum,pragma.c,125,2.24,1,4,5,8,1,0,1,4
1851,test_enable_shared,test1.c,1641,2.24,0,5,5,27,4,0,2,2
1852,sqlite3_compileoption_used,main.c,5038,2.24,1,5,4,16,1,0,2,2
1853,out2PrereleaseWithClear,vdbe.c,665,2.23,1,1,1,5,1,0,0,7
1854,vdbeIncrBgPopulate,vdbesort.c,1932,2.23,1,1,1,5,1,0,0,7
1855,vdbeReleaseAndSetInt64,vdbemem.c,927,2.23,1,1,1,5,2,0,0,7
1856,whereLoopIsOneRow,wherecode.c,1450,2.23,1,0,4,15,1,0,3,1
1857,sqlite3ColumnColl,build.c,729,2.23,9,0,5,10,1,0,2,1
1858,vdbeSorterJoinThread,vdbesort.c,1114,2.23,5,1,2,14,1,0,1,4
1859,sqlite3ExprVectorSize,expr.c,505,2.23,18,0,4,13,1,0,1,1
1860,rowSetNDeepTree,rowset.c,340,2.23,3,2,4,25,2,0,2,2
1861,test_errstr,test1.c,3645,2.23,0,6,4,18,4,0,2,2
1862,vdbeSorterOpenTempFile,vdbesort.c,1306,2.22,0,1,3,21,3,0,2,3
1863,sqlite3PcacheRelease,pcache.c,551,2.22,4,2,3,12,1,0,2,2
1864,dropCell,btree.c,7187,2.22,4,4,5,42,4,0,1,3
1865,jsonEachClose,json.c,4898,2.22,0,2,1,6,1,0,0,7
1866,findElementWithHash,hash.c,147,2.22,2,2,5,31,3,0,2,2
1867,sehInjectFault,wal.c,678,2.22,13,2,2,12,1,0,1,2
1868,sqlite3AddNotNull,build.c,1576,2.21,0,0,5,18,2,0,3,1
1869,optimization_control,test1.c,8059,2.21,0,7,7,60,4,0,2,1
1870,sqlite3TableColumnToIndex,build.c,1065,2.20,10,0,3,7,2,0,2,1
1871,init_wrapper_uninstall,test_init.c,211,2.20,0,1,2,16,4,0,1,5
1872,test_bind_text16,test1.c,4147,2.20,0,9,5,46,4,0,2,1
1873,test_shutdown,test_mutex.c,154,2.20,0,1,2,15,4,0,1,5
1874,gatherSelectWindowsCallback,expr.c,1767,2.19,0,1,2,11,2,0,1,5
1875,sqlite3TriggerList,trigger.c,50,2.19,3,1,4,30,2,0,2,2
1876,disableTerm,wherecode.c,417,2.19,8,0,5,20,2,0,2,1
1877,isDupColumn,build.c,2236,2.19,4,1,3,20,4,0,2,2
1878,test_bind_text,test1.c,4094,2.19,0,9,5,41,4,0,2,1
1879,zeroPage,btree.c,2261,2.19,6,3,3,30,2,0,1,3
1880,demoRead,test_demovfs.c,212,2.18,0,3,6,29,4,0,2,2
1881,releaseAllSavepoints,pager.c,1785,2.18,2,3,3,13,1,0,1,4
1882,walMergesort,wal.c,1861,2.18,0,3,5,46,4,0,2,2
1883,sqlite3PcacheMakeDirty,pcache.c,593,2.18,4,1,3,15,1,0,2,2
1884,vdbeIncrPopulateThread,vdbesort.c,1922,2.17,0,1,1,6,1,0,0,7
1885,btreeInitPage,btree.c,2208,2.17,4,4,4,37,1,0,1,3
1886,pagerPagecount,pager.c,3262,2.17,3,1,4,21,2,0,2,2
1887,faultInstallCmd,test2.c,627,2.17,0,8,6,36,4,0,2,1
1888,sqlite3AutoincrementBegin,insert.c,457,2.17,2,1,4,48,1,0,2,2
1889,sqlite3_uri_boolean,main.c,4779,2.16,3,2,2,5,3,0,0,6
1890,multiplexSync,test_multiplex.c,839,2.16,0,0,4,14,2,0,3,1
1891,walIteratorInit,wal.c,1941,2.16,1,2,3,56,3,0,1,4
1892,sqlite3WithDeleteGeneric,build.c,5764,2.16,0,1,1,3,2,0,0,7
1893,checkConstraintExprNode,insert.c,1692,2.16,0,0,4,13,2,0,3,1
1894,heightOfExprList,expr.c,816,2.16,4,1,3,8,2,0,2,2
1895,faultSimCallback,test2.c,587,2.15,0,8,6,31,1,0,2,1
1896,vdbeSorterExtendFile,vdbesort.c,1287,2.15,3,2,3,10,3,0,2,2
1897,sqlite3_errcode,main.c,2720,2.15,7,2,3,9,1,0,1,3
1898,sqlite3ColumnIndex,select.c,320,2.15,3,2,3,9,2,0,2,2
1899,sqlite3BtreeSetCacheSize,btree.c,2976,2.15,7,3,1,8,2,0,0,5
1900,sqlite3PageMalloc,pcache1.c,493,2.13,4,1,1,4,1,0,0,6
1901,name_to_enc,test5.c,90,2.13,0,3,5,26,2,0,2,2
1902,createModule,vtab.c,87,2.13,1,4,2,15,5,0,1,4
1903,uriParameter,main.c,3204,2.13,1,4,3,10,2,0,2,2
1904,test_errcode,test1.c,4737,2.12,0,5,2,18,4,0,1,4
1905,test_errmsg,test1.c,4763,2.12,0,5,2,18,4,0,1,4
1906,test_error_offset,test1.c,4791,2.12,0,5,2,18,4,0,1,4
1907,test_ex_errcode,test1.c,4710,2.12,0,5,2,18,4,0,1,4
1908,sqlite3PreferredTableName,build.c,486,2.12,1,3,4,11,1,0,2,2
1909,sqlite3WhereMinMaxOptEarlyOut,where.c,119,2.11,1,2,5,14,2,0,2,2
1910,sqlite3PagerUnrefNotNull,pager.c,5751,2.11,7,2,2,11,1,0,1,3
1911,testpcacheRekey,test_pcache.c,335,2.11,0,8,5,30,4,0,2,1
1912,backupTestCmd,test_backup.c,26,2.11,0,4,8,65,4,0,2,1
1913,sqlite3LogEstAdd,util.c,1684,2.10,6,0,6,22,2,0,2,1
1914,sqlite3_strglob,func.c,862,2.10,7,1,3,9,2,0,1,3
1915,pcache1FreePage,pcache1.c,474,2.10,3,1,2,13,1,0,1,4
1916,winShmSystemLock,os_win.c,3798,2.09,0,3,4,28,4,0,2,2
1917,sqlite3VdbeAddParseSchemaOp,vdbeaux.c,564,2.09,6,3,2,7,4,0,1,3
1918,pager_reset,pager.c,1767,2.09,7,2,1,5,1,0,0,5
1919,sqlite3VdbeUsesBtree,vdbeaux.c,2026,2.09,12,1,2,8,2,0,1,2
1920,freeEphemeralFunction,vdbeaux.c,1353,2.08,3,1,2,6,2,0,1,4
1921,autoIncrementEnd,insert.c,531,2.08,1,2,3,35,1,0,2,2
1922,connectionIsBusy,main.c,1219,2.08,2,1,4,10,1,0,2,2
1923,vdbeSorterCreateThread,vdbesort.c,1135,2.08,3,1,1,8,3,0,0,6
1924,sqlite3_value_type,vdbeapi.c,245,2.07,24,0,1,69,1,0,0,1
1925,pcache1Free,pcache1.c,383,2.07,2,6,3,26,1,0,1,3
1926,sqlite3BackupUpdate,backup.c,686,2.07,3,1,2,3,3,0,1,4
1927,test_busy_timeout,test1.c,5929,2.07,0,4,2,19,4,0,1,4
1928,sqlite3_get_clientdata,main.c,3751,2.07,0,4,3,13,2,0,2,2
1929,renumberCursors,select.c,4119,2.06,1,3,1,14,4,0,0,6
1930,quotaLeave,test_quota.c,191,2.06,21,1,1,1,0,0,0,2
1931,test_bind_double,test1.c,3982,2.06,0,5,6,59,4,0,2,1
1932,walHandleException,wal.c,2411,2.06,4,1,6,23,1,0,1,3
1933,isCandidateForInOpt,expr.c,2988,2.06,1,0,12,35,1,0,2,1
1934,winMutexInit,mutex_w32.c,127,2.06,0,3,4,14,0,0,2,2
1935,sqlite3MutexInit,mutex.c,219,2.06,1,2,3,26,0,0,2,2
1936,getLockingMode,pragma.c,110,2.06,1,2,4,7,1,0,2,2
1937,sqlite3CteDelete,build.c,5696,2.06,2,2,1,5,2,0,0,6
1938,sqlite3MisuseError,main.c,3857,2.05,26,0,1,4,1,0,0,1
1939,sqlite3VdbeIntegerAffinity,vdbemem.c,690,2.05,2,1,3,16,1,0,2,2
1940,sqlite3OsInit,os.c,345,2.05,1,3,1,6,0,0,0,6
1941,btreeParseCellPtrIndex,btree.c,1344,2.04,0,1,5,33,3,0,2,2
1942,sqlite3FunctionSearch,callback.c,341,2.04,2,1,3,13,2,0,2,2
1943,sqlite3SubInt64,util.c,1600,2.04,2,1,3,11,2,0,2,2
1944,testpcacheInit,test_pcache.c,53,2.03,0,4,1,7,1,0,0,6
1945,pagerUnlockDb,pager.c,1128,2.03,7,0,3,16,2,0,2,1
1946,closedir,test_windirent.c,176,2.02,5,2,3,11,1,0,1,3
1947,sqlite3ResolveSelectNames,resolve.c,2251,2.02,2,1,1,14,3,0,0,6
1948,pagerLockDb,pager.c,1156,2.02,7,0,3,12,2,0,2,1
1949,sqlite3PcacheSetSpillsize,pcache.c,875,2.02,1,1,4,13,2,0,2,2
1950,sehExceptionFilter,wal.c,656,2.01,7,0,3,10,3,0,2,1
1951,areDoubleQuotedStringsEnabled,resolve.c,161,2.01,1,1,4,11,2,0,2,2
1952,xorMemory,os_win.c,5877,2.01,7,0,3,9,3,0,2,1
1953,test_blob_read,test_blob.c,213,2.01,0,5,6,37,4,0,2,1
1954,sqlite3_vfs_register,os.c,408,2.01,9,2,3,18,2,0,1,2
1955,strContainsChar,func.c,1269,2.01,1,1,4,9,3,0,2,2
1956,sqlite3_bind_zeroblob64,vdbeapi.c,1875,2.01,0,3,2,14,3,0,1,4
1957,sqlite3PutVarint,util.c,1207,2.01,10,1,3,12,2,0,1,2
1958,superlockIsWal,test_superlock.c,67,2.01,0,2,4,14,1,0,2,2
1959,DbHookCmd,tclsqlite.c,1950,2.00,1,5,5,25,4,0,2,1
1960,MD5Final,test_md5.c,236,2.00,3,10,2,21,2,0,1,2
1961,quotaRemoveAllFiles,test_quota.c,218,2.00,1,2,2,6,1,0,1,4
1962,binaryCompareP5,expr.c,398,2.00,1,2,1,9,3,0,0,6
1963,sqlite3PragmaVtabRegister,pragma.c,3060,2.00,1,1,3,9,2,0,1,4
1964,heightOfSelect,expr.c,824,2.00,2,6,2,11,2,0,1,3
1965,sqlite3RCStrUnref,printf.c,1402,1.99,6,1,2,11,1,0,1,3
1966,fsdirClose,test_fs.c,219,1.99,0,3,2,7,1,0,1,4
1967,vdbeClrCopy,vdbemem.c,1084,1.99,1,2,1,5,3,0,0,6
1968,computeYMD_HMS,date.c,506,1.99,10,2,1,4,1,0,0,4
1969,test_value_overhead,test5.c,57,1.98,0,1,4,27,4,0,2,2
1970,findNextHostParameter,vdbetrace.c,29,1.98,1,1,3,17,2,0,2,2
1971,sqlite3PcacheMove,pcache.c,664,1.98,4,2,3,22,2,0,1,3
1972,winCheckReservedLock,os_win.c,3386,1.98,0,2,3,22,2,0,2,2
1973,file_control_tempfilename,test1.c,7070,1.98,0,6,4,25,4,0,1,3
1974,file_control_vfsname,test1.c,7008,1.98,0,6,4,25,4,0,1,3
1975,sqliteDefaultBusyCallback,main.c,1693,1.98,0,1,4,26,2,0,2,2
1976,sqlite3VdbeMemIntegerify,vdbemem.c,722,1.98,6,1,1,9,1,0,0,5
1977,vfslog_eventname,test_osinst.c,769,1.98,0,0,25,30,1,0,1,1
1978,test_prepare16,test1.c,5102,1.98,0,4,6,45,4,0,2,1
1979,test_prepare16_v2,test1.c,5162,1.98,0,4,6,45,4,0,2,1
1980,matchQuality,callback.c,299,1.98,2,0,9,25,3,0,2,1
1981,collationMatch,build.c,5480,1.97,1,1,3,12,2,0,2,2
1982,test_install_mutex_counters,test_mutex.c,196,1.97,0,2,8,51,4,0,2,1
1983,sqlite3SelectPopWith,select.c,5916,1.97,1,1,3,10,2,0,2,2
1984,sqlite3ThreadJoin,threads.c,184,1.96,3,3,3,19,2,0,1,3
1985,sqlite3_uri_int64,main.c,4788,1.96,1,1,2,12,3,0,1,4
1986,sqlite3BtreeSetSpillSize,btree.c,2995,1.95,4,3,1,9,2,0,0,5
1987,gatherSelectWindows,expr.c,1781,1.95,1,1,1,9,1,0,0,6
1988,md5_cmd,test_md5.c,311,1.95,0,7,2,23,4,0,1,3
1989,sqlite3_str_finish,printf.c,1108,1.95,0,2,2,10,1,0,1,4
1990,test_stmt_utf16,test1.c,5733,1.94,3,2,4,29,4,0,2,1
1991,hasColumn,build.c,2214,1.94,6,0,3,8,3,0,2,1
1992,sqlite3SelectAddTypeInfo,select.c,6489,1.94,1,1,1,8,2,0,0,6
1993,sqlite3IsLikeFunction,func.c,2285,1.94,1,1,8,35,4,0,2,1
1994,walRestartHdr,wal.c,2140,1.94,2,5,1,15,2,0,0,5
1995,dupedExprSize,expr.c,1557,1.94,3,3,3,9,1,0,1,3
1996,sqlite3FixSelect,attach.c,569,1.94,1,1,1,6,2,0,0,6
1997,fsDelete,test_onefile.c,686,1.94,0,6,4,19,3,0,2,1
1998,test_enable_load,test1.c,2093,1.93,0,5,4,27,4,0,1,3
1999,pragmaVtabOpen,pragma.c,2895,1.93,0,2,1,9,2,0,0,6
2000,fstreeOpen,test_fs.c,413,1.93,0,2,1,9,2,0,0,6
2001,testBitvecBuiltinTest,test2.c,670,1.93,0,4,6,27,4,0,2,1
2002,sqlite3OsClose,os.c,82,1.93,15,0,2,6,1,0,1,1
2003,tclOpen,test_bestindex.c,248,1.93,0,2,1,8,2,0,0,6
2004,multiplexClose,test_multiplex.c,701,1.93,0,2,1,8,1,0,0,6
2005,fsdirOpen,test_fs.c,204,1.93,0,2,1,8,2,0,0,6
2006,vlogOpen,test_osinst.c,937,1.93,0,2,1,8,2,0,0,6
2007,pragmaFunclistLine,pragma.c,332,1.93,2,0,7,42,4,0,2,1
2008,sqlite3VdbeMemZeroTerminateIfAble,vdbemem.c,324,1.93,2,0,8,24,1,0,2,1
2009,test_normalize,test1.c,5372,1.93,0,6,3,21,4,0,1,3
2010,sqlite3BtreeGetRequestedReserve,btree.c,3125,1.93,3,3,2,8,1,0,0,5
2011,ptrmapPageno,btree.c,1036,1.93,14,0,3,13,2,0,1,1
2012,sqlite3BtreeGetAutoVacuum,btree.c,3211,1.93,3,2,3,11,1,0,0,5
2013,sqlite3_column_int,vdbeapi.c,1388,1.92,0,2,1,5,2,0,0,6
2014,sqlite3_column_double,vdbeapi.c,1383,1.92,0,2,1,5,2,0,0,6
2015,sqlite3RowSetDelete,rowset.c,172,1.92,0,2,1,4,1,0,0,6
2016,freeIdxStr,where.c,1616,1.92,5,1,2,7,1,0,1,3
2017,quotaRemoveFile,test_quota.c,207,1.92,5,1,2,7,1,0,1,3
2018,quotaGroupFind,test_quota.c,329,1.92,5,1,2,6,1,0,1,3
2019,jsonEachBestIndex,json.c,5158,1.91,0,0,9,55,2,0,2,1
2020,t1_ifnullFunc,test1.c,731,1.91,0,1,3,15,3,0,2,2
2021,sqlite3AutoincrementEnd,insert.c,568,1.91,5,1,2,3,1,0,1,3
2022,anotherValidCursor,btree.c,9017,1.91,0,1,3,12,1,0,2,2
2023,sqlite3WalEndReadTransaction,wal.c,3472,1.91,4,2,2,7,1,0,1,3
2024,test_mmap_warm,test1.c,8489,1.91,0,6,3,24,4,0,2,1
2025,sqlite3_free_filename,main.c,4738,1.90,4,2,2,5,1,0,1,3
2026,sqlite3GetBoolean,pragma.c,97,1.90,5,1,1,3,2,0,0,5
2027,sqlite3BtreeSetPagerFlags,btree.c,3029,1.89,3,3,1,11,2,0,0,5
2028,installTestPCache,test_pcache.c,428,1.89,0,5,3,40,4,0,2,1
2029,vdbeMemRenderNum,vdbemem.c,106,1.89,2,3,3,15,3,0,1,3
2030,sqlite3_get_auxdata,vdbeapi.c,1151,1.89,5,0,3,11,2,0,2,1
2031,compileoptiongetFunc,func.c,1067,1.89,0,1,1,11,3,0,0,6
2032,walDecodeFrame,wal.c,993,1.88,2,7,4,30,5,0,1,2
2033,numberOfCachePages,pcache.c,277,1.88,5,0,3,10,1,0,2,1
2034,jsonArgIsJsonb,json.c,3374,1.88,1,3,4,22,2,0,1,3
2035,winSync,os_win.c,2998,1.88,0,4,4,41,2,0,2,1
2036,tclvarAddToIdxstr,test_tclvar.c,306,1.88,5,0,3,9,2,0,2,1
2037,identPut,build.c,2058,1.88,2,0,7,20,3,0,2,1
2038,sqlite3IsRowid,expr.c,2952,1.88,6,3,4,6,1,0,1,2
2039,termFromWhereClause,where.c,1387,1.88,5,0,3,8,2,0,2,1
2040,ctimestampFunc,date.c,1705,1.88,0,1,1,8,3,0,0,6
2041,sorter_test_fakeheap,test1.c,8234,1.88,0,3,6,26,4,0,2,1
2042,whereLoopFindLesser,where.c,2723,1.88,2,0,6,37,2,0,2,1
2043,sqlite3AddInt64,util.c,1580,1.87,4,0,4,16,2,0,2,1
2044,tclvarOpen,test_tclvar.c,110,1.87,0,1,1,6,2,0,0,6
2045,fsOpen,test_fs.c,657,1.87,0,1,1,6,2,0,0,6
2046,vdbePmaWriteBlob,vdbesort.c,1476,1.87,3,1,4,21,3,0,2,1
2047,memdbFromDbSchema,memdb.c,734,1.87,2,2,4,12,2,0,1,3
2048,tclRowid,test_bestindex.c,392,1.87,0,1,1,5,2,0,0,6
2049,fsRowid,test_fs.c,758,1.87,0,1,1,5,2,0,0,6
2050,invokeValueDestructor,vdbeapi.c,412,1.87,7,2,3,15,3,0,1,2
2051,sqlite3ExprNeedsNoAffinityChange,expr.c,2916,1.87,2,0,6,32,2,0,2,1
2052,sqlite3VdbeJumpHereOrPopInst,vdbeaux.c,1333,1.86,4,1,2,11,2,0,1,3
2053,sqlite3UpsertNextIsIPK,upsert.c,227,1.86,2,0,7,13,1,0,2,1
2054,memjrnlFreeChunks,memjournal.c,133,1.86,4,1,2,8,1,0,1,3
2055,sqlite3PcacheDirtyList,pcache.c,818,1.85,4,1,2,7,1,0,1,3
2056,sqlite3TestHexToBin,test_hexio.c,52,1.85,3,0,4,35,3,0,2,1
2057,sqlite3BtreePager,btree.c,10483,1.85,23,0,1,3,1,0,0,1
2058,insertElement,hash.c,72,1.85,2,0,6,26,3,0,2,1
2059,sqlite3_busy_timeout,main.c,1815,1.85,3,2,2,10,2,0,1,3
2060,sqlite3_wal_autocheckpoint,main.c,2431,1.84,3,2,2,8,2,0,1,3
2061,walFramePgno,wal.c,1205,1.84,3,2,2,8,2,0,1,3
2062,pagerOpenWal,pager.c,7548,1.84,2,2,3,16,1,0,1,3
2063,walMerge,wal.c,1804,1.84,2,1,4,34,6,0,2,1
2064,computeYMD,date.c,461,1.84,5,1,6,25,1,0,1,2
2065,sqlite3_error_offset,main.c,2663,1.83,2,3,2,9,1,0,1,3
2066,page_read,test2.c,417,1.83,0,5,2,18,4,0,1,3
2067,vdbeMergeEngineCompare,vdbesort.c,2033,1.83,1,1,5,39,2,0,2,1
2068,sqlite3Int64ToText,util.c,729,1.83,1,1,6,21,2,0,2,1
2069,vfsUnlink,os.c,386,1.83,2,0,6,15,1,0,2,1
2070,pager_close,test2.c,78,1.83,0,4,3,21,4,0,1,3
2071,sqlite3ExprColUsed,resolve.c,179,1.82,3,0,4,21,1,0,2,1
2072,sqlite3VdbeMemTooBig,vdbemem.c,1025,1.82,4,0,3,11,1,0,2,1
2073,sqlite3Utf8ReadLimited,utf.c,179,1.82,3,0,4,20,3,0,2,1
2074,cachedCellSize,btree.c,7545,1.82,8,1,2,5,2,0,1,2
2075,cteClear,build.c,5686,1.82,2,3,1,6,2,0,0,5
2076,heightOfExpr,expr.c,809,1.81,4,0,3,7,2,0,2,1
2077,whereLikeOptimizationStringFixup,wherecode.c,1020,1.81,3,1,2,16,3,0,1,3
2078,moveToParent,btree.c,5459,1.81,3,1,1,19,1,0,0,5
2079,faultsimInstall,test_malloc.c,178,1.81,1,0,6,35,1,0,2,1
2080,substExprList,select.c,3965,1.81,7,1,3,10,2,0,1,2
2081,memdbLeave,memdb.c,198,1.81,17,1,1,3,1,0,0,2
2082,pagerFlushOnCommit,pager.c,1967,1.80,2,1,4,6,2,0,1,3
2083,sqlite3PageFree,pcache1.c,501,1.80,8,1,1,3,1,0,0,4
2084,checkProgress,btree.c,10501,1.80,2,1,4,19,1,0,2,1
2085,dbReleaseColumnNames,tclsqlite.c,1625,1.79,2,2,3,11,1,0,2,1
2086,cellSizePtrTableLeaf,btree.c,1508,1.79,0,0,7,39,2,0,2,1
2087,vdbePmaWriterFinish,vdbesort.c,1509,1.79,2,2,2,14,2,0,1,3
2088,winLockSharedMemory,os_win.c,3897,1.79,2,0,5,18,1,0,2,1
2089,sqlite3PcacheCleanAll,pcache.c,630,1.79,3,1,2,7,1,0,1,3
2090,sqlite3VListNameToNum,util.c,1848,1.79,2,1,4,12,3,0,2,1
2091,sqlite3VdbeTypeofColumn,vdbeaux.c,1305,1.79,3,1,2,6,2,0,1,3
2092,sqlite3ProcessReturningSubqueries,trigger.c,1008,1.78,1,3,2,16,2,0,1,3
2093,pragmaVtabBestIndex,pragma.c,2857,1.78,0,0,7,35,2,0,2,1
2094,btreePageLookup,btree.c,2344,1.78,2,2,2,9,2,0,1,3
2095,sqlite3VdbeCursorRestore,vdbeaux.c,3823,1.78,2,2,2,7,1,0,1,3
2096,sqlite3PCacheBufferSetup,pcache1.c,275,1.77,1,0,6,21,3,0,2,1
2097,sqlite3DequoteToken,util.c,376,1.77,2,0,5,10,1,0,2,1
2098,sqlite3SafetyCheckSickOrOk,util.c,1560,1.77,7,1,2,13,1,0,1,2
2099,sqlite3_uri_parameter,main.c,4756,1.77,2,2,2,5,2,0,1,3
2100,walEncodeFrame,wal.c,962,1.77,1,8,2,23,5,0,1,2
2101,alsoAnInt,vdbe.c,329,1.77,1,3,2,9,3,0,1,3
2102,sqlite3VListAdd,util.c,1794,1.76,1,1,4,30,5,0,2,1
2103,jsonIs4HexB,json.c,1286,1.76,2,1,3,6,2,0,1,3
2104,test_limit,test1.c,7164,1.76,0,7,3,52,4,0,1,2
2105,vlogClose,test_osinst.c,950,1.76,0,4,2,11,1,0,1,3
2106,getCellInfo,btree.c,4845,1.76,7,1,2,8,1,0,1,2
2107,sqlite3BtreeSchemaLocked,btree.c,11306,1.76,1,3,1,9,1,0,0,5
2108,testCreateCollationDel,test1.c,1835,1.76,0,4,2,10,1,0,1,3
2109,sqlite3_extended_errcode,main.c,2729,1.76,1,2,3,9,1,0,1,3
2110,initMemArray,vdbeaux.c,2146,1.75,3,0,3,11,4,0,2,1
2111,sqlite3BtreeMaxPageCount,btree.c,3140,1.75,1,3,1,7,2,0,0,5
2112,sqlite3_limit,main.c,2895,1.75,1,0,5,30,3,0,2,1
2113,releasePageNotNull,btree.c,2411,1.75,7,1,1,9,1,0,0,4
2114,codeReal,expr.c,4179,1.75,3,0,3,9,4,0,2,1
2115,installInitWrappers,test_init.c,156,1.75,1,2,1,28,0,0,0,5
2116,tclvarClose,test_tclvar.c,120,1.75,0,3,3,11,1,0,1,3
2117,rowSetListToTree,rowset.c,376,1.75,2,1,2,17,1,0,1,3
2118,sqlite3WindowUnlinkFromSelect,window.c,1122,1.75,3,0,3,7,1,0,2,1
2119,isAllZero,vdbeaux.c,4473,1.75,3,0,3,7,2,0,2,1
2120,sqlite3ExprListSetSortOrder,expr.c,2144,1.74,1,0,5,26,3,0,2,1
2121,sqlite3VdbeSerialTypeLen,vdbeaux.c,3969,1.74,12,0,2,9,1,0,1,1
2122,dbEvalColumnValue,tclsqlite.c,1815,1.74,0,2,4,26,2,0,2,1
2123,kahanBabuskaNeumaierStepInt64,func.c,1784,1.74,5,3,2,11,2,0,1,2
2124,putVarint64,util.c,1183,1.74,1,0,5,24,2,0,2,1
2125,fkChildIsModified,fkey.c,798,1.74,2,0,4,14,4,0,2,1
2126,sqlite3_db_filename,main.c,4865,1.74,1,2,2,5,2,0,0,5
2127,sqlite3_db_readonly,main.c,4881,1.74,1,2,2,5,2,0,0,5
2128,sqlite3FkReferences,fkey.c,676,1.74,7,1,1,3,1,0,0,4
2129,sqlite3GetVTable,vtab.c,192,1.74,12,0,2,6,2,0,1,1
2130,quotaEnter,test_quota.c,190,1.74,16,1,1,1,0,0,0,2
2131,computeHMS,date.c,490,1.73,2,1,2,12,1,0,1,3
2132,sqlite3ExprIdToTrueFalse,expr.c,2323,1.73,2,1,2,12,1,0,1,3
2133,cellSizePtrIdxLeaf,btree.c,1445,1.73,0,0,6,32,2,0,2,1
2134,sqlite3_quota_initialize,test_quota.c,749,1.73,1,1,2,39,2,0,1,3
2135,printfFunc,func.c,311,1.73,0,3,2,22,3,0,1,3
2136,sqlite3RootPageMoved,build.c,3211,1.73,1,0,5,21,4,0,2,1
2137,osLocaltime,date.c,554,1.73,1,2,3,12,2,0,2,1
2138,sqlite3GetUInt32,util.c,1142,1.73,2,0,4,11,2,0,2,1
2139,sqlite3VListNumToName,util.c,1832,1.73,2,0,4,11,2,0,2,1
2140,whereRangeAdjust,where.c,1901,1.73,2,0,4,11,2,0,2,1
2141,btreeHeapPull,btree.c,10731,1.72,1,0,5,18,2,0,2,1
2142,pager_truncate,test2.c,370,1.72,0,3,2,18,4,0,1,3
2143,sqlite3SubqueryDetach,build.c,4913,1.72,2,1,1,10,2,0,0,5
2144,testBestIndexObjConstraints,test_bestindex.c,403,1.72,0,0,4,62,2,0,2,1
2145,vtabDisconnectAll,vtab.c,229,1.72,2,0,3,24,2,0,2,1
2146,sqlite3ArrayAllocate,build.c,4634,1.72,2,0,3,23,5,0,2,1
2147,sqlite3MemdbInit,memdb.c,922,1.72,1,2,2,10,0,0,1,3
2148,sqlite3VdbeMemRealify,vdbemem.c,737,1.72,2,1,1,8,1,0,0,5
2149,sqlite3_result_error_code,vdbeapi.c,668,1.71,11,0,3,7,2,0,1,1
2150,sqlite3_create_filename,main.c,4702,1.71,1,5,4,30,5,0,1,2
2151,vdbeSorterRowkey,vdbesort.c,2673,1.71,2,0,3,21,2,0,2,1
2152,dupedExprStructSize,expr.c,1512,1.71,2,0,3,20,2,0,2,1
2153,pager_error,pager.c,1933,1.71,6,1,2,15,2,0,1,2
2154,vfslog_finalize,test_osinst.c,691,1.71,1,2,2,7,1,0,1,3
2155,sqlite3_column_int64,vdbeapi.c,1393,1.71,2,1,1,5,2,0,0,5
2156,sqlite3_column_text,vdbeapi.c,1398,1.71,2,1,1,5,2,0,0,5
2157,sqlite3PcacheSetPageSize,pcache.c,359,1.71,2,0,3,19,2,0,2,1
2158,tclDisconnect,test_bestindex.c,233,1.71,0,3,2,11,1,0,1,3
2159,walIndexWriteHdr,wal.c,935,1.71,3,5,1,11,1,0,0,4
2160,sqlite3_strlike,func.c,876,1.70,1,1,3,9,3,0,1,3
2161,sqlite3BtreeEnterCursor,btmutex.c,299,1.70,2,1,1,3,1,0,0,5
2162,sqlite3MayAbort,build.c,5388,1.70,20,0,2,4,1,0,0,1
2163,growVTrans,vtab.c,732,1.70,2,0,3,15,1,0,2,1
2164,sqlite3SubqueryDelete,build.c,4903,1.70,1,2,1,5,2,0,0,5
2165,downgradeAllSharedCacheTableLocks,btree.c,513,1.69,2,0,3,13,1,0,2,1
2166,whereAndInfoDelete,whereexpr.c,36,1.69,1,2,1,4,2,0,0,5
2167,whereOrInfoDelete,whereexpr.c,28,1.69,1,2,1,4,2,0,0,5
2168,cellSizePtr,btree.c,1403,1.69,0,0,5,32,2,0,2,1
2169,sqlite3BtreeSetMmapLimit,btree.c,3010,1.69,0,3,1,8,2,0,0,5
2170,fsRead,test_onefile.c,400,1.69,0,0,5,31,4,0,2,1
2171,populateCellCache,btree.c,7519,1.69,1,1,3,16,3,0,2,1
2172,faultsimStep,test_malloc.c,65,1.69,2,2,6,22,0,0,1,2
2173,agginfoFree,select.c,7159,1.69,0,3,1,6,2,0,0,5
2174,sqlite3VdbeReusable,vdbeaux.c,668,1.68,2,0,3,9,1,0,2,1
2175,quotaGroupOpenFileCount,test_quota.c,195,1.68,2,0,3,9,1,0,2,1
2176,sqlite3PagerReadFileheader,pager.c,3867,1.68,1,1,3,14,3,0,2,1
2177,generateColumnTypes,select.c,2052,1.68,1,1,2,18,3,0,1,3
2178,sqlite3IndexHasDuplicateRootPage,prepare.c,61,1.68,2,0,3,7,1,0,2,1
2179,identLength,build.c,2037,1.68,2,0,3,7,1,0,2,1
2180,test_treetrace,test1.c,7787,1.68,0,2,3,16,4,0,2,1
2181,sqlite3Utf16ByteLen,utf.c,522,1.68,1,0,4,15,3,0,2,1
2182,windowCacheFrame,window.c,2031,1.68,1,0,4,15,1,0,2,1
2183,memdbEnter,memdb.c,195,1.67,15,1,1,3,1,0,0,2
2184,quotaFileSize,test_quota.c,629,1.67,0,2,2,19,2,0,1,3
2185,isSetNullAction,fkey.c,854,1.67,1,0,4,13,2,0,2,1
2186,test_collate_needed,test1.c,3431,1.67,0,2,2,18,4,0,1,3
2187,test_ex_sql,test1.c,5441,1.67,0,2,2,18,4,0,1,3
2188,sqlite3_reset_auto_extension,loadext.c,862,1.67,1,1,2,12,0,0,1,3
2189,dbEvalInit,tclsqlite.c,1649,1.67,2,5,2,19,5,0,1,2
2190,memdbFetch,memdb.c,510,1.67,0,2,2,17,4,0,1,3
2191,test_extended_result_codes,test1.c,1678,1.67,0,2,2,17,4,0,1,3
2192,renameColumnTokenNext,alter.c,1040,1.67,1,0,4,11,1,0,2,1
2193,quotaTruncate,test_quota.c,602,1.67,0,2,2,16,2,0,1,3
2194,changeTempStorage,pragma.c,183,1.66,1,1,2,10,2,0,1,3
2195,sqlite3MallocAlarm,malloc.c,217,1.66,4,3,2,6,1,0,1,2
2196,sqlite3OsRandomness,os.c,273,1.66,0,2,3,10,3,0,2,1
2197,winUnlockFile,os_win.c,2546,1.66,8,3,2,18,5,0,1,1
2198,pagerFreeMapHdrs,pager.c,4098,1.66,1,1,2,8,1,0,1,3
2199,analyzeOverKeyword,tokenize.c,254,1.66,1,1,2,7,2,0,1,3
2200,memdbTruncate,memdb.c,331,1.66,0,2,2,12,2,0,1,3
2201,sqlite3VdbeLoadString,vdbeaux.c,376,1.66,20,0,1,3,3,0,0,1
2202,sqlite3LookasideUsed,status.c,188,1.66,3,4,2,8,2,0,1,2
2203,sqlite3VdbeTakeOpArray,vdbeaux.c,1120,1.65,1,1,1,9,3,0,0,5
2204,vfslog_call,test_osinst.c,652,1.65,0,7,2,23,7,0,1,2
2205,winMutexFree,mutex_w32.c,260,1.65,0,2,2,9,1,0,1,3
2206,sqlite3_vtab_in,where.c,4443,1.65,0,0,5,13,3,0,2,1
2207,sqlite3Atoi,util.c,994,1.65,10,1,1,5,1,0,0,3
2208,walIndexHdr,wal.c,820,1.65,10,1,1,5,1,0,0,3
2209,intarrayFree,test_intarray.c,64,1.64,0,2,2,7,1,0,1,3
2210,sqlite3_filename_wal,main.c,4825,1.64,0,2,2,5,1,0,1,3
2211,sqlite3ExprAddCollateToken,expr.c,173,1.64,1,0,3,16,4,0,2,1
2212,indexIteratorNext,insert.c,1791,1.63,1,0,3,15,2,0,2,1
2213,sqlite3_column_blob,vdbeapi.c,1363,1.63,0,2,1,6,2,0,0,5
2214,openSubJournal,pager.c,4488,1.63,1,0,3,14,1,0,2,1
2215,jsonSkipLabel,json.c,4918,1.63,5,1,2,9,1,0,1,2
2216,sqlite3_column_type,vdbeapi.c,1419,1.63,0,2,1,5,2,0,0,5
2217,sqlite3_column_bytes,vdbeapi.c,1373,1.63,0,2,1,5,2,0,0,5
2218,sqlite3_column_bytes16,vdbeapi.c,1378,1.63,0,2,1,5,2,0,0,5
2219,sqlite3_column_text16,vdbeapi.c,1413,1.63,0,2,1,5,2,0,0,5
2220,freeP4FuncCtx,vdbeaux.c,1367,1.63,0,2,1,5,2,0,0,5
2221,whereReverseScanOrder,where.c,6542,1.63,1,0,3,13,1,0,2,1
2222,parseTimezone,date.c,166,1.63,1,1,7,29,2,0,1,2
2223,walWriteToLog,wal.c,3894,1.62,0,0,4,21,4,0,2,1
2224,vdbeSorterMapFile,vdbesort.c,614,1.62,1,0,3,11,3,0,2,1
2225,tclvarNext,test_tclvar.c,164,1.62,0,1,3,16,1,0,2,1
2226,allConstraintsUsed,where.c,4196,1.62,1,0,3,10,2,0,2,1
2227,rowSetTreeToList,rowset.c,305,1.62,3,2,3,20,3,0,1,2
2228,cursorOnLastPage,btree.c,5923,1.62,1,0,3,9,1,0,2,1
2229,walWriteOneFrame,wal.c,3919,1.61,0,1,2,16,4,0,1,3
2230,writeSuperJournal,pager.c,1699,1.61,1,1,6,40,2,0,1,2
2231,safeToUseEvalObjv,tclsqlite.c,513,1.61,0,1,3,10,1,0,2,1
2232,sqlite3ExprCanBeNull,expr.c,2875,1.61,8,0,4,27,1,0,1,1
2233,walUnlockShared,wal.c,1085,1.61,10,0,2,7,2,0,1,1
2234,exec_printf_cb,test1.c,195,1.60,0,0,4,13,4,0,2,1
2235,sqlite3_mutex_free,mutex.c,302,1.60,10,0,2,6,1,0,1,1
2236,vfs_reregister_all,test1.c,6567,1.60,0,1,2,12,4,0,1,3
2237,winUnlockReadLock,os_win.c,3196,1.60,2,3,3,18,1,0,1,2
2238,pager_write_changecounter,pager.c,3067,1.60,3,3,2,8,1,0,1,2
2239,strlen30,tclsqlite.c,232,1.60,10,0,2,5,1,0,1,1
2240,btreePageFromDbPage,btree.c,2298,1.60,3,2,3,12,3,0,1,2
2241,winLockFile,os_win.c,2514,1.60,7,3,2,19,6,0,1,1
2242,vlogDisconnect,test_osinst.c,924,1.60,0,1,2,9,1,0,1,3
2243,faultsimMalloc,test_malloc.c,92,1.59,0,1,2,7,1,0,1,3
2244,faultsimRealloc,test_malloc.c,105,1.59,0,1,2,7,2,0,1,3
2245,sqlite3OsOpenMalloc,os.c,308,1.59,0,0,3,25,5,0,2,1
2246,ctimeFunc,date.c,1568,1.58,0,1,1,8,3,0,0,5
2247,multiplexFileSize,test_multiplex.c,857,1.58,0,0,3,22,2,0,2,1
2248,test_open,test1.c,5217,1.58,0,5,3,20,4,0,1,2
2249,walCkptInfo,wal.c,811,1.58,9,1,1,5,1,0,0,3
2250,fstreeBestIndex,test_fs.c,385,1.58,0,0,3,20,2,0,2,1
2251,test_wal_checkpoint,test1.c,7426,1.58,0,4,4,23,4,0,1,2
2252,jsonEachDisconnect,json.c,4852,1.58,0,1,1,5,1,0,0,5
2253,sqlite3_cancel_auto_extension,loadext.c,834,1.58,0,0,3,19,1,0,2,1
2254,statAccumDestructor,analyze.c,365,1.58,0,1,1,4,1,0,0,5
2255,test_sleep,test1.c,4685,1.57,0,5,3,17,4,0,1,2
2256,allocSpace,vdbeaux.c,2561,1.57,0,0,3,18,3,0,2,1
2257,sqlite3_progress_handler,main.c,1784,1.57,3,2,2,18,4,0,1,2
2258,fsBestIndex,test_fs.c,769,1.57,0,0,3,17,2,0,2,1
2259,fsdirBestIndex,test_fs.c,178,1.57,0,0,3,17,2,0,2,1
2260,sqlite3_filename_journal,main.c,4815,1.57,1,4,3,10,1,0,1,2
2261,sqlite3ReturningSubqueryCorrelated,trigger.c,982,1.57,0,0,3,16,2,0,2,1
2262,pagerFixMaplimit,pager.c,3516,1.57,4,1,2,10,1,0,1,2
2263,sqlite3PcacheDrop,pcache.c,579,1.56,4,1,2,9,1,0,1,2
2264,returnSingleText,pragma.c,225,1.56,4,1,2,9,2,0,1,2
2265,sqlite3WalEndWriteTransaction,wal.c,3712,1.56,4,1,2,9,1,0,1,2
2266,sqlite3_os_init,os_win.c,6066,1.56,1,6,1,109,0,0,0,3
2267,getToken,tokenize.c,197,1.56,3,1,3,18,1,0,1,2
2268,sqlite3DbNameToBtree,main.c,4838,1.56,3,1,3,4,2,0,0,4
2269,sqlite3PcacheMakeClean,pcache.c,613,1.56,3,2,2,13,1,0,1,2
2270,vfslog_string,test_osinst.c,676,1.56,0,4,4,14,2,0,1,2
2271,walShmBarrier,wal.c,911,1.56,4,1,2,5,1,0,1,2
2272,renameSetENames,alter.c,1290,1.55,0,0,3,9,2,0,2,1
2273,file_control_lasterrno_test,test1.c,6622,1.55,0,8,5,29,4,0,1,1
2274,sqlite3ExprToRegister,expr.c,4378,1.55,3,1,3,12,2,0,1,2
2275,sqlite3_uri_key,main.c,4765,1.55,0,4,4,10,2,0,1,2
2276,sqlite3_set_authorizer,auth.c,70,1.55,2,3,2,12,3,0,1,2
2277,testHexToBin,test_func.c,336,1.54,3,2,2,6,2,0,1,2
2278,finalDbSize,btree.c,4093,1.54,2,2,3,15,3,0,1,2
2279,tableOfTrigger,trigger.c,666,1.54,4,1,1,3,1,0,0,4
2280,sqlite3LogEstFromDouble,util.c,1734,1.53,2,2,3,10,1,0,1,2
2281,test_changes,test1.c,3143,1.53,0,5,2,16,4,0,1,2
2282,sqlite3_user_data,vdbeapi.c,949,1.53,18,0,1,4,1,0,0,1
2283,sqlite3StrIHash,util.c,451,1.52,8,0,3,9,1,0,1,1
2284,jsonCreateEditSubstructure,json.c,2770,1.52,2,2,2,24,3,0,1,2
2285,sqlite3MallocInit,malloc.c,159,1.52,1,2,4,15,0,0,1,2
2286,sqlite3IsTrueOrFalse,expr.c,2311,1.52,2,2,3,5,1,0,1,2
2287,sqlite3BtreePayloadSize,btree.c,4916,1.52,8,1,1,6,1,0,0,3
2288,getTempStore,pragma.c,141,1.51,1,2,4,11,1,0,1,2
2289,sqlite3PagerLockingMode,pager.c,7295,1.51,3,1,2,12,2,0,1,2
2290,pager_wait_on_lock,pager.c,3916,1.50,3,1,2,11,2,0,1,2
2291,sqlite3RecordErrorOffsetOfExpr,printf.c,934,1.50,7,0,4,10,2,0,1,1
2292,sqlite3VdbeHandleMovedCursor,vdbeaux.c,3808,1.50,3,1,2,10,1,0,1,2
2293,sqlite3_profile,main.c,2242,1.50,2,2,2,15,3,0,1,2
2294,pagerExclusiveLock,pager.c,7526,1.49,2,2,2,11,1,0,1,2
2295,sqlite3_autovacuum_pages,main.c,2374,1.49,1,3,2,16,4,0,1,2
2296,sqlite3_enable_load_extension,loadext.c,735,1.49,2,2,2,10,2,0,1,2
2297,getTextArg,printf.c,129,1.49,3,1,2,4,1,0,1,2
2298,sqlite3PagerSetFlags,pager.c,3595,1.48,2,0,10,34,2,0,1,1
2299,test_register_func,test1.c,2198,1.48,0,3,3,23,4,0,1,2
2300,btreeSetNPage,btree.c,4457,1.48,2,2,2,7,2,0,1,2
2301,winClose,os_win.c,2664,1.48,0,3,3,22,1,0,1,2
2302,estimateIndexWidth,build.c,2198,1.48,2,1,3,11,1,0,1,2
2303,winIsVerbatimPathname,os_win.c,5566,1.48,2,1,3,11,1,0,1,2
2304,sqlite3PagerRekey,pager.c,7263,1.48,3,1,1,5,3,0,0,4
2305,sqlite3SelectExprHeight,expr.c,878,1.48,3,1,1,5,1,0,0,4
2306,register_fs_module,test_fs.c,885,1.48,0,4,2,17,4,0,1,2
2307,test_system_errno,test1.c,6202,1.48,0,4,2,17,4,0,1,2
2308,walUnlockExclusive,wal.c,1109,1.48,8,0,2,8,3,0,1,1
2309,whereRangeScanEst,where.c,2077,1.48,0,2,4,25,5,0,1,2
2310,sqlite3_trace_v2,main.c,2212,1.48,1,2,3,15,4,0,1,2
2311,sqlite3SetHasNullFlag,expr.c,3033,1.48,2,2,1,9,3,0,0,4
2312,sqlite3ExprSkipCollate,expr.c,203,1.48,8,0,2,7,1,0,1,1
2313,sqlite3ExpirePreparedStatements,vdbeaux.c,5314,1.47,8,0,2,6,2,0,1,1
2314,test_is_interrupted,test1.c,5827,1.47,0,3,3,17,4,0,1,2
2315,columnMem,vdbeapi.c,1308,1.46,0,3,3,15,2,0,1,2
2316,kahanBabuskaNeumaierStep,func.c,1767,1.46,6,2,2,13,2,0,1,1
2317,sqlite3VdbeExplainPop,vdbeaux.c,550,1.45,7,1,1,4,1,0,0,3
2318,file_control_external_reader,test1.c,7102,1.45,0,6,5,29,4,0,1,1
2319,test_stmt_utf8,test1.c,5675,1.44,3,3,4,30,4,0,1,1
2320,abuse_create_function,test_func.c,745,1.44,0,1,10,49,4,0,1,1
2321,hexio_render_int32,test_hexio.c,276,1.44,0,3,2,21,4,0,1,2
2322,hexio_render_int16,test_hexio.c,249,1.43,0,3,2,19,4,0,1,2
2323,vdbeSafetyNotNull,vdbeapi.c,48,1.43,2,1,2,8,1,0,1,2
2324,strAccumFinishRealloc,printf.c,1057,1.43,1,2,2,13,1,0,1,2
2325,pcache1UnderMemoryPressure,pcache1.c,522,1.43,2,1,2,7,1,0,1,2
2326,test_create_null_module,test1.c,2548,1.43,0,3,2,17,4,0,1,2
2327,setDoNotMergeFlagOnCopy,expr.c,4431,1.42,2,1,2,5,1,0,1,2
2328,jsonbArrayCount,json.c,2474,1.42,1,2,2,10,2,0,1,2
2329,sqlite3VdbeParameterIndex,vdbeapi.c,1919,1.42,2,1,2,4,3,0,1,2
2330,register_tclvar_module,test_tclvar.c,501,1.42,0,2,2,37,4,0,1,2
2331,autoAdjustDate,date.c,682,1.42,1,1,3,13,1,0,1,2
2332,getDbPointer,test1.c,112,1.42,7,0,2,11,3,0,1,1
2333,test_enter_db_mutex,test_mutex.c,437,1.42,0,2,3,18,4,0,1,2
2334,test_leave_db_mutex,test_mutex.c,456,1.42,0,2,3,18,4,0,1,2
2335,sqlite3WalDefaultHook,main.c,2405,1.42,0,3,2,13,4,0,1,2
2336,sqlite3PagerLookup,pager.c,5726,1.42,7,0,2,10,2,0,1,1
2337,jsonStringInit,json.c,485,1.42,11,1,1,5,2,0,0,2
2338,dupedExprNodeSize,expr.c,1538,1.42,1,2,2,7,2,0,1,2
2339,sqllogTokenize,test_sqllog.c,151,1.41,1,1,3,11,3,0,1,2
2340,renameColumnExprCb,alter.c,1014,1.41,0,2,3,16,2,0,1,2
2341,sqlite3VtabSavepoint,vtab.c,1101,1.41,6,0,2,36,3,0,1,1
2342,estimateTableWidth,build.c,2184,1.41,1,1,3,10,1,0,1,2
2343,btreeClearHasContent,btree.c,682,1.41,2,1,1,4,1,0,0,4
2344,sqlite3ExprIsVector,expr.c,495,1.41,11,1,1,3,1,0,0,2
2345,winMutexTry,mutex_w32.c,307,1.41,0,2,3,15,1,0,1,2
2346,sqlite3BtreeLeaveCursor,btmutex.c,303,1.41,2,1,1,3,1,0,0,4
2347,jsonStringTrimOneChar,json.c,593,1.41,7,0,2,6,1,0,1,1
2348,sqlite3PCachePercentDirty,pcache.c,907,1.41,1,1,3,7,1,0,1,2
2349,renumberCursorsCb,select.c,4090,1.40,0,2,3,10,2,0,1,2
2350,setGetterMethod,pager.c,1040,1.39,6,0,3,9,1,0,1,1
2351,auth_callback,tclsqlite.c,1153,1.39,0,3,5,72,6,0,1,1
2352,sqlite3WhereExprUsage,whereexpr.c,1825,1.39,10,1,2,3,2,0,0,2
2353,recomputeColumnsUsedExpr,select.c,4012,1.39,0,1,4,9,2,0,1,2
2354,dekkerMul2,util.c,467,1.38,12,4,1,21,3,0,0,1
2355,tclFunction,test_bestindex.c,728,1.38,0,2,2,21,3,0,1,2
2356,incrblobWideSeek,tclsqlite.c,382,1.38,0,2,2,21,4,0,1,2
2357,sqlite3ColumnType,util.c,104,1.38,5,1,3,10,2,0,1,1
2358,walBusyLock,wal.c,2095,1.38,1,1,2,13,5,0,1,2
2359,sqlite3_status64,status.c,134,1.38,0,1,3,22,4,0,1,2
2360,db_enter,test1.c,383,1.37,0,2,2,16,4,0,1,2
2361,db_leave,test1.c,399,1.37,0,2,2,16,4,0,1,2
2362,sqlite3_quota_file_available,test_quota.c,1211,1.37,1,4,5,14,1,0,1,1
2363,unmapColumnIdlistNames,alter.c,863,1.37,1,1,2,10,2,0,1,2
2364,test_global_recover,test1.c,5708,1.37,0,2,2,15,4,0,1,2
2365,test_interrupt,test1.c,5806,1.37,0,2,2,15,4,0,1,2
2366,backupTestInit,test_backup.c,110,1.37,0,6,3,32,4,0,1,1
2367,sqlite3_compileoption_get,main.c,5071,1.37,1,1,2,9,1,0,1,2
2368,test_enter_static_mutex,test_mutex.c,399,1.37,0,1,3,18,4,0,1,2
2369,test_leave_static_mutex,test_mutex.c,418,1.37,0,1,3,18,4,0,1,2
2370,apiHandleError,malloc.c,888,1.37,1,1,2,8,2,0,1,2
2371,demoFileSize,test_demovfs.c,333,1.36,0,2,2,13,2,0,1,2
2372,sqlite3PagerJournalSizeLimit,pager.c,7440,1.36,1,1,2,7,2,0,1,2
2373,sqlite3PcacheInitialize,pcache.c,299,1.36,1,1,2,7,0,0,1,2
2374,whereRightSubexprIsColumn,where.c,297,1.36,1,1,2,7,1,0,1,2
2375,computeFloor,date.c,302,1.36,4,0,5,16,1,0,1,1
2376,sqlite3VdbeExplainParent,vdbeaux.c,495,1.36,1,1,2,6,1,0,1,2
2377,getStaticMutexPointer,test_mutex.c,387,1.36,0,2,2,11,2,0,1,2
2378,sqlite3_next_stmt,vdbeapi.c,2051,1.36,0,2,2,11,2,0,1,2
2379,walLockExclusive,wal.c,1094,1.36,6,0,2,13,3,0,1,1
2380,demoSync,test_demovfs.c,317,1.36,0,2,2,10,2,0,1,2
2381,nocaseCollatingFunc,main.c,1075,1.35,0,1,3,13,5,0,1,2
2382,renameUnmapExprCb,alter.c,815,1.35,0,2,2,8,2,0,1,2
2383,sqlite3ReadOnlyShadowTables,build.c,3416,1.35,6,0,2,10,1,0,1,1
2384,rtrimCollFunc,main.c,1046,1.35,0,1,3,11,5,0,1,2
2385,file_control_reservebytes,test1.c,7038,1.35,0,6,3,23,4,0,1,1
2386,readdir_r,test_windirent.c,131,1.34,0,4,5,31,3,0,1,1
2387,sqlite3PcacheClear,pcache.c,738,1.34,1,1,1,3,1,0,0,4
2388,sqlite3VarintLen,util.c,1431,1.34,6,0,2,5,1,0,1,1
2389,sqlite3SelectDestInit,select.c,112,1.34,15,0,1,8,3,0,0,1
2390,fsAccess,test_onefile.c,712,1.34,0,5,4,24,4,0,1,1
2391,file_control_chunksize_test,test1.c,6698,1.34,0,4,5,28,4,0,1,1
2392,runAsObjProc,test1.c,7612,1.34,0,5,4,23,4,0,1,1
2393,sqlite3DefaultRowEst,build.c,4506,1.34,2,1,6,21,1,0,1,1
2394,selectWindowRewriteEList,window.c,858,1.34,3,3,1,24,6,0,0,3
2395,readdir,test_windirent.c,98,1.33,1,3,5,20,1,0,1,1
2396,echoDestructor,test8.c,374,1.33,1,6,1,10,1,0,0,3
2397,vdbeRecordDecodeInt,vdbeaux.c,4656,1.33,1,0,8,32,2,0,1,1
2398,sqlthread_spawn,test_thread.c,170,1.33,0,6,2,33,4,0,1,1
2399,schemaColumn,test_schema.c,133,1.33,0,6,3,15,3,0,1,1
2400,testHexToInt,test1.c,47,1.33,5,0,3,10,1,0,1,1
2401,sqlite3PagerGet,pager.c,5693,1.33,14,1,1,8,4,0,0,1
2402,sqlite3BtreeSharedCacheReport,test_btree.c,25,1.32,0,1,2,17,4,0,1,2
2403,winUnfetch,os_win.c,4565,1.32,0,1,2,16,3,0,1,2
2404,register_tcl_module,test_bestindex.c,845,1.32,0,1,2,15,4,0,1,2
2405,sqlite3ExprAddCollateString,expr.c,189,1.32,4,2,1,10,3,0,0,3
2406,setDateTimeToCurrent,date.c,372,1.31,0,1,2,12,2,0,1,2
2407,codeApplyAffinity,wherecode.c,455,1.30,3,0,5,20,4,0,1,1
2408,renameTableExprCb,alter.c,1662,1.30,0,1,2,10,2,0,1,2
2409,sqlite3OsCloseFree,os.c,333,1.30,4,2,1,5,1,0,0,3
2410,read32bits,pager.c,1094,1.30,0,1,2,8,3,0,1,2
2411,resolveRemoveWindowsCb,resolve.c,1742,1.30,0,1,2,8,2,0,1,2
2412,sqlite3SrcItemColumnUsed,select.c,333,1.30,5,0,2,14,2,0,1,1
2413,renameQuotefixExprCb,alter.c,1872,1.30,0,1,2,6,2,0,1,2
2414,sqlite3_bind_parameter_name,vdbeapi.c,1908,1.29,0,1,2,5,2,0,1,2
2415,DbTraceV2Handler,tclsqlite.c,722,1.29,0,4,2,62,4,0,1,1
2416,sqlite3_filename_database,main.c,4811,1.29,0,1,2,4,1,0,1,2
2417,cdateFunc,date.c,1582,1.29,0,1,1,8,3,0,0,4
2418,journalHdrOffset,pager.c,1350,1.29,5,0,2,11,1,0,1,1
2419,walLockShared,wal.c,1072,1.29,5,0,2,11,2,0,1,1
2420,test_stmt_status,test1.c,2259,1.29,0,4,3,42,4,0,1,1
2421,test_sqlite3_log,test1.c,7581,1.29,0,4,4,24,4,0,1,1
2422,sqlite3UpsertOfIndex,upsert.c,247,1.29,5,0,2,10,2,0,1,1
2423,codeOffset,select.c,846,1.29,5,0,2,10,3,0,1,1
2424,byteReverse,test_md5.c,61,1.28,5,0,2,9,2,0,1,1
2425,memjrnlClose,memjournal.c,290,1.28,0,1,1,5,1,0,0,4
2426,demoFlushBuffer,test_demovfs.c,188,1.28,5,0,2,8,1,0,1,1
2427,winLogIoerr,os_win.c,2184,1.28,5,0,2,8,2,0,1,1
2428,isSimpleCount,select.c,5491,1.28,1,0,7,28,2,0,1,1
2429,databaseName,main.c,4676,1.28,5,0,2,6,1,0,1,1
2430,transferJoinMarkings,whereexpr.c,490,1.28,5,0,2,6,2,0,1,1
2431,windirent_getenv,test_windirent.c,23,1.27,3,2,2,14,1,0,1,1
2432,sqlite3TouchRegister,expr.c,7330,1.27,5,0,2,3,2,0,1,1
2433,daysAfterJan01,date.c,1329,1.27,4,1,1,11,1,0,0,3
2434,returnSingleInt,pragma.c,217,1.26,14,0,1,4,2,0,0,1
2435,btreePagecount,btree.c,2358,1.26,14,0,1,3,1,0,0,1
2436,sqlite3_win32_write_debug,os_win.c,1336,1.26,0,4,4,13,2,0,1,1
2437,sqlite3RecordErrorByteOffset,printf.c,913,1.26,1,1,6,16,2,0,1,1
2438,sqlite3BtreeIntegerKey,btree.c,4875,1.26,4,1,1,7,1,0,0,3
2439,sqlite3BtreeClearCursor,btree.c,848,1.25,4,1,1,6,1,0,0,3
2440,explainIndexColumnName,wherecode.c,28,1.25,4,0,3,6,2,0,1,1
2441,strftime_cmd,test1.c,7756,1.25,0,4,3,27,4,0,1,1
2442,sqlite3AbsInt32,util.c,1641,1.25,4,0,3,5,1,0,1,1
2443,memjrnlRead,memjournal.c,86,1.25,0,1,6,40,4,0,1,1
2444,test_create_aggregate,test1.c,1308,1.25,0,3,4,29,4,0,1,1
2445,vdbePmaWriteVarint,vdbesort.c,1528,1.24,3,2,1,6,2,0,0,3
2446,test_print_eqp,test1.c,7675,1.24,0,5,2,18,4,0,1,1
2447,test_function,test1.c,3600,1.24,0,2,5,31,4,0,1,1
2448,file_control_sizehint_test,test1.c,6735,1.23,0,2,5,28,4,0,1,1
2449,appendToEchoModule,test8.c,309,1.23,12,1,2,4,2,0,0,1
2450,sqlite3Delete83Name,test_delete.c,46,1.23,2,2,3,6,1,0,1,1
2451,sqlite3TestBinToHex,test_hexio.c,32,1.23,4,0,2,13,2,0,1,1
2452,serialGet7,vdbeaux.c,4087,1.22,3,1,2,17,2,0,1,1
2453,walAssertLockmask,wal.c,2442,1.22,2,0,5,13,1,0,1,1
2454,actionName,pragma.c,269,1.22,2,0,5,12,1,0,1,1
2455,sqlite3SelectOpName,select.c,1607,1.22,4,0,2,10,1,0,1,1
2456,sqlite3VdbeSetVarmask,vdbeaux.c,5366,1.22,4,0,2,10,2,0,1,1
2457,winSeekFile,os_win.c,2591,1.22,2,2,2,20,2,0,1,1
2458,sqlite3PagerFilename,pager.c,7055,1.22,4,0,2,8,2,0,1,1
2459,countLookasideSlots,status.c,176,1.22,4,0,2,8,1,0,1,1
2460,winRandomness,os_win.c,5891,1.21,0,9,2,28,3,0,0,2
2461,sqlite3LogEstToInt,util.c,1748,1.21,2,0,5,9,1,0,1,1
2462,invokeProfileCallback,vdbeapi.c,62,1.21,3,0,3,16,2,0,1,1
2463,sqlite3StrAccumInit,printf.c,1163,1.21,13,0,1,9,5,0,0,1
2464,sqlthread_parent,test_thread.c,223,1.21,0,4,2,27,4,0,1,1
2465,checkOom,btree.c,10491,1.21,4,0,2,5,1,0,1,1
2466,exprRowColumn,update.c,143,1.21,4,0,2,5,2,0,1,1
2467,operatorMask,whereexpr.c,139,1.21,2,0,4,24,1,0,1,1
2468,sqlite3TableColumnAffinity,expr.c,24,1.21,4,0,2,4,2,0,1,1
2469,allowedOp,whereexpr.c,99,1.20,3,0,3,12,1,0,1,1
2470,fstreeCloseFd,test_fs.c,423,1.20,3,1,2,6,1,0,1,1
2471,test_column_blob,test1.c,5587,1.20,0,4,2,22,4,0,1,1
2472,binCollFunc,main.c,1023,1.20,2,1,3,15,5,0,1,1
2473,test_bind_zeroblob64,test1.c,3730,1.19,0,3,3,25,4,0,1,1
2474,test_column_double,test1.c,5619,1.19,0,4,2,20,4,0,1,1
2475,test_column_int64,test1.c,5560,1.19,0,4,2,20,4,0,1,1
2476,test_stmt_explain,test1.c,2976,1.19,0,4,2,20,4,0,1,1
2477,sqlite3OomClear,malloc.c,876,1.19,3,0,3,8,1,0,1,1
2478,indexCellCompare,btree.c,5886,1.19,0,3,3,24,4,0,1,1
2479,test_next_stmt,test1.c,2891,1.19,0,3,3,23,4,0,1,1
2480,test_step,test1.c,5401,1.19,0,4,2,18,4,0,1,1
2481,test_stmt_busy,test1.c,3005,1.19,0,4,2,18,4,0,1,1
2482,test_stmt_isexplain,test1.c,2950,1.19,0,4,2,18,4,0,1,1
2483,test_stmt_readonly,test1.c,2923,1.19,0,4,2,18,4,0,1,1
2484,sqlite3RealToI64,vdbemem.c,767,1.18,3,0,3,5,1,0,1,1
2485,test_register_dbstat_vtab,test1.c,8344,1.18,0,3,3,20,4,0,1,1
2486,sqlite3BtreeRowCountEst,btree.c,6210,1.18,2,0,4,13,1,0,1,1
2487,sqlite3_quota_fflush,test_quota.c,1082,1.18,1,3,2,8,2,0,1,1
2488,file_control_lockproxy_test,test1.c,6773,1.17,0,3,3,17,4,0,1,1
2489,testHexChar,test_func.c,322,1.17,2,0,4,10,1,0,1,1
2490,test_column_type,test1.c,5511,1.17,0,2,3,38,4,0,1,1
2491,whereNthSubterm,whereexpr.c,511,1.17,2,0,4,9,2,0,1,1
2492,test_blob_open,test_blob.c,98,1.17,0,1,4,42,4,0,1,1
2493,tclScriptThread,test_thread.c,110,1.17,0,2,3,37,1,0,1,1
2494,winShmBarrier,os_win.c,4189,1.17,1,3,1,8,1,0,0,3
2495,sqlite3_busy_handler,main.c,1761,1.16,6,2,1,13,3,0,0,2
2496,btreeHeapInsert,btree.c,10719,1.16,3,0,2,12,2,0,1,1
2497,SqlitetestThread_Init,test_thread.c,636,1.16,2,1,2,17,1,0,1,1
2498,constraintCompatibleWithOuterJoin,where.c,827,1.16,2,0,3,21,2,0,1,1
2499,sqlite3PagerSync,pager.c,6367,1.16,3,0,2,11,2,0,1,1
2500,setSectorSize,pager.c,2711,1.16,3,0,2,11,1,0,1,1
2501,testpcacheDestroy,test_pcache.c,405,1.16,0,4,1,9,1,0,0,3
2502,sqlite3JournalOpen,memjournal.c,353,1.16,1,1,3,26,5,0,1,1
2503,fs_register,test_onefile.c,821,1.15,1,2,2,7,0,0,0,3
2504,strHash,hash.c,55,1.15,3,0,2,9,1,0,1,1
2505,testpcacheRandom,test_pcache.c,119,1.15,3,0,2,9,1,0,1,1
2506,testpcacheShutdown,test_pcache.c,67,1.15,0,4,1,7,1,0,0,3
2507,sqlite3_sleep,main.c,4018,1.15,3,0,2,8,1,0,1,1
2508,alignmentCollFunc,test1.c,3467,1.15,0,1,5,15,5,0,1,1
2509,sqlite3BackupRestart,backup.c,701,1.15,3,0,2,7,1,0,1,1
2510,invalidateAllOverflowCache,btree.c,569,1.15,3,0,2,7,1,0,1,1
2511,vfslog_flush,test_osinst.c,617,1.15,3,0,2,7,1,0,1,1
2512,aggregateIdxEprRefToColCallback,select.c,6650,1.15,0,0,6,18,2,0,1,1
2513,sqlite3AuthContextPop,auth.c,253,1.15,3,0,2,6,1,0,1,1
2514,autoIncStep,insert.c,518,1.14,3,0,2,5,3,0,1,1
2515,blobHandleFromObj,test_blob.c,51,1.14,0,2,3,25,3,0,1,1
2516,test_bind_int,test1.c,3767,1.14,0,2,3,25,4,0,1,1
2517,test_bind_int64,test1.c,3944,1.14,0,2,3,25,4,0,1,1
2518,findRightmost,select.c,194,1.14,3,0,2,4,1,0,1,1
2519,serialGet,vdbeaux.c,4052,1.14,1,1,3,19,3,0,1,1
2520,test_dbconfig_maindbname_icecube,test1.c,8466,1.14,0,3,2,19,4,0,1,1
2521,closeIncrblobChannels,tclsqlite.c,244,1.14,2,1,2,8,1,0,1,1
2522,test_bind_null,test1.c,4058,1.14,0,2,3,23,4,0,1,1
2523,test_finalize,test1.c,2227,1.14,0,2,3,23,4,0,1,1
2524,testPendingByte,test2.c,562,1.13,0,3,2,18,4,0,1,1
2525,test_blob_write,test_blob.c,269,1.13,0,0,5,31,4,0,1,1
2526,sqlite3FaultSim,util.c,44,1.13,12,0,1,4,1,0,0,1
2527,schemaCreate,test_schema.c,83,1.13,0,3,2,17,6,0,1,1
2528,chacha_block,random.c,39,1.13,1,1,3,16,2,0,1,1
2529,test_reset,test1.c,3057,1.13,0,2,3,21,4,0,1,1
2530,pagerSyncHotJournal,pager.c,4018,1.13,2,0,3,10,1,0,1,1
2531,vfsCurrentTimeInt64,test1.c,2527,1.13,0,3,2,16,4,0,1,1
2532,jsonAppendControlChar,json.c,629,1.13,2,0,2,27,2,0,1,1
2533,winModeBit,os_win.c,3499,1.13,2,0,3,9,3,0,1,1
2534,prng_seed,test1.c,7279,1.13,0,2,3,19,4,0,1,1
2535,vdbeSorterGetCompare,vdbesort.c,1389,1.13,2,0,3,8,1,0,1,1
2536,guess_number_of_cores,test1.c,8763,1.13,0,3,2,14,4,0,1,1
2537,sqlite3BtreeOffset,btree.c,4899,1.13,2,1,1,7,1,0,0,3
2538,sqlite3StringToId,build.c,1760,1.12,2,0,3,7,1,0,1,1
2539,sqlite3InvokeBusyHandler,main.c,1745,1.12,1,1,3,11,1,0,1,1
2540,sqlite3ExprSetErrorOffset,expr.c,900,1.12,2,0,3,5,2,0,1,1
2541,sqlite3RegisterBuiltinFunctions,func.c,2658,1.12,1,4,1,90,0,0,0,2
2542,clang_sanitize_address,test1.c,272,1.12,0,3,2,11,4,0,1,1
2543,sqlite3PagerSetMmapLimit,pager.c,3532,1.12,2,1,1,4,2,0,0,3
2544,init_wrapper_query,test_init.c,246,1.12,0,0,5,24,4,0,1,1
2545,sqlite3PagerSetCachesize,pager.c,3501,1.12,2,1,1,3,2,0,0,3
2546,quota_mbcs_free,test_quota.c,408,1.12,2,1,1,3,1,0,0,3
2547,walIteratorFree,wal.c,1924,1.12,2,1,1,3,1,0,0,3
2548,analyzeWindowKeyword,tokenize.c,246,1.11,1,2,1,8,1,0,0,3
2549,btreeParseCellAdjustSizeForOverflow,btree.c,1178,1.11,2,0,2,20,3,0,1,1
2550,superlockWalLock,test_superlock.c,109,1.11,0,1,4,17,2,0,1,1
2551,whereLoopIsNoBetter,where.c,5637,1.11,1,0,4,10,2,0,1,1
2552,sqlthread_proc,test_thread.c,320,1.10,0,0,4,36,4,0,1,1
2553,demoClose,test_demovfs.c,200,1.10,0,3,1,8,1,0,0,3
2554,sqlite3JournalModename,pragma.c,289,1.10,2,0,2,15,1,0,1,1
2555,Sqlitetest_hexio_Init,test_hexio.c,456,1.10,1,1,2,20,1,0,1,1
2556,DbWalHandler,tclsqlite.c,841,1.10,0,2,2,24,4,0,1,1
2557,winFileSize,os_win.c,3083,1.10,0,2,2,24,2,0,1,1
2558,test_write_db,test1.c,8522,1.10,0,2,2,24,4,0,1,1
2559,kahanBabuskaNeumaierInit,func.c,1799,1.10,2,0,2,13,2,0,1,1
2560,Sqlitetest9_Init,test9.c,184,1.09,1,1,2,17,1,0,1,1
2561,Sqlitetest_blob_Init,test_blob.c,310,1.09,1,1,2,17,1,0,1,1
2562,readSuperJournal,pager.c,1294,1.09,0,0,4,31,3,0,1,1
2563,fetchPayload,btree.c,5347,1.09,1,0,3,21,2,0,1,1
2564,codeTableLocks,build.c,101,1.09,2,0,2,11,1,0,1,1
2565,Sqlitetest8_Init,test8.c,1436,1.09,1,1,2,16,1,0,1,1
2566,Sqlitetest_init_Init,test_init.c,274,1.09,1,1,2,16,1,0,1,1
2567,sqlite3RegisterLikeFunctions,func.c,2247,1.09,1,0,3,20,2,0,1,1
2568,counterMutexAlloc,test_mutex.c,85,1.09,1,0,3,20,1,0,1,1
2569,Sqlitetesttcl_Init,test_bestindex.c,869,1.09,1,1,2,15,1,0,1,1
2570,Sqlitetestfs_Init,test_fs.c,911,1.09,1,1,2,15,1,0,1,1
2571,Sqlitetesttclvar_Init,test_tclvar.c,547,1.09,1,1,2,15,1,0,1,1
2572,testLocaltime,test1.c,7718,1.09,0,0,4,29,2,0,1,1
2573,read_fts3varint,test_hexio.c,368,1.09,0,2,2,20,4,0,1,1
2574,test_stmt_int,test1.c,5777,1.09,0,2,2,20,4,0,1,1
2575,exprCommute,whereexpr.c,116,1.09,1,0,3,19,2,0,1,1
2576,pager_cksum,pager.c,2226,1.09,2,0,2,9,2,0,1,1
2577,Sqlite3_Init,tclsqlite.c,3984,1.09,2,0,2,9,1,0,1,1
2578,sqlite3BtreeLastPage,btree.c,2361,1.09,6,1,1,4,1,0,0,2
2579,sqlite3TokenInit,util.c,390,1.09,6,1,1,4,2,0,0,2
2580,sqlite3VdbeChangeP2,vdbeaux.c,1287,1.09,6,1,1,4,3,0,0,2
2581,test_bind_zeroblob,test1.c,3694,1.09,0,1,3,24,4,0,1,1
2582,sqlite3_intarray_bind,test_intarray.c,263,1.09,1,1,2,14,4,0,1,1
2583,setRawDateNumber,date.c,391,1.08,2,0,2,8,2,0,1,1
2584,sqlite3WalCallback,wal.c,4389,1.08,2,0,2,8,1,0,1,1
2585,hashString,test8.c,651,1.08,2,0,2,8,1,0,1,1
2586,vdbeSafety,vdbeapi.c,40,1.08,2,0,2,8,1,0,1,1
2587,sqlite3PagerPageRefcount,pager.c,6813,1.08,6,1,1,3,1,0,0,2
2588,sqlite3VdbeGetLastOp,vdbeaux.c,1705,1.08,6,1,1,3,1,0,0,2
2589,test_transfer_bind,test1.c,3116,1.08,0,2,2,18,4,0,1,1
2590,pcacheUnpin,pcache.c,265,1.08,2,0,2,7,1,0,1,1
2591,renumberCursorDoMapping,select.c,4078,1.08,2,0,2,7,2,0,1,1
2592,pcache1Init,pcache1.c,699,1.08,0,1,3,22,1,0,1,1
2593,incrblobOutput,tclsqlite.c,342,1.08,0,0,4,26,4,0,1,1
2594,test_blob_bytes,test_blob.c,177,1.08,0,2,2,17,4,0,1,1
2595,uses_stmt_journal,test1.c,3031,1.08,0,2,2,17,4,0,1,1
2596,sqlite3BtreeCursorRestore,btree.c,971,1.08,1,0,3,16,2,0,1,1
2597,sqlite3WalDbsize,wal.c,3638,1.08,2,0,2,6,1,0,1,1
2598,findLeftmostExprlist,select.c,4140,1.08,2,0,2,6,1,0,1,1
2599,test_blob_close,test_blob.c,149,1.08,0,1,3,21,4,0,1,1
2600,test_blob_reopen,test1.c,1795,1.08,0,1,3,21,4,0,1,1
2601,incrblobInput,tclsqlite.c,310,1.08,0,0,4,25,4,0,1,1
2602,test_column_count,test1.c,5486,1.08,0,2,2,16,4,0,1,1
2603,test_data_count,test1.c,5648,1.08,0,2,2,16,4,0,1,1
2604,test_expired,test1.c,3092,1.08,0,2,2,16,4,0,1,1
2605,test_register_cksumvfs,test1.c,8554,1.08,0,2,2,16,4,0,1,1
2606,test_unregister_cksumvfs,test1.c,8575,1.08,0,2,2,16,4,0,1,1
2607,compare2pow63,util.c,765,1.08,1,0,3,15,2,0,1,1
2608,sqlite3_str_value,printf.c,1130,1.08,2,0,2,5,1,0,1,1
2609,echoFindFunction,test8.c,1236,1.08,0,1,3,20,5,0,1,1
2610,databaseIsUnmoved,pager.c,4112,1.07,1,0,3,14,1,0,1,1
2611,indexColumnNotNull,where.c,608,1.07,1,0,3,14,2,0,1,1
2612,sqlite3_result_subtype,vdbeapi.c,536,1.07,11,0,1,7,2,0,0,1
2613,test_hard_heap_limit,test1.c,6301,1.07,0,1,3,19,4,0,1,1
2614,test_soft_heap_limit,test1.c,6274,1.07,0,1,3,19,4,0,1,1
2615,test_printf,test1.c,1349,1.07,0,2,2,14,4,0,1,1
2616,btreePayloadToLocal,btree.c,1213,1.07,1,0,3,13,2,0,1,1
2617,sqlite3WalLimit,wal.c,1737,1.07,2,0,2,3,2,0,1,1
2618,demoAccess,test_demovfs.c,500,1.07,0,1,3,18,4,0,1,1
2619,demoDirectWrite,test_demovfs.c,161,1.07,0,1,3,18,4,0,1,1
2620,tcl_variable_type,test1.c,5955,1.07,0,1,3,18,4,0,1,1
2621,Sqlitetest1_Init,test1.c,8795,1.07,1,0,1,284,1,0,0,1
2622,sqlite3_quota_file_mtime,test_quota.c,1169,1.07,1,1,2,7,2,0,1,1
2623,sqlite3MallocEnd,malloc.c,187,1.07,1,1,2,6,0,0,1,1
2624,sqlite3Win32Wait,os_win.c,1397,1.07,1,1,2,6,1,0,1,1
2625,schemaOpen,test_schema.c,106,1.07,0,2,2,11,2,0,1,1
2626,sqlite3OsUnfetch,os.c,197,1.07,11,0,1,3,3,0,0,1
2627,sqlite3BtreeGetPageSize,btree.c,3094,1.07,11,0,1,3,1,0,0,1
2628,sqlite3Fault,test_malloc.c,43,1.06,1,1,2,5,0,0,1,1
2629,sqlite3FirstFault,test_malloc.c,55,1.06,1,1,2,5,0,0,1,1
2630,DbProgressHandler,tclsqlite.c,683,1.06,0,2,2,10,1,0,1,1
2631,tclScriptEvent,test_thread.c,75,1.06,0,2,2,10,2,0,1,1
2632,sqlite3WhereOrderByLimitOptLabel,where.c,96,1.06,1,0,3,9,1,0,1,1
2633,exprProbability,resolve.c,944,1.06,1,0,3,9,1,0,1,1
2634,sqlite3ExecFunc,test1.c,836,1.06,0,2,1,13,3,0,0,3
2635,sqlite3_transfer_bindings,vdbeapi.c,1957,1.06,0,0,4,16,2,0,1,1
2636,DbRollbackHandler,tclsqlite.c,830,1.06,0,2,2,7,1,0,1,1
2637,sqlite3PagerOkToChangeJournalMode,pager.c,7427,1.06,1,0,3,6,1,0,1,1
2638,sqlite3_mutex_alloc,mutex.c,281,1.06,1,0,3,6,1,0,1,1
2639,multiplexStrlen30,test_multiplex.c,187,1.06,1,0,3,6,1,0,1,1
2640,memIntValue,vdbemem.c,623,1.05,1,1,1,5,1,0,0,3
2641,memRealValue,vdbemem.c,652,1.05,1,1,1,5,1,0,0,3
2642,backupOnePage,backup.c,226,1.05,2,3,2,39,4,0,0,2
2643,exprStructSize,expr.c,1472,1.05,1,0,3,5,1,0,1,1
2644,tempTriggersExist,trigger.c,758,1.05,1,0,3,5,1,0,1,1
2645,test_utf16bin_collate_func,test1.c,3364,1.05,0,1,3,10,5,0,1,1
2646,memdbUnfetch,memdb.c,529,1.05,0,2,1,9,3,0,0,3
2647,sqlite3PagerSetSpillsize,pager.c,3509,1.05,1,1,1,3,2,0,0,3
2648,pcache1Rekey,pcache1.c,1115,1.05,0,0,3,31,4,0,1,1
2649,memdbFileSize,memdb.c,357,1.05,0,2,1,7,2,0,0,3
2650,fsTruncate,test_onefile.c,488,1.04,0,0,4,10,2,0,1,1
2651,blobHandleFromObj,test1.c,1766,1.04,0,1,2,23,3,0,1,1
2652,sqlite3DeferForeignKey,build.c,3705,1.04,0,0,4,9,2,0,1,1
2653,sqlite3PagerCacheStat,pager.c,6849,1.04,1,0,2,16,4,0,1,1
2654,sqlite3_bind_parameter_index,vdbeapi.c,1923,1.04,0,2,1,3,2,0,0,3
2655,testCreateCollationCmp,test1.c,1847,1.04,0,1,2,21,5,0,1,1
2656,exprTableColumn,fkey.c,503,1.04,1,0,2,15,4,0,1,1
2657,overloadedGlobFunction,test8.c,1203,1.03,0,0,3,24,3,0,1,1
2658,test_bind_parameter_index,test1.c,4640,1.03,0,1,2,19,4,0,1,1
2659,test_bind_parameter_name,test1.c,4613,1.03,0,1,2,19,4,0,1,1
2660,test_utf16bin_collate,test1.c,3374,1.03,0,1,2,19,4,0,1,1
2661,delete_collation,test1.c,5878,1.03,0,1,2,18,4,0,1,1
2662,delete_function,test1.c,5852,1.03,0,1,2,18,4,0,1,1
2663,test_bind_value_from_preupdate,test1.c,4263,1.03,0,1,2,18,4,0,1,1
2664,vfs_list,test1.c,7139,1.03,0,1,2,18,4,0,1,1
2665,sqlite3OsCurrentTimeInt64,os.c,290,1.03,1,0,2,11,2,0,1,1
2666,sqlite3PagerDontWrite,pager.c,6250,1.03,1,0,2,11,1,0,1,1
2667,sqlite3ExprListFlags,expr.c,2278,1.03,1,0,2,11,1,0,1,1
2668,sqlite3ValueIsOfClass,vdbemem.c,1390,1.03,1,0,2,11,2,0,1,1
2669,declare_vtab,test8.c,1410,1.02,0,0,3,20,4,0,1,1
2670,indexIteratorFirst,insert.c,1779,1.02,1,0,2,10,2,0,1,1
2671,MD5DigestToBase16,test_md5.c,277,1.02,1,0,2,10,2,0,1,1
2672,sqlite3_vfslog_annotate,test_osinst.c,758,1.02,1,0,2,10,2,0,1,1
2673,sqlite3_vfslog_finalize,test_osinst.c,699,1.02,1,0,2,10,1,0,1,1
2674,extra_schema_checks,test1.c,7307,1.02,0,1,2,15,4,0,1,1
2675,test_bind_parameter_count,test1.c,4589,1.02,0,1,2,15,4,0,1,1
2676,test_clear_bindings,test1.c,4665,1.02,0,1,2,15,4,0,1,1
2677,test_config_sorterref,test1.c,2506,1.02,0,1,2,15,4,0,1,1
2678,test_sql,test1.c,5424,1.02,0,1,2,15,4,0,1,1
2679,randomBlob,func.c,583,1.02,0,0,3,19,3,0,1,1
2680,fsSync,test_onefile.c,502,1.02,0,0,3,18,2,0,1,1
2681,load_testfixture_extensions,test_tclsh.c,180,1.02,0,0,3,18,4,0,1,1
2682,sqlite3PcacheClearWritable,pcache.c,641,1.02,1,0,2,8,1,0,1,1
2683,sqlite3WalSystemErrno,wal.c,2463,1.02,1,0,2,8,1,0,1,1
2684,walFramePage,wal.c,1190,1.02,10,0,1,11,1,0,0,1
2685,sqlite3BitvecTest,bitvec.c,153,1.02,5,1,1,3,2,0,0,2
2686,tmpRead,test_onefile.c,266,1.02,0,1,2,13,4,0,1,1
2687,tclvarSetOmit,test_tclvar.c,320,1.02,0,1,2,13,1,0,1,1
2688,sqlite3_value_pointer,vdbeapi.c,214,1.02,0,1,2,13,2,0,1,1
2689,sqlite3PagerMaxPageCount,pager.c,3817,1.02,1,0,2,7,2,0,1,1
2690,sqlite3PcacheClearSyncFlags,pcache.c,653,1.02,1,0,2,7,1,0,1,1
2691,sqlite3ErrorToParser,util.c,273,1.02,1,0,2,7,2,0,1,1
2692,sqlite3KeyInfoRef,select.c,1548,1.02,1,0,2,7,1,0,1,1
2693,sqlite3MutexEnd,mutex.c,265,1.02,1,0,2,7,0,0,1,1
2694,faultsimPending,test_malloc.c,158,1.02,1,0,2,7,0,0,1,1
2695,getDbPointer,test_mutex.c,374,1.02,0,1,2,12,2,0,1,1
2696,analyzeFilterKeyword,tokenize.c,261,1.01,1,0,2,6,2,0,1,1
2697,winTempDirDefined,os_win.c,4743,1.01,1,0,2,6,0,0,1,1
2698,sqlite3DbIsNamed,attach.c,52,1.01,4,2,1,6,3,0,0,2
2699,sqlite3PagerWalSupported,pager.c,7516,1.01,1,0,2,5,1,0,1,1
2700,sqlite3PcacheShutdown,pcache.c,309,1.01,1,0,2,5,0,0,1,1
2701,jsonAllAlphanum,json.c,3801,1.01,1,0,2,5,2,0,1,1
2702,sqlite3_data_count,vdbeapi.c,1258,1.01,1,0,2,5,1,0,1,1
2703,sqlite3MemMalloc,mem1.c,128,1.01,0,1,2,10,1,0,1,1
2704,sqlite3MemRealloc,mem1.c,200,1.01,0,1,2,10,2,0,1,1
2705,fsdirColumn,test_fs.c,290,1.01,0,0,3,14,3,0,1,1
2706,hasAnchor,select.c,2848,1.01,1,0,2,4,1,0,1,1
2707,DbCommitHandler,tclsqlite.c,819,1.01,0,1,2,9,1,0,1,1
2708,multiplexSubSize,test_multiplex.c,357,1.01,0,0,3,13,3,0,1,1
2709,winSetSystemCall,os_win.c,1166,1.00,0,0,2,30,3,0,1,1
2710,wrMemInit,test_init.c,45,1.00,0,0,3,12,1,0,1,1
2711,wrMutexInit,test_init.c,68,1.00,0,0,3,12,0,0,1,1
2712,wrPCacheInit,test_init.c,109,1.00,0,0,3,12,1,0,1,1
2713,counterMutexFree,test_mutex.c,113,1.00,0,1,2,7,1,0,1,1
2714,sqlite3PagerGetData,pager.c,7272,1.00,10,0,1,4,1,0,0,1
2715,sqlite3MallocSize,malloc.c,365,1.00,10,0,1,4,1,0,0,1
2716,sqlite3SectorSize,pager.c,2677,1.00,0,0,3,10,1,0,1,1
2717,superlockBusyHandler,test_superlock.c,52,1.00,0,1,2,5,2,0,1,1
2718,echoClose,test8.c,566,1.00,0,1,1,9,1,0,0,3
2719,destructor,test_func.c,88,0.99,0,1,1,7,1,0,0,3
2720,jsonHexToInt4,json.c,894,0.99,2,4,1,8,1,0,0,2
2721,moduleDestroy,test8.c,1358,0.99,0,1,1,6,1,0,0,3
2722,pragmaVtabDisconnect,pragma.c,2844,0.99,0,1,1,5,1,0,0,3
2723,intarrayClose,test_intarray.c,122,0.99,0,1,1,5,1,0,0,3
2724,intarrayDestroy,test_intarray.c,75,0.99,0,1,1,5,1,0,0,3
2725,tmpClose,test_onefile.c,257,0.99,0,1,1,5,1,0,0,3
2726,tclvarDisconnect,test_tclvar.c,101,0.99,0,1,1,4,1,0,0,3
2727,fsDisconnect,test_fs.c,648,0.99,0,1,1,4,1,0,0,3
2728,fsdirDisconnect,test_fs.c,168,0.99,0,1,1,4,1,0,0,3
2729,fstreeDisconnect,test_fs.c,375,0.99,0,1,1,4,1,0,0,3
2730,getWin32Handle,test1.c,128,0.99,0,1,1,4,3,0,0,3
2731,test_read_mutex_counters,test_mutex.c,257,0.99,0,0,2,22,4,0,1,1
2732,sqlite3VdbeValueListFree,vdbeapi.c,1006,0.98,0,1,1,3,1,0,0,3
2733,sqlite3VdbeSetColName,vdbeaux.c,2879,0.98,0,0,2,21,5,0,1,1
2734,testBestIndexObjOrderby,test_bestindex.c,471,0.98,0,0,2,21,2,0,1,1
2735,free_test_auxdata,test_func.c,184,0.98,0,1,1,1,1,0,0,3
2736,sqlite3PcacheFetchFinish,pcache.c,528,0.97,0,0,2,16,3,0,1,1
2737,sqlite3StatusHighwater,status.c,114,0.97,0,0,2,16,2,0,1,1
2738,install_fts3_rank_function,test_func.c,909,0.97,0,0,2,16,4,0,1,1
2739,test_clear_mutex_counters,test_mutex.c,286,0.97,0,0,2,16,4,0,1,1
2740,add_alignment_test_collations,test1.c,3482,0.97,0,0,2,16,4,0,1,1
2741,init_wrapper_clear,test_init.c,229,0.97,0,0,2,15,4,0,1,1
2742,multiplexShmMap,test_multiplex.c,1081,0.97,0,0,2,15,5,0,1,1
2743,sqlite3_bind_text64,vdbeapi.c,1800,0.97,0,0,2,15,6,0,1,1
2744,multiplexShmLock,test_multiplex.c,1099,0.97,0,0,2,14,4,0,1,1
2745,randomFunc,func.c,557,0.96,0,0,2,13,3,0,1,1
2746,superlockShmLock,test_superlock.c,91,0.96,0,0,2,13,4,0,1,1
2747,winNextSystemCall,os_win.c,1228,0.96,0,0,2,13,2,0,1,1
2748,sqlite3ExprTruthValue,expr.c,2340,0.96,4,1,1,8,1,0,0,2
2749,test_frombind,test_func.c,636,0.96,0,0,2,12,3,0,1,1
2750,vfslogClose,test_osinst.c,256,0.96,0,0,2,12,1,0,1,1
2751,sqlite3StatusUp,status.c,89,0.96,0,0,2,11,2,0,1,1
2752,sqlite3_database_file_object,pager.c,5057,0.96,0,0,2,11,1,0,1,1
2753,getFts3Varint,test_hexio.c,338,0.96,0,0,2,11,2,0,1,1
2754,putFts3Varint,test_hexio.c,350,0.96,0,0,2,11,2,0,1,1
2755,sqlite3StmtCurrentTime,vdbeapi.c,1088,0.96,0,0,2,10,1,0,1,1
2756,sqlite3VtabArgExtend,vtab.c,540,0.96,0,0,2,10,2,0,1,1
2757,cellSizePtrNoPayload,btree.c,1487,0.96,0,0,2,10,2,0,1,1
2758,exprIdxCover,expr.c,6751,0.96,0,0,2,10,2,0,1,1
2759,sqlite3_status,status.c,159,0.96,0,0,2,10,4,0,1,1
2760,fsFileSize,test_onefile.c,526,0.96,0,0,2,10,2,0,1,1
2761,sqlite3ReturningSubqueryVarSelect,trigger.c,963,0.96,0,0,2,10,2,0,1,1
2762,exprSelectWalkTableConstant,expr.c,2582,0.96,0,0,2,9,2,0,1,1
2763,multiplexCheckReservedLock,test_multiplex.c,906,0.96,0,0,2,9,2,0,1,1
2764,multiplexDeviceCharacteristics,test_multiplex.c,1069,0.96,0,0,2,9,1,0,1,1
2765,multiplexLock,test_multiplex.c,882,0.96,0,0,2,9,2,0,1,1
2766,multiplexSectorSize,test_multiplex.c,1057,0.96,0,0,2,9,1,0,1,1
2767,multiplexShmUnmap,test_multiplex.c,1127,0.96,0,0,2,9,2,0,1,1
2768,multiplexUnlock,test_multiplex.c,894,0.96,0,0,2,9,2,0,1,1
2769,winCurrentTime,os_win.c,6016,0.96,0,0,2,9,2,0,1,1
2770,vdbeSorterTreeDepth,vdbesort.c,2348,0.96,0,0,2,9,1,0,1,1
2771,sqlite3BtreeGetFilename,btree.c,11209,0.96,4,1,1,4,1,0,0,2
2772,sqlite3VdbeChangeP3,vdbeaux.c,1291,0.96,4,1,1,4,3,0,0,2
2773,sqlite3_mutex_try,mutex.c,324,0.95,0,0,2,8,1,0,1,1
2774,backupTruncateFile,backup.c,289,0.95,0,0,2,8,2,0,1,1
2775,sqlite3MutexAlloc,mutex.c,290,0.95,0,0,2,8,1,0,1,1
2776,selectRefLeave,expr.c,6827,0.95,0,0,2,8,2,0,1,1
2777,jsonEachOpenTree,json.c,4873,0.95,0,0,2,8,2,0,1,1
2778,multiplexShmBarrier,test_multiplex.c,1116,0.95,0,0,2,8,1,0,1,1
2779,sqlite3WindowExtraAggFuncDepth,window.c,933,0.95,0,0,2,8,2,0,1,1
2780,winShmLeaveMutex,os_win.c,3699,0.95,4,1,1,3,0,0,0,2
2781,checkReadTransaction,backup.c,124,0.95,0,0,2,7,2,0,1,1
2782,sqlite3ResultIntReal,vdbeapi.c,709,0.95,0,0,2,7,1,0,1,1
2783,exprColumnFlagUnion,insert.c,266,0.95,0,0,2,7,2,0,1,1
2784,sqlite3_db_name,main.c,4847,0.95,0,0,2,7,2,0,1,1
2785,multiplexGetLastError,test_multiplex.c,683,0.95,0,0,2,7,3,0,1,1
2786,counterMutexInit,test_mutex.c,66,0.95,0,0,2,7,0,0,1,1
2787,exprNodeIsDeterministic,where.c,6262,0.95,0,0,2,7,2,0,1,1
2788,sqlite3_column_count,vdbeapi.c,1248,0.95,0,0,2,5,1,0,1,1
2789,sqlite3OsDeviceCharacteristics,os.c,165,0.94,0,0,2,4,1,0,1,1
2790,incrAggDepth,resolve.c,35,0.94,0,0,2,4,2,0,1,1
2791,sqlite3HashInit,hash.c,23,0.94,9,0,1,7,1,0,0,1
2792,sqlite3OsFileControlHint,os.c,157,0.94,0,0,2,3,3,0,1,1
2793,sqlite3_result_error_toobig,vdbeapi.c,683,0.94,9,0,1,6,1,0,0,1
2794,jsonIs4Hex,json.c,913,0.94,3,2,1,3,1,0,0,2
2795,clearYMD_HMS_TZ,date.c,514,0.94,9,0,1,5,1,0,0,1
2796,sqlite3_value_text16,vdbeapi.c,231,0.93,9,0,1,3,1,0,0,1
2797,sqlite3_trace,main.c,2191,0.93,2,2,2,10,3,0,0,2
2798,estLog,where.c,695,0.93,3,1,2,3,1,0,0,2
2799,sqlite3MultiWrite,build.c,5367,0.91,8,0,2,4,1,0,0,1
2800,sqlite3_commit_hook,main.c,2272,0.90,2,2,1,13,3,0,0,2
2801,sqlite3_wal_hook,main.c,2452,0.90,2,2,1,13,3,0,0,2
2802,winShmEnterMutex,os_win.c,3696,0.89,3,1,1,3,0,0,0,2
2803,sqlite3_result_text,vdbeapi.c,558,0.88,8,0,1,9,4,0,0,1
2804,sqlite3ChangeCookie,build.c,2021,0.88,8,0,1,7,2,0,0,1
2805,schemaFilter,test_schema.c,243,0.88,0,4,1,15,5,0,0,2
2806,sqlite3HexToInt,util.c,1483,0.87,8,0,1,5,1,0,0,1
2807,markTermAsChild,whereexpr.c,500,0.87,8,0,1,5,3,0,0,1
2808,sqlite3IsNaN,util.c,57,0.87,7,1,1,8,1,0,0,1
2809,sqlite3_extended_result_codes,main.c,4034,0.86,1,2,2,6,2,0,0,2
2810,sqlite3_win32_sleep,os_win.c,1382,0.86,7,1,1,3,1,0,0,1
2811,sqlite3PcacheOpen,pcache.c,333,0.85,1,2,1,21,6,0,0,2
2812,unlockBtreeMutex,btmutex.c,41,0.84,2,1,1,9,1,0,0,2
2813,lockBtreeMutex,btmutex.c,27,0.83,2,1,1,8,1,0,0,2
2814,sqlite3_collation_needed,main.c,3710,0.83,1,2,1,12,3,0,0,2
2815,sqlite3_collation_needed16,main.c,3731,0.83,1,2,1,12,3,0,0,2
2816,sqlite3VdbeChangeP1,vdbeaux.c,1283,0.82,2,1,1,4,3,0,0,2
2817,addToVTrans,vtab.c,755,0.82,2,1,1,4,2,0,0,2
2818,sqlite3_vfslog_register,test_osinst.c,1071,0.82,1,1,1,31,1,0,0,2
2819,sqlite3BtreePayloadFetch,btree.c,5386,0.82,2,1,1,3,2,0,0,2
2820,sqlite3_errstr,main.c,2747,0.82,2,1,1,3,1,0,0,2
2821,measureAllocationSize,malloc.c,431,0.82,2,1,1,3,2,0,0,2
2822,sqlite3PagerMemUsed,pager.c,6802,0.82,1,2,1,7,1,0,0,2
2823,sqlite3_create_module,vtab.c,108,0.81,7,0,1,8,4,0,0,1
2824,integrityCheckResultRow,pragma.c,385,0.81,7,0,1,8,1,0,0,1
2825,Sqlitetest_autoext_Init,test_autoext.c,203,0.81,1,7,1,17,1,0,0,1
2826,btreeGetHasContent,btree.c,673,0.81,1,2,1,4,2,0,0,2
2827,test_libversion_number,test1.c,1701,0.81,0,3,1,9,4,0,0,2
2828,put32bits,test_osinst.c,645,0.81,7,0,1,6,2,0,0,1
2829,sqlite3DequoteExpr,util.c,320,0.81,1,1,2,6,1,0,0,2
2830,set_options,test_config.c,43,0.80,1,0,1,171,1,0,0,1
2831,sqlite3MemoryBarrier,mutex_w32.c,85,0.79,6,1,1,3,0,0,0,1
2832,MD5Transform,test_md5.c,87,0.78,4,0,1,75,2,0,0,1
2833,addAggInfoColumn,expr.c,6976,0.77,1,1,1,11,2,0,0,2
2834,addAggInfoFunc,expr.c,6992,0.77,1,1,1,11,2,0,0,2
2835,vdbeMemTypeName,vdbe.c,799,0.77,1,1,1,10,1,0,0,2
2836,attachBackupObject,backup.c,302,0.77,1,1,1,8,1,0,0,2
2837,pagerReleaseMapPage,pager.c,4085,0.77,1,1,1,8,1,0,0,2
2838,sqlite3_rollback_hook,main.c,2322,0.77,0,2,1,13,3,0,0,2
2839,sqlite3_update_hook,main.c,2297,0.77,0,2,1,13,3,0,0,2
2840,sqlite3PcacheSetCachesize,pcache.c,863,0.76,1,1,1,6,2,0,0,2
2841,sqlite3BtreeGetJournalname,btree.c,11222,0.76,1,1,1,4,1,0,0,2
2842,Sqliteconfig_Init,test_config.c,836,0.76,1,1,1,4,1,0,0,2
2843,sqlite3PagerRef,pager.c,4215,0.76,1,1,1,3,1,0,0,2
2844,sqlite3PagerShrink,pager.c,3540,0.76,1,1,1,3,1,0,0,2
2845,sqlite3PagerWalCallback,pager.c,7508,0.76,1,1,1,3,1,0,0,2
2846,sqlite3PagerWalSystemErrno,pager.c,7790,0.76,1,1,1,3,1,0,0,2
2847,sqlite3MemJournalOpen,memjournal.c,391,0.76,1,1,1,3,1,0,0,2
2848,sqlite3_set_last_insert_rowid,main.c,1105,0.75,0,2,1,5,2,0,0,2
2849,sqlite3VdbeMemInit,vdbemem.c,861,0.74,6,0,1,6,3,0,0,1
2850,sqlite3VdbeEndCoroutine,vdbeaux.c,574,0.74,6,0,1,5,2,0,0,1
2851,isLimitTerm,where.c,4186,0.74,6,0,1,5,1,0,0,1
2852,jsonHexToInt,json.c,881,0.74,6,0,1,4,1,0,0,1
2853,sqlite3IsIdChar,tokenize.c,190,0.73,6,0,1,1,1,0,0,1
2854,typeofFunc,func.c,79,0.72,0,1,1,16,3,0,0,2
2855,btreeParseCellPtrNoPayload,btree.c,1241,0.72,0,1,1,15,3,0,0,2
2856,appendText,main.c,4687,0.72,4,2,1,5,2,0,0,1
2857,echoConnect,test8.c,509,0.71,0,1,1,10,6,0,0,2
2858,dbPrepare,tclsqlite.c,1338,0.71,0,1,1,10,4,0,0,2
2859,test_thread_cleanup,test1.c,6326,0.70,0,1,1,9,4,0,0,2
2860,pcache1PinPage,pcache1.c,580,0.70,5,0,1,14,1,0,0,1
2861,btreeInvokeBusyHandler,btree.c,2494,0.70,0,1,1,6,1,0,0,2
2862,xBusy,test_thread.c,255,0.70,0,1,1,6,2,0,0,2
2863,write32bits,pager.c,1113,0.70,0,1,1,5,3,0,0,2
2864,winSleep,os_win.c,5948,0.70,0,1,1,5,2,0,0,2
2865,sqlite3VdbeChangeOpcode,vdbeaux.c,1279,0.69,0,1,1,4,3,0,0,2
2866,sqlite3TestExtInit,main.c,34,0.69,0,1,1,4,1,0,0,2
2867,echoDisconnect,test8.c,523,0.69,0,1,1,4,1,0,0,2
2868,fsEof,test_fs.c,764,0.69,0,1,1,4,1,0,0,2
2869,sqlite3_vtab_nochange,vdbeapi.c,990,0.69,0,1,1,4,1,0,0,2
2870,sqlite3WritableSchema,build.c,993,0.69,5,0,1,10,1,0,0,1
2871,whereLoopAddOr,where.c,4680,0.68,1,1,1,96,3,0,0,1
2872,sqlite3_open,main.c,3583,0.68,5,0,1,7,2,0,0,1
2873,sqlite3CantopenError,main.c,3861,0.67,5,0,1,4,1,0,0,1
2874,sqlite3ClearTempRegCache,expr.c,7321,0.67,5,0,1,4,1,0,0,1
2875,sqlite3IsBinary,main.c,1061,0.67,5,0,1,4,1,0,0,1
2876,sqlite3ExprCodeMove,expr.c,4369,0.67,5,0,1,3,4,0,0,1
2877,sqlite3IsOverflow,util.c,75,0.67,4,1,1,7,1,0,0,1
2878,DbUseNre,tclsqlite.c,1853,0.66,4,1,1,5,0,0,0,1
2879,windowArgCount,window.c,1529,0.66,4,0,2,6,1,0,0,1
2880,sqlite3FixInit,attach.c,517,0.65,4,0,1,23,5,0,0,1
2881,sqlite3_msize,malloc.c,403,0.65,4,0,2,5,1,0,0,1
2882,sqlite3_value_subtype,vdbeapi.c,210,0.65,4,0,2,4,1,0,0,1
2883,columnNullValue,vdbeapi.c,1267,0.64,4,0,1,17,0,0,0,1
2884,sqlite3_prepare_v2,prepare.c,934,0.63,4,0,1,13,5,0,0,1
2885,jsonStringZero,json.c,476,0.61,4,0,1,6,1,0,0,1
2886,sqlite3PagerPagecount,pager.c,3895,0.61,4,0,1,5,2,0,0,1
2887,sqlite3VdbeSetChanges,vdbeaux.c,5282,0.61,4,0,1,5,2,0,0,1
2888,getPageReferenced,btree.c,10558,0.61,4,0,1,5,2,0,0,1
2889,walHash,wal.c,1125,0.61,4,0,1,5,1,0,0,1
2890,sqlite3PagerGetExtra,pager.c,7281,0.61,4,0,1,3,1,0,0,1
2891,sqlite3VtabLock,vtab.c,182,0.61,4,0,1,3,1,0,0,1
2892,sqlite3WhereIsDistinct,where.c,57,0.61,4,0,1,3,1,0,0,1
2893,sqlite3_value_bytes16,vdbeapi.c,198,0.61,4,0,1,3,1,0,0,1
2894,get32bits,test_osinst.c,830,0.61,4,0,1,3,1,0,0,1
2895,walNextHash,wal.c,1130,0.61,4,0,1,3,1,0,0,1
2896,sqlite3Hwtime,hwtime.h,81,0.60,4,0,1,1,0,0,0,1
2897,finalize,test_schema.c,158,0.60,3,1,1,5,1,0,0,1
2898,sqlite3WhereOkOnePass,where.c,168,0.60,3,1,1,4,2,0,0,1
2899,btreeParseCell,btree.c,1381,0.59,2,2,1,7,3,0,0,1
2900,postToParent,test_thread.c,90,0.58,0,4,1,14,2,0,0,1
2901,schemaClose,test_schema.c,121,0.57,0,4,1,8,1,0,0,1
2902,sqlite3WhereClauseInit,whereexpr.c,1707,0.56,3,0,1,12,2,0,0,1
2903,sqlite3AuthContextPush,auth.c,238,0.56,3,0,1,10,3,0,0,1
2904,sqlite3_create_module_v2,vtab.c,123,0.55,3,0,1,9,5,0,0,1
2905,MD5Init,test_md5.c,173,0.55,3,0,1,9,1,0,0,1
2906,sqlite3GetFuncCollSeq,func.c,27,0.55,3,0,1,8,1,0,0,1
2907,getStmtPointer,test1.c,165,0.55,3,0,1,8,3,0,0,1
2908,sqlite3RCStrRef,printf.c,1390,0.55,3,0,1,7,1,0,0,1
2909,sqlite3BtreeCursorHasMoved,btree.c,939,0.55,3,0,1,7,1,0,0,1
2910,assignAggregateRegisters,select.c,6702,0.55,3,0,1,6,2,0,0,1
2911,logBadConnection,util.c,1522,0.55,3,0,1,6,1,0,0,1
2912,sqlite3OsDelete,os.c,233,0.54,3,0,1,5,3,0,0,1
2913,sqlite3PagerTruncateImage,pager.c,3987,0.54,3,0,1,5,2,0,0,1
2914,sqlite3BtreeFakeValidCursor,btree.c,952,0.54,3,0,1,5,0,0,0,1
2915,winIsDriveLetterAndColon,os_win.c,5554,0.54,3,0,1,5,1,0,0,1
2916,daysAfterMonday,date.c,1349,0.54,3,0,1,4,1,0,0,1
2917,sqlite3VtabCommit,vtab.c,1028,0.54,3,0,1,4,1,0,0,1
2918,sqlite3VtabRollback,vtab.c,1019,0.54,3,0,1,4,1,0,0,1
2919,sqlite3BtreeConnectionCount,btree.c,11487,0.54,3,0,1,4,1,0,0,1
2920,vfslog_time,test_osinst.c,231,0.54,2,1,1,9,0,0,0,1
2921,sqlite3PagerGetJournalMode,pager.c,7418,0.54,3,0,1,3,1,0,0,1
2922,isFatalError,backup.c,217,0.54,3,0,1,3,1,0,0,1
2923,sqlite3TempInMemory,main.c,2616,0.54,3,0,1,3,1,0,0,1
2924,sqlite3WhereUsesDeferredSeek,where.c,184,0.54,3,0,1,3,1,0,0,1
2925,sqlite3BtreeCursorSize,btree.c,4745,0.54,3,0,1,3,0,0,0,1
2926,sqlite3BtreeIsReadonly,btree.c,11454,0.54,3,0,1,3,1,0,0,1
2927,sqlite3BtreeSharable,btree.c,11478,0.54,3,0,1,3,1,0,0,1
2928,sqlite3VdbeCountChanges,vdbeaux.c,5292,0.54,3,0,1,3,1,0,0,1
2929,sqlite3_last_insert_rowid,main.c,1092,0.54,3,0,1,3,1,0,0,1
2930,jsonIs2Hex,json.c,906,0.54,3,0,1,3,1,0,0,1
2931,isLookaside,malloc.c,354,0.54,3,0,1,3,2,0,0,1
2932,sqlite3AggInfoPersistWalkerInit,expr.c,6965,0.53,2,1,1,6,2,0,0,1
2933,datetimeError,date.c,245,0.53,2,1,1,4,1,0,0,1
2934,Sqlitetest2_Init,test2.c,701,0.52,1,0,1,52,1,0,0,1
2935,sqlite3Checkpoint,main.c,2570,0.52,2,0,1,22,5,0,0,1
2936,sqlite3_demovfs,test_demovfs.c,620,0.52,2,0,1,22,0,0,0,1
2937,Sqlitetestrtree_Init,test_rtree.c,499,0.52,1,2,1,5,1,0,0,1
2938,sqlite3_sql,vdbeapi.c,2104,0.52,2,0,2,4,1,0,0,1
2939,Sqlitetest3_Init,test3.c,659,0.52,1,1,1,27,1,0,0,1
2940,autoExtBrokenObjCmd,test_autoext.c,154,0.52,0,3,1,10,4,0,0,1
2941,autoExtCubeObjCmd,test_autoext.c,122,0.52,0,3,1,10,4,0,0,1
2942,autoExtSqrObjCmd,test_autoext.c,90,0.52,0,3,1,10,4,0,0,1
2943,cancelAutoExtBrokenObjCmd,test_autoext.c,170,0.52,0,3,1,10,4,0,0,1
2944,cancelAutoExtCubeObjCmd,test_autoext.c,138,0.52,0,3,1,10,4,0,0,1
2945,cancelAutoExtSqrObjCmd,test_autoext.c,106,0.52,0,3,1,10,4,0,0,1
2946,sqlite3RegisterJsonFunctions,json.c,5386,0.52,1,0,1,49,0,0,0,1
2947,sqlite3WhereIsOrdered,where.c,69,0.52,2,0,2,3,1,0,0,1
2948,sqlite3WindowFunctions,window.c,610,0.51,2,0,1,20,0,0,0,1
2949,testpcachePagecount,test_pcache.c,180,0.51,0,3,1,7,1,0,0,1
2950,sqlite3_quota_file_truesize,test_quota.c,1187,0.51,1,1,2,6,1,0,0,1
2951,sqlite3PCacheSetDefault,pcache1.c,1196,0.51,2,0,1,18,0,0,0,1
2952,testpcacheCachesize,test_pcache.c,169,0.51,0,3,1,6,2,0,0,1
2953,sqlite3VtabSync,vtab.c,997,0.51,2,0,1,16,2,0,0,1
2954,sqlite3VdbeRewind,vdbeaux.c,2584,0.51,2,0,1,16,1,0,0,1
2955,sqlite3NoopMutex,mutex_noop.c,52,0.50,2,0,1,14,0,0,0,1
2956,sqlite3MemSetDefault,mem1.c,277,0.50,2,0,1,13,0,0,0,1
2957,sqlite3_prepare,prepare.c,922,0.50,2,0,1,12,5,0,0,1
2958,pcacheFetchFinishWithInit,pcache.c,501,0.49,0,2,1,20,3,0,0,1
2959,sqlite3WalSavepoint,wal.c,3777,0.48,2,0,1,7,2,0,0,1
2960,tclSqlCollate,tclsqlite.c,977,0.48,0,2,1,17,5,0,0,1
2961,sqlite3DeleteIndexSamples,analyze.c,1665,0.48,2,0,1,6,2,0,0,1
2962,whereLoopInit,where.c,2506,0.48,2,0,1,6,1,0,0,1
2963,sqlite3WalReadFrame,wal.c,3618,0.48,1,0,2,15,4,0,0,1
2964,setPageReferenced,btree.c,10567,0.48,2,0,1,5,2,0,0,1
2965,sqlite3SkipAccumulatorLoad,func.c,40,0.48,2,0,1,5,1,0,0,1
2966,jrnlBufferSize,pager.c,1189,0.48,2,0,1,5,1,0,0,1
2967,sqlite3OsFetch,os.c,193,0.48,2,0,1,4,4,0,0,1
2968,sqlite3PcachePagecount,pcache.c,846,0.48,2,0,1,4,1,0,0,1
2969,daysAfterSunday,date.c,1362,0.48,2,0,1,4,1,0,0,1
2970,sqlite3CompileOptions,ctime.c,791,0.48,2,0,1,4,1,0,0,1
2971,sqlite3WhereContinueLabel,where.c,138,0.48,2,0,1,4,1,0,0,1
2972,sqlite3BtreeCursorIsValidNN,btree.c,4864,0.48,2,0,1,4,1,0,0,1
2973,vdbeIncrMergerSetThreads,vdbesort.c,2020,0.48,2,0,1,4,1,0,0,1
2974,working_64bit_int,test1.c,6390,0.47,0,2,1,14,4,0,0,1
2975,sqlite3BitvecSize,bitvec.c,292,0.47,2,0,1,3,1,0,0,1
2976,sqlite3OsSleep,os.c,284,0.47,2,0,1,3,2,0,0,1
2977,sqlite3PagerDataVersion,pager.c,1776,0.47,2,0,1,3,1,0,0,1
2978,sqlite3PagerIsreadonly,pager.c,6785,0.47,2,0,1,3,1,0,0,1
2979,sqlite3PagerTempSpace,pager.c,3806,0.47,2,0,1,3,1,0,0,1
2980,sqlite3PcachePageRefcount,pcache.c,839,0.47,2,0,1,3,1,0,0,1
2981,sqlite3PcacheRefCount,pcache.c,832,0.47,2,0,1,3,1,0,0,1
2982,sqlite3CommitInternalChanges,build.c,658,0.47,2,0,1,3,1,0,0,1
2983,sqlite3HeapNearlyFull,malloc.c,180,0.47,2,0,1,3,0,0,0,1
2984,sqlite3WhereOutputRowCount,where.c,49,0.47,2,0,1,3,1,0,0,1
2985,sqlite3BtreeEof,btree.c,6197,0.47,2,0,1,3,1,0,0,1
2986,sqlite3_db_mutex,main.c,870,0.47,2,0,1,3,1,0,0,1
2987,sqlite3_interrupt,main.c,1832,0.47,2,0,1,3,1,0,0,1
2988,sqlite3_wal_checkpoint,main.c,2543,0.47,2,0,1,3,2,0,0,1
2989,sqlite3WalHeapMemory,wal.c,4466,0.47,2,0,1,3,1,0,0,1
2990,SqlitetestSyscall_Init,test_syscall.c,758,0.47,2,0,1,3,1,0,0,1
2991,addDatabaseRef,tclsqlite.c,586,0.47,2,0,1,3,1,0,0,1
2992,sqllog_isspace,test_sqllog.c,140,0.47,2,0,1,3,1,0,0,1
2993,sqlite3_win32_is_nt,os_win.c,1431,0.47,2,0,1,3,0,0,0,1
2994,walPagesize,wal.c,2119,0.47,2,0,1,3,1,0,0,1
2995,winDlSym,os_win.c,5846,0.47,1,1,1,8,3,0,0,1
2996,sqlite3_quota_fread,test_quota.c,985,0.47,1,1,1,8,4,0,0,1
2997,sqlite3PrngRestoreState,random.c,150,0.47,1,1,1,7,0,0,0,1
2998,sqlite3PrngSaveState,random.c,143,0.47,1,1,1,7,0,0,0,1
2999,sqlite3_sourceid,main.c,109,0.47,2,0,1,1,0,0,0,1
3000,sqlite3_libversion_number,main.c,114,0.47,2,0,1,1,0,0,0,1
3001,Sqlitetest4_Init,test4.c,736,0.47,2,0,1,1,1,0,0,1
3002,sqlite3RealSameAsInt,vdbemem.c,756,0.47,1,1,1,6,2,0,0,1
3003,computeCellSize,btree.c,7539,0.47,1,1,1,6,2,0,0,1
3004,sqlite_abort,test1.c,2139,0.47,0,2,1,11,4,0,0,1
3005,winDlClose,os_win.c,5854,0.47,1,1,1,5,2,0,0,1
3006,Sqlitetest_mutex_Init,test_mutex.c,475,0.47,1,0,1,27,1,0,0,1
3007,sqlite3ThreadProc,threads.c,123,0.47,0,2,1,10,1,0,0,1
3008,sqlite3MemSize,mem1.c,177,0.46,1,1,1,4,1,0,0,1
3009,Sqlitetestbackup_Init,test_backup.c,147,0.46,1,1,1,4,1,0,0,1
3010,sqlite3BtreeCursorZero,btree.c,4776,0.46,1,1,1,3,1,0,0,1
3011,getProcessId,test_sqllog.c,86,0.46,1,1,1,3,0,0,0,1
3012,sqlite3_quota_ferror,test_quota.c,1120,0.46,1,1,1,3,1,0,0,1
3013,sqlite3_quota_fseek,test_quota.c,1099,0.46,1,1,1,3,3,0,0,1
3014,sqlite3_quota_ftell,test_quota.c,1113,0.46,1,1,1,3,1,0,0,1
3015,sqlite3_quota_rewind,test_quota.c,1106,0.46,1,1,1,3,1,0,0,1
3016,cubeFunc,test_autoext.c,48,0.46,0,2,1,8,3,0,0,1
3017,sqrFunc,test_autoext.c,23,0.46,0,2,1,8,3,0,0,1
3018,halfFunc,test_loadext.c,21,0.46,0,2,1,7,3,0,0,1
3019,demoSleep,test_demovfs.c,591,0.45,0,2,1,5,2,0,0,1
3020,sqlite3_blob_bytes,vdbeblob.c,474,0.45,1,0,2,4,1,0,0,1
3021,sqlite3_system_errno,main.c,2738,0.45,1,0,2,3,1,0,0,1
3022,lookasideMallocSize,malloc.c,369,0.45,1,0,2,3,2,0,0,1
3023,sqlite3_db_handle,vdbeapi.c,1981,0.45,1,0,2,3,1,0,0,1
3024,sqlite3_quota_file_size,test_quota.c,1203,0.45,1,0,2,3,1,0,0,1
3025,DbUpdateHandler,tclsqlite.c,931,0.45,0,1,1,24,5,0,0,1
3026,test_pcache_stats,test1.c,7358,0.45,0,1,1,24,4,0,0,1
3027,blobStringFromObj,test_blob.c,86,0.45,0,1,2,6,1,0,0,1
3028,vfslogOpen,test_osinst.c,481,0.44,0,1,1,22,5,0,0,1
3029,sqlite3OsSectorSize,os.c,161,0.44,0,1,2,4,1,0,0,1
3030,Sqlitetest5_Init,test5.c,201,0.44,1,0,1,16,1,0,0,1
3031,sqlite3VdbeSwap,vdbeaux.c,127,0.44,0,1,1,21,2,0,0,1
3032,sqlite3RegisterDateTimeFunctions,date.c,1795,0.44,1,0,1,15,0,0,0,1
3033,sqlite3_prepare_v3,prepare.c,952,0.44,1,0,1,15,6,0,0,1
3034,registerTestFunctions,test_func.c,662,0.44,0,0,1,42,3,0,0,1
3035,winFullPathname,os_win.c,5781,0.43,1,0,1,14,4,0,0,1
3036,sqlite3PagerSetBusyHandler,pager.c,3693,0.43,1,0,1,13,3,0,0,1
3037,Md5_Init,test_md5.c,393,0.43,1,0,1,11,1,0,0,1
3038,sqlite3AlterFunctions,alter.c,2306,0.43,1,0,1,10,0,0,0,1
3039,faultsimConfig,test_malloc.c,119,0.43,1,0,1,10,2,0,0,1
3040,vfslogAccess,test_osinst.c,527,0.42,0,1,1,15,4,0,0,1
3041,sqlite3_result_text16,vdbeapi.c,600,0.42,1,0,1,9,4,0,0,1
3042,sqlite3BenignMallocHooks,fault.c,60,0.42,1,0,1,8,2,0,0,1
3043,winCurrentTimeInt64,os_win.c,5973,0.42,0,1,1,13,2,0,0,1
3044,testbrokenext_init,test_loadext.c,118,0.42,0,1,1,12,3,0,0,1
3045,sqlite3PcacheRef,pcache.c,567,0.42,1,0,1,6,1,0,0,1
3046,sqlite3BtreeGetReserveNoMutex,btree.c,3109,0.42,1,0,1,6,1,0,0,1
3047,sqlite3VdbeFrameMemDel,vdbeaux.c,2238,0.42,1,0,1,6,1,0,0,1
3048,winIsLongPathPrefix,os_win.c,5543,0.42,1,0,1,6,1,0,0,1
3049,broken_init,test_autoext.c,73,0.42,0,1,1,11,3,0,0,1
3050,utf8_to_utf8,test_hexio.c,306,0.42,0,1,1,11,4,0,0,1
3051,sqliteCmdUsage,tclsqlite.c,3767,0.42,0,1,1,11,2,0,0,1
3052,sqlite3PcacheClose,pcache.c,729,0.41,1,0,1,5,1,0,0,1
3053,sqlite3SelectWalkFail,expr.c,2297,0.41,1,0,1,5,2,0,0,1
3054,sqlite3WhereIsSorted,where.c,5355,0.41,1,0,1,5,1,0,0,1
3055,sqlite3BtreeIsInBackup,btree.c,11264,0.41,1,0,1,5,1,0,0,1
3056,sqlite3_result_error16,vdbeapi.c,487,0.41,1,0,1,5,3,0,0,1
3057,DbTraceHandler,tclsqlite.c,702,0.41,0,1,1,10,2,0,0,1
3058,sqlite3_schema_init,test_schema.c,355,0.41,0,1,1,10,3,0,0,1
3059,vfslogDelete,test_osinst.c,512,0.41,0,1,1,10,3,0,0,1
3060,sqlite3PcacheShrink,pcache.c,892,0.41,1,0,1,4,1,0,0,1
3061,sqlite3BtreeCursorHintFlags,btree.c,1020,0.41,1,0,1,4,2,0,0,1
3062,sqlite3BtreeCursorPin,btree.c,4886,0.41,1,0,1,4,1,0,0,1
3063,sqlite3BtreeCursorUnpin,btree.c,4890,0.41,1,0,1,4,1,0,0,1
3064,sqlite3BtreeIncrblobCursor,btree.c,11401,0.41,1,0,1,4,1,0,0,1
3065,sqlite3VdbeLinkSubProgram,vdbeaux.c,1452,0.41,1,0,1,4,2,0,0,1
3066,sqlite3_enable_shared_cache,btree.c,89,0.41,1,0,1,4,1,0,0,1
3067,sqlite3VdbeOneByteSerialTypeLen,vdbeaux.c,3978,0.41,1,0,1,4,1,0,0,1
3068,sqlite3_os_end,os_win.c,6201,0.41,1,0,1,4,0,0,0,1
3069,sqlite3_release_memory,malloc.c,23,0.41,1,0,1,4,1,0,0,1
3070,sqlite3_aggregate_count,vdbeapi.c,1239,0.41,1,0,1,4,1,0,0,1
3071,createMask,where.c,288,0.41,1,0,1,4,2,0,0,1
3072,resetAutoExtObjCmd,test_autoext.c,189,0.41,0,1,1,9,4,0,0,1
3073,test_translate_selftest,test5.c,185,0.41,0,1,1,9,4,0,0,1
3074,sqlite3OsDlClose,os.c,269,0.41,1,0,1,3,2,0,0,1
3075,sqlite3OsShmBarrier,os.c,173,0.41,1,0,1,3,1,0,0,1
3076,sqlite3OsShmUnmap,os.c,176,0.41,1,0,1,3,2,0,0,1
3077,sqlite3PagerIsMemdb,pager.c,6872,0.41,1,0,1,3,1,0,0,1
3078,sqlite3PagerJournalname,pager.c,7095,0.41,1,0,1,3,1,0,0,1
3079,sqlite3PCacheIsDirty,pcache.c,919,0.41,1,0,1,3,1,0,0,1
3080,sqlite3IsMemdb,memdb.c,914,0.41,1,0,1,3,1,0,0,1
3081,sqlite3WhereBreakLabel,where.c,147,0.41,1,0,1,3,1,0,0,1
3082,sqlite3BtreeCursorHasHint,btree.c,11447,0.41,1,0,1,3,2,0,0,1
3083,sqlite3VdbeHasSubProgram,vdbeaux.c,1460,0.41,1,0,1,3,1,0,0,1
3084,sqlite3VdbeParser,vdbeaux.c,52,0.41,1,0,1,3,1,0,0,1
3085,sqlite3VdbePrepareFlags,vdbeaux.c,5331,0.41,1,0,1,3,1,0,0,1
3086,sqlite3VdbeRunOnlyOnce,vdbeaux.c,661,0.41,1,0,1,3,1,0,0,1
3087,sqlite3_changes64,main.c,1120,0.41,1,0,1,3,1,0,0,1
3088,sqlite3_total_changes64,main.c,1136,0.41,1,0,1,3,1,0,0,1
3089,sqlite3_changes,main.c,1129,0.41,1,0,1,3,1,0,0,1
3090,sqlite3_get_autocommit,main.c,3827,0.41,1,0,1,3,1,0,0,1
3091,sqlite3_global_recover,main.c,3816,0.41,1,0,1,3,0,0,0,1
3092,sqlite3_is_interrupted,main.c,1848,0.41,1,0,1,3,1,0,0,1
3093,sqlite3WalFile,wal.c,4591,0.41,1,0,1,3,1,0,0,1
3094,faultsimBenignFailures,test_malloc.c,150,0.41,1,0,1,3,0,0,0,1
3095,faultsimFailures,test_malloc.c,142,0.41,1,0,1,3,0,0,0,1
3096,sqlite3_value_nochange,vdbeapi.c,334,0.41,1,0,1,3,1,0,0,1
3097,DbObjCmdAdaptor,tclsqlite.c,3753,0.41,0,1,1,8,4,0,0,1
3098,sqlite3_thread_cleanup,main.c,3892,0.41,1,0,1,2,0,0,0,1
3099,sqlite3PcacheSize,pcache.c,319,0.40,1,0,1,1,0,0,0,1
3100,sqlite3_libversion,main.c,101,0.40,1,0,1,1,0,0,0,1
3101,pcache1Shutdown,pcache1.c,752,0.40,0,1,1,5,1,0,0,1
3102,demoCurrentTime,test_demovfs.c,608,0.40,0,1,1,5,2,0,0,1
3103,winMutexEnter,mutex_w32.c,284,0.40,0,1,1,5,1,0,0,1
3104,winMutexLeave,mutex_w32.c,357,0.40,0,1,1,5,1,0,0,1
3105,schemaDestroy,test_schema.c,75,0.40,0,1,1,4,1,0,0,1
3106,whereOrMove,where.c,191,0.40,0,1,1,4,2,0,0,1
3107,sqlite3MemFree,mem1.c,162,0.40,0,1,1,3,1,0,0,1
3108,tmpTruncate,test_onefile.c,307,0.39,0,0,2,5,2,0,0,1
3109,sqlite3BtreeTxnState,btree.c,11231,0.39,0,0,2,4,1,0,0,1
3110,sqlite3_bind_parameter_count,vdbeapi.c,1897,0.39,0,0,2,4,1,0,0,1
3111,schemaEof,test_schema.c,164,0.39,0,0,2,4,1,0,0,1
3112,tclvarEof,test_tclvar.c,293,0.39,0,0,2,4,1,0,0,1
3113,sqlite3PcacheFetch,pcache.c,403,0.39,0,0,1,21,3,0,0,1
3114,sqlite3OsGetLastError,os.c,287,0.39,0,0,2,3,1,0,0,1
3115,sqlite3PagerJrnlFile,pager.c,7084,0.39,0,0,2,3,1,0,0,1
3116,sqlite3JournalSize,memjournal.c,438,0.39,0,0,2,3,1,0,0,1
3117,gatherSelectWindowsSelectCallback,expr.c,1778,0.39,0,0,2,3,2,0,0,1
3118,sqlite3_stmt_isexplain,vdbeapi.c,1997,0.39,0,0,2,3,1,0,0,1
3119,sqlite3_str_length,printf.c,1125,0.39,0,0,2,3,1,0,0,1
3120,echoEof,test8.c,580,0.39,0,0,2,3,1,0,0,1
3121,sqlite3_stmt_readonly,vdbeapi.c,1989,0.39,0,0,2,3,1,0,0,1
3122,clock_milliseconds_proc,test_thread.c,387,0.37,0,0,1,16,4,0,0,1
3123,vfslogShmMap,test_osinst.c,442,0.37,0,0,1,16,5,0,0,1
3124,sqlite3JsonTableFunctions,json.c,5451,0.37,0,0,1,15,1,0,0,1
3125,pcache1Fetch,pcache1.c,1049,0.37,0,0,1,15,3,0,0,1
3126,sqlite3_prepare16_v3,prepare.c,1074,0.37,0,0,1,15,6,0,0,1
3127,testloadext_init,test_loadext.c,97,0.37,0,0,1,15,3,0,0,1
3128,binarize,test5.c,29,0.37,0,0,1,15,4,0,0,1
3129,vfs_initfail_test,test1.c,6517,0.37,0,0,1,15,4,0,0,1
3130,vfslogRead,test_osinst.c,273,0.37,0,0,1,15,4,0,0,1
3131,vfslogWrite,test_osinst.c,292,0.37,0,0,1,15,4,0,0,1
3132,sqlite3OsOpen,os.c,215,0.37,0,0,1,14,5,0,0,1
3133,sqlite3Attach,attach.c,433,0.37,0,0,1,14,4,0,0,1
3134,sqlite3Detach,attach.c,413,0.37,0,0,1,14,2,0,0,1
3135,sqlite3DefaultMutex,mutex_w32.c,379,0.37,0,0,1,14,0,0,0,1
3136,clock_seconds_proc,test_thread.c,368,0.37,0,0,1,14,4,0,0,1
3137,tclCollateNeeded,tclsqlite.c,959,0.37,0,0,1,13,4,0,0,1
3138,sqlthread_id,test_thread.c,302,0.37,0,0,1,13,4,0,0,1
3139,memdbAccess,memdb.c,639,0.36,0,0,1,12,4,0,0,1
3140,getPageError,pager.c,5677,0.36,0,0,1,12,4,0,0,1
3141,pagerOpentemp,pager.c,3654,0.36,0,0,1,12,3,0,0,1
3142,sqlite3_prepare16,prepare.c,1050,0.36,0,0,1,12,5,0,0,1
3143,sqlite3_prepare16_v2,prepare.c,1062,0.36,0,0,1,12,5,0,0,1
3144,register_circle_geom,test_rtree.c,463,0.36,0,0,1,12,4,0,0,1
3145,register_cube_geom,test_rtree.c,435,0.36,0,0,1,12,4,0,0,1
3146,winGetSystemCall,os_win.c,1209,0.36,0,0,1,11,2,0,0,1
3147,sqlite3OsFullPathname,os.c,247,0.36,0,0,1,10,4,0,0,1
3148,sqlite3OsShmMap,os.c,179,0.36,0,0,1,10,5,0,0,1
3149,sqlite3_memory_alarm,malloc.c,72,0.36,0,0,1,10,3,0,0,1
3150,multiplexFuncInit,test_multiplex.c,417,0.36,0,0,1,10,3,0,0,1
3151,sqlite3_bind_blob64,vdbeapi.c,1727,0.36,0,0,1,10,5,0,0,1
3152,xLogcallback,test1.c,7571,0.36,0,0,1,10,3,0,0,1
3153,noopStepFunc,window.c,569,0.36,0,0,1,10,3,0,0,1
3154,vfslogCheckReservedLock,test_osinst.c,381,0.36,0,0,1,10,2,0,0,1
3155,vfslogDeviceCharacteristics,test_osinst.c,421,0.36,0,0,1,10,1,0,0,1
3156,vfslogFileSize,test_osinst.c,339,0.36,0,0,1,10,2,0,0,1
3157,vfslogLock,test_osinst.c,353,0.36,0,0,1,10,2,0,0,1
3158,vfslogSectorSize,test_osinst.c,407,0.36,0,0,1,10,1,0,0,1
3159,vfslogShmLock,test_osinst.c,432,0.36,0,0,1,10,4,0,0,1
3160,vfslogShmUnmap,test_osinst.c,466,0.36,0,0,1,10,2,0,0,1
3161,vfslogSync,test_osinst.c,325,0.36,0,0,1,10,2,0,0,1
3162,vfslogTruncate,test_osinst.c,311,0.36,0,0,1,10,2,0,0,1
3163,vfslogUnlock,test_osinst.c,367,0.36,0,0,1,10,2,0,0,1
3164,vlogFilter,test_osinst.c,1022,0.36,0,0,1,10,5,0,0,1
3165,quotaShmMap,test_quota.c,700,0.36,0,0,1,10,5,0,0,1
3166,sqlite3OsAccess,os.c,238,0.36,0,0,1,9,4,0,0,1
3167,sqlite3StatusDown,status.c,100,0.36,0,0,1,9,2,0,0,1
3168,cube_init,test_autoext.c,60,0.36,0,0,1,9,3,0,0,1
3169,sqr_init,test_autoext.c,35,0.36,0,0,1,9,3,0,0,1
3170,intarrayFilter,test_intarray.c,167,0.36,0,0,1,9,5,0,0,1
3171,sqlite3_bind_text,vdbeapi.c,1791,0.36,0,0,1,9,5,0,0,1
3172,sqlite3_result_text16be,vdbeapi.c,609,0.36,0,0,1,9,4,0,0,1
3173,sqlite3_result_text16le,vdbeapi.c,618,0.36,0,0,1,9,4,0,0,1
3174,fsFullPathname,test_onefile.c,746,0.36,0,0,1,9,4,0,0,1
3175,sqlite3_bind_blob,vdbeapi.c,1715,0.36,0,0,1,9,5,0,0,1
3176,sqlite3_bind_text16,vdbeapi.c,1816,0.36,0,0,1,9,5,0,0,1
3177,sqlite3_vtab_on_conflict,vtab.c,1313,0.36,0,0,1,9,1,0,0,1
3178,database_may_be_corrupt,test1.c,7329,0.36,0,0,1,9,4,0,0,1
3179,database_never_corrupt,test1.c,7345,0.36,0,0,1,9,4,0,0,1
3180,intrealFunction,test1.c,981,0.36,0,0,1,9,3,0,0,1
3181,restore_prng_state,test1.c,7243,0.36,0,0,1,9,4,0,0,1
3182,quotaRead,test_quota.c,551,0.36,0,0,1,9,4,0,0,1
3183,quotaShmLock,test_quota.c,713,0.36,0,0,1,9,4,0,0,1
3184,sqlite3StatusValue,status.c,69,0.36,0,0,1,8,1,0,0,1
3185,sourceidFunc,func.c,1011,0.36,0,0,1,8,3,0,0,1
3186,versionFunc,func.c,995,0.36,0,0,1,8,3,0,0,1
3187,pcache1Pagecount,pcache1.c,858,0.36,0,0,1,8,1,0,0,1
3188,echoRollback,test8.c,1185,0.36,0,0,1,8,1,0,0,1
3189,wrPCacheRekey,test_init.c,141,0.36,0,0,1,8,4,0,0,1
3190,incrblobSeek,tclsqlite.c,406,0.36,0,0,1,8,4,0,0,1
3191,counterMutexTry,test_mutex.c,135,0.36,0,0,1,8,1,0,0,1
3192,test_breakpoint,test1.c,3678,0.36,0,0,1,8,4,0,0,1
3193,test_io_trace,test1.c,231,0.36,0,0,1,8,4,0,0,1
3194,test_key,test1.c,656,0.36,0,0,1,8,4,0,0,1
3195,test_rekey,test1.c,670,0.36,0,0,1,8,4,0,0,1
3196,test_release_memory,test1.c,6120,0.36,0,0,1,8,4,0,0,1
3197,vfslogFullPathname,test_osinst.c,548,0.36,0,0,1,8,4,0,0,1
3198,vfslogShmBarrier,test_osinst.c,458,0.36,0,0,1,8,1,0,0,1
3199,memdbDeviceCharacteristics,memdb.c,501,0.35,0,0,1,7,1,0,0,1
3200,incrblobHandle,tclsqlite.c,422,0.35,0,0,1,7,3,0,0,1
3201,counterMutexEnter,test_mutex.c,124,0.35,0,0,1,7,1,0,0,1
3202,incrblobClose,tclsqlite.c,299,0.35,0,0,1,6,2,0,0,1
3203,legacyCountStep,test1.c,1276,0.35,0,0,1,6,3,0,0,1
3204,sqlite3OsDlOpen,os.c,258,0.35,0,0,1,5,2,0,0,1
3205,sqlite3OsLock,os.c,107,0.35,0,0,1,5,2,0,0,1
3206,sqlite3WalkerDepthIncrease,walker.c,229,0.35,0,0,1,5,2,0,0,1
3207,sqlite3BtreeMaxRecordSize,btree.c,4936,0.35,0,0,1,5,1,0,0,1
3208,memdbSync,memdb.c,348,0.35,0,0,1,5,2,0,0,1
3209,memjrnlFileSize,memjournal.c,310,0.35,0,0,1,5,2,0,0,1
3210,jsonEachRowid,json.c,5147,0.35,0,0,1,5,2,0,0,1
3211,sqlite3_memory_highwater,malloc.c,208,0.35,0,0,1,5,1,0,0,1
3212,sqlite3_memory_used,malloc.c,197,0.35,0,0,1,5,0,0,0,1
3213,pragmaVtabRowid,pragma.c,3020,0.35,0,0,1,5,2,0,0,1
3214,intarrayEof,test_intarray.c,149,0.35,0,0,1,5,1,0,0,1
3215,intarrayNext,test_intarray.c,158,0.35,0,0,1,5,1,0,0,1
3216,intarrayRowid,test_intarray.c,143,0.35,0,0,1,5,2,0,0,1
3217,sqlite3_vtab_distinct,where.c,4498,0.35,0,0,1,5,1,0,0,1
3218,tmpFileSize,test_onefile.c,323,0.35,0,0,1,5,2,0,0,1
3219,wrMutexEnd,test_init.c,80,0.35,0,0,1,5,0,0,0,1
3220,incrblobWatch,tclsqlite.c,416,0.35,0,0,1,5,2,0,0,1
3221,schemaRowid,test_schema.c,152,0.35,0,0,1,5,2,0,0,1
3222,fsdirRowid,test_fs.c,311,0.35,0,0,1,5,2,0,0,1
3223,winDeviceCharacteristics,os_win.c,3666,0.35,0,0,1,5,1,0,0,1
3224,winNolockCheckReservedLock,os_win.c,3478,0.35,0,0,1,5,2,0,0,1
3225,winNolockLock,os_win.c,3472,0.35,0,0,1,5,2,0,0,1
3226,winNolockUnlock,os_win.c,3484,0.35,0,0,1,5,2,0,0,1
3227,vlogEof,test_osinst.c,1016,0.35,0,0,1,5,1,0,0,1
3228,vlogRowid,test_osinst.c,1065,0.35,0,0,1,5,2,0,0,1
3229,quotaFileControl,test_quota.c,673,0.35,0,0,1,5,3,0,0,1
3230,sqlite3OsCheckReservedLock,os.c,116,0.35,0,0,1,4,2,0,0,1
3231,sqlite3OsFileControl,os.c,129,0.35,0,0,1,4,3,0,0,1
3232,sqlite3OsFileSize,os.c,103,0.35,0,0,1,4,2,0,0,1
3233,sqlite3OsRead,os.c,88,0.35,0,0,1,4,4,0,0,1
3234,sqlite3OsSync,os.c,99,0.35,0,0,1,4,2,0,0,1
3235,sqlite3OsUnlock,os.c,112,0.35,0,0,1,4,2,0,0,1
3236,sqlite3OsWrite,os.c,92,0.35,0,0,1,4,4,0,0,1
3237,sqlite3WalkWinDefnDummyCallback,walker.c,121,0.35,0,0,1,4,2,0,0,1
3238,sqlite3ExprWalkNoop,walker.c,249,0.35,0,0,1,4,2,0,0,1
3239,sqlite3SelectWalkNoop,walker.c,258,0.35,0,0,1,4,2,0,0,1
3240,sqlite3WalkerDepthDecrease,walker.c,234,0.35,0,0,1,4,2,0,0,1
3241,sqlite3MemInit,mem1.c,239,0.35,0,0,1,4,1,0,0,1
3242,sqlite3MemShutdown,mem1.c,266,0.35,0,0,1,4,1,0,0,1
3243,noopMutexAlloc,mutex_noop.c,40,0.35,0,0,1,4,1,0,0,1
3244,noopMutexTry,mutex_noop.c,46,0.35,0,0,1,4,1,0,0,1
3245,memjrnlSync,memjournal.c,302,0.35,0,0,1,4,2,0,0,1
3246,jsonEachEof,json.c,4908,0.35,0,0,1,4,1,0,0,1
3247,pragmaVtabEof,pragma.c,2994,0.35,0,0,1,4,1,0,0,1
3248,backupTestFinish,test_backup.c,101,0.35,0,0,1,4,1,0,0,1
3249,demoCheckReservedLock,test_demovfs.c,366,0.35,0,0,1,4,2,0,0,1
3250,echoRelease,test8.c,1282,0.35,0,0,1,4,2,0,0,1
3251,echoRollbackTo,test8.c,1287,0.35,0,0,1,4,2,0,0,1
3252,echoSavepoint,test8.c,1277,0.35,0,0,1,4,2,0,0,1
3253,tclEof,test_bestindex.c,398,0.35,0,0,1,4,1,0,0,1
3254,fsCheckReservedLock,test_onefile.c,554,0.35,0,0,1,4,2,0,0,1
3255,fsCurrentTime,test_onefile.c,811,0.35,0,0,1,4,2,0,0,1
3256,fsDlClose,test_onefile.c,785,0.35,0,0,1,4,2,0,0,1
3257,fsDlError,test_onefile.c,769,0.35,0,0,1,4,3,0,0,1
3258,fsDlOpen,test_onefile.c,759,0.35,0,0,1,4,2,0,0,1
3259,fsDlSym,test_onefile.c,777,0.35,0,0,1,4,3,0,0,1
3260,fsFileControl,test_onefile.c,562,0.35,0,0,1,4,3,0,0,1
3261,fsRandomness,test_onefile.c,794,0.35,0,0,1,4,3,0,0,1
3262,fsSleep,test_onefile.c,803,0.35,0,0,1,4,2,0,0,1
3263,tmpCheckReservedLock,test_onefile.c,346,0.35,0,0,1,4,2,0,0,1
3264,wrMemShutdown,test_init.c,57,0.35,0,0,1,4,1,0,0,1
3265,wrPCacheShutdown,test_init.c,121,0.35,0,0,1,4,1,0,0,1
3266,counterMutexEnd,test_mutex.c,77,0.35,0,0,1,4,0,0,0,1
3267,counterMutexLeave,test_mutex.c,146,0.35,0,0,1,4,1,0,0,1
3268,tclvarRowid,test_tclvar.c,288,0.35,0,0,1,4,2,0,0,1
3269,fsdirEof,test_fs.c,282,0.35,0,0,1,4,1,0,0,1
3270,fstreeEof,test_fs.c,552,0.35,0,0,1,4,1,0,0,1
3271,fstreeRowid,test_fs.c,591,0.35,0,0,1,4,2,0,0,1
3272,sqlite3_expired,vdbeapi.c,29,0.35,0,0,1,4,1,0,0,1
3273,sqlite3_stmt_busy,vdbeapi.c,2040,0.35,0,0,1,4,1,0,0,1
3274,winSectorSize,os_win.c,3658,0.35,0,0,1,4,1,0,0,1
3275,vlogBestIndex,test_osinst.c,916,0.35,0,0,1,4,2,0,0,1
3276,quotaCheckReservedLock,test_quota.c,666,0.35,0,0,1,4,2,0,0,1
3277,quotaDeviceCharacteristics,test_quota.c,693,0.35,0,0,1,4,1,0,0,1
3278,quotaLock,test_quota.c,652,0.35,0,0,1,4,2,0,0,1
3279,quotaSectorSize,test_quota.c,686,0.35,0,0,1,4,1,0,0,1
3280,quotaShmBarrier,test_quota.c,725,0.35,0,0,1,4,1,0,0,1
3281,quotaShmUnmap,test_quota.c,732,0.35,0,0,1,4,2,0,0,1
3282,quotaSubOpen,test_quota.c,339,0.35,0,0,1,4,1,0,0,1
3283,quotaSync,test_quota.c,621,0.35,0,0,1,4,2,0,0,1
3284,quotaUnlock,test_quota.c,659,0.35,0,0,1,4,2,0,0,1
3285,sqlite3OsDlError,os.c,263,0.34,0,0,1,3,3,0,0,1
3286,sqlite3OsDlSym,os.c,266,0.34,0,0,1,3,3,0,0,1
3287,sqlite3OsShmLock,os.c,170,0.34,0,0,1,3,4,0,0,1
3288,sqlite3OsTruncate,os.c,96,0.34,0,0,1,3,2,0,0,1
3289,sqlite3PagerBackupPtr,pager.c,7454,0.34,0,0,1,3,1,0,0,1
3290,sqlite3PagerFile,pager.c,7076,0.34,0,0,1,3,1,0,0,1
3291,sqlite3PagerVfs,pager.c,7067,0.34,0,0,1,3,1,0,0,1
3292,validJulianDay,date.c,454,0.34,0,0,1,3,1,0,0,1
3293,sqlite3_backup_pagecount,backup.c,639,0.34,0,0,1,3,1,0,0,1
3294,sqlite3_backup_remaining,backup.c,625,0.34,0,0,1,3,1,0,0,1
3295,sqlite3JournalIsInMemory,memjournal.c,430,0.34,0,0,1,3,1,0,0,1
3296,sqlite3MallocMutex,malloc.c,62,0.34,0,0,1,3,0,0,0,1
3297,sqlite3Pcache1Mutex,pcache1.c,1224,0.34,0,0,1,3,0,0,0,1
3298,sqlite3VdbeDb,vdbeaux.c,5324,0.34,0,0,1,3,1,0,0,1
3299,sqlite3VdbeResetStepResult,vdbeaux.c,3505,0.34,0,0,1,3,1,0,0,1
3300,sqlite3_value_encoding,vdbeapi.c,329,0.34,0,0,1,3,1,0,0,1
3301,sqlite3MemRoundup,mem1.c,232,0.34,0,0,1,3,1,0,0,1
3302,memdbCurrentTimeInt64,memdb.c,726,0.34,0,0,1,3,2,0,0,1
3303,memdbDlClose,memdb.c,694,0.34,0,0,1,3,2,0,0,1
3304,memdbDlError,memdb.c,680,0.34,0,0,1,3,3,0,0,1
3305,memdbDlOpen,memdb.c,671,0.34,0,0,1,3,2,0,0,1
3306,memdbDlSym,memdb.c,687,0.34,0,0,1,3,3,0,0,1
3307,memdbGetLastError,memdb.c,723,0.34,0,0,1,3,3,0,0,1
3308,memdbRandomness,memdb.c,702,0.34,0,0,1,3,3,0,0,1
3309,memdbSleep,memdb.c,710,0.34,0,0,1,3,2,0,0,1
3310,sqlite3_column_name,vdbeapi.c,1532,0.34,0,0,1,3,2,0,0,1
3311,sqlite3_total_changes,main.c,1145,0.34,0,0,1,3,1,0,0,1
3312,sqlite3_str_errcode,printf.c,1120,0.34,0,0,1,3,1,0,0,1
3313,pager_test_reiniter,test2.c,32,0.34,0,0,1,3,1,0,0,1
3314,demoDeviceCharacteristics,test_demovfs.c,386,0.34,0,0,1,3,1,0,0,1
3315,demoDlClose,test_demovfs.c,575,0.34,0,0,1,3,2,0,0,1
3316,demoDlOpen,test_demovfs.c,565,0.34,0,0,1,3,2,0,0,1
3317,demoDlSym,test_demovfs.c,572,0.34,0,0,1,3,3,0,0,1
3318,demoFileControl,test_demovfs.c,374,0.34,0,0,1,3,3,0,0,1
3319,demoLock,test_demovfs.c,360,0.34,0,0,1,3,2,0,0,1
3320,demoRandomness,test_demovfs.c,583,0.34,0,0,1,3,3,0,0,1
3321,demoSectorSize,test_demovfs.c,383,0.34,0,0,1,3,1,0,0,1
3322,demoTruncate,test_demovfs.c,307,0.34,0,0,1,3,2,0,0,1
3323,demoUnlock,test_demovfs.c,363,0.34,0,0,1,3,2,0,0,1
3324,intarrayBestIndex,test_intarray.c,180,0.34,0,0,1,3,2,0,0,1
3325,sqlite3_blob_read,vdbeblob.c,457,0.34,0,0,1,3,4,0,0,1
3326,sqlite3_blob_write,vdbeblob.c,464,0.34,0,0,1,3,4,0,0,1
3327,sqlite3_value_frombind,vdbeapi.c,339,0.34,0,0,1,3,1,0,0,1
3328,fsDeviceCharacteristics,test_onefile.c,577,0.34,0,0,1,3,1,0,0,1
3329,fsLock,test_onefile.c,540,0.34,0,0,1,3,2,0,0,1
3330,fsSectorSize,test_onefile.c,570,0.34,0,0,1,3,1,0,0,1
3331,fsUnlock,test_onefile.c,547,0.34,0,0,1,3,2,0,0,1
3332,tmpDeviceCharacteristics,test_onefile.c,368,0.34,0,0,1,3,1,0,0,1
3333,tmpFileControl,test_onefile.c,354,0.34,0,0,1,3,3,0,0,1
3334,tmpLock,test_onefile.c,332,0.34,0,0,1,3,2,0,0,1
3335,tmpSectorSize,test_onefile.c,361,0.34,0,0,1,3,1,0,0,1
3336,tmpSync,test_onefile.c,316,0.34,0,0,1,3,2,0,0,1
3337,tmpUnlock,test_onefile.c,339,0.34,0,0,1,3,2,0,0,1
3338,wrMutexAlloc,test_init.c,85,0.34,0,0,1,3,1,0,0,1
3339,wrMutexEnter,test_init.c,91,0.34,0,0,1,3,1,0,0,1
3340,wrMutexFree,test_init.c,88,0.34,0,0,1,3,1,0,0,1
3341,wrMutexHeld,test_init.c,100,0.34,0,0,1,3,1,0,0,1
3342,wrMutexLeave,test_init.c,97,0.34,0,0,1,3,1,0,0,1
3343,wrMutexNotheld,test_init.c,103,0.34,0,0,1,3,1,0,0,1
3344,wrMutexTry,test_init.c,94,0.34,0,0,1,3,1,0,0,1
3345,wrPCacheCachesize,test_init.c,129,0.34,0,0,1,3,2,0,0,1
3346,wrPCacheCreate,test_init.c,126,0.34,0,0,1,3,3,0,0,1
3347,wrPCacheDestroy,test_init.c,152,0.34,0,0,1,3,1,0,0,1
3348,wrPCacheFetch,test_init.c,135,0.34,0,0,1,3,3,0,0,1
3349,wrPCachePagecount,test_init.c,132,0.34,0,0,1,3,1,0,0,1
3350,wrPCacheTruncate,test_init.c,149,0.34,0,0,1,3,2,0,0,1
3351,wrPCacheUnpin,test_init.c,138,0.34,0,0,1,3,3,0,0,1
3352,multiplexAccess,test_multiplex.c,656,0.34,0,0,1,3,4,0,0,1
3353,multiplexCurrentTime,test_multiplex.c,680,0.34,0,0,1,3,2,0,0,1
3354,multiplexCurrentTimeInt64,test_multiplex.c,690,0.34,0,0,1,3,2,0,0,1
3355,multiplexDlClose,test_multiplex.c,671,0.34,0,0,1,3,2,0,0,1
3356,multiplexDlError,test_multiplex.c,665,0.34,0,0,1,3,3,0,0,1
3357,multiplexDlOpen,test_multiplex.c,662,0.34,0,0,1,3,2,0,0,1
3358,multiplexDlSym,test_multiplex.c,668,0.34,0,0,1,3,3,0,0,1
3359,multiplexFullPathname,test_multiplex.c,659,0.34,0,0,1,3,4,0,0,1
3360,multiplexRandomness,test_multiplex.c,674,0.34,0,0,1,3,3,0,0,1
3361,multiplexSleep,test_multiplex.c,677,0.34,0,0,1,3,2,0,0,1
3362,schemaBestIndex,test_schema.c,262,0.34,0,0,1,3,2,0,0,1
3363,faultsimBeginBenign,test_malloc.c,167,0.34,0,0,1,3,0,0,0,1
3364,faultsimEndBenign,test_malloc.c,170,0.34,0,0,1,3,0,0,0,1
3365,counterMutexHeld,test_mutex.c,52,0.34,0,0,1,3,1,0,0,1
3366,counterMutexNotheld,test_mutex.c,57,0.34,0,0,1,3,1,0,0,1
3367,sqlite3_column_decltype,vdbeapi.c,1555,0.34,0,0,1,3,2,0,0,1
3368,sqlite3_column_decltype16,vdbeapi.c,1559,0.34,0,0,1,3,2,0,0,1
3369,sqlite3_column_name16,vdbeapi.c,1536,0.34,0,0,1,3,2,0,0,1
3370,sqlite3_value_text16be,vdbeapi.c,234,0.34,0,0,1,3,1,0,0,1
3371,sqlite3_value_text16le,vdbeapi.c,237,0.34,0,0,1,3,1,0,0,1
3372,vfslogCurrentTime,test_osinst.c,606,0.34,0,0,1,3,2,0,0,1
3373,vfslogCurrentTimeInt64,test_osinst.c,613,0.34,0,0,1,3,2,0,0,1
3374,vfslogDlClose,test_osinst.c,583,0.34,0,0,1,3,2,0,0,1
3375,vfslogDlError,test_osinst.c,569,0.34,0,0,1,3,3,0,0,1
3376,vfslogDlOpen,test_osinst.c,560,0.34,0,0,1,3,2,0,0,1
3377,vfslogDlSym,test_osinst.c,576,0.34,0,0,1,3,3,0,0,1
3378,vfslogGetLastError,test_osinst.c,610,0.34,0,0,1,3,3,0,0,1
3379,vfslogRandomness,test_osinst.c,591,0.34,0,0,1,3,3,0,0,1
3380,vfslogSleep,test_osinst.c,599,0.34,0,0,1,3,2,0,0,1
3381,sqlite3BtreeCursorList,test_btree.c,48,0.34,0,0,1,2,1,0,0,1
3382,test_agg_errmsg16_step,test_func.c,161,0.34,0,0,1,2,3,0,0,1
3383,cf2Final,test1.c,1930,0.34,0,0,1,2,1,0,0,1
3384,cf2Func,test1.c,1926,0.34,0,0,1,2,3,0,0,1
3385,cf2Step,test1.c,1928,0.34,0,0,1,2,3,0,0,1
3386,sqlite3HeaderSizePcache,pcache.c,901,0.34,0,0,1,1,0,0,0,1
3387,sqlite3HeaderSizePcache1,pcache1.c,1218,0.34,0,0,1,1,0,0,0,1
3388,sqlite3NoopDestructor,vdbemem.c,954,0.34,0,0,1,1,1,0,0,1
3389,sqlite3HeaderSizeBtree,btree.c,11461,0.34,0,0,1,1,0,0,0,1
3390,noopMutexEnd,mutex_noop.c,39,0.34,0,0,1,1,0,0,0,1
3391,noopMutexEnter,mutex_noop.c,45,0.34,0,0,1,1,1,0,0,1
3392,noopMutexFree,mutex_noop.c,44,0.34,0,0,1,1,1,0,0,1
3393,noopMutexInit,mutex_noop.c,38,0.34,0,0,1,1,0,0,0,1
3394,noopMutexLeave,mutex_noop.c,50,0.34,0,0,1,1,1,0,0,1
3395,sqlite3_threadsafe,main.c,120,0.34,0,0,1,1,0,0,0,1
3396,tFinal,test_func.c,736,0.34,0,0,1,1,1,0,0,1
3397,tStep,test_func.c,735,0.34,0,0,1,1,3,0,0,1
3398,wrMemFree,test_init.c,62,0.34,0,0,1,1,1,0,0,1
3399,wrMemMalloc,test_init.c,61,0.34,0,0,1,1,1,0,0,1
3400,wrMemRealloc,test_init.c,63,0.34,0,0,1,1,2,0,0,1
3401,wrMemRoundup,test_init.c,65,0.34,0,0,1,1,1,0,0,1
3402,wrMemSize,test_init.c,64,0.34,0,0,1,1,1,0,0,1
3403,Sqlite3_SafeInit,tclsqlite.c,4006,0.34,0,0,1,1,1,0,0,1
3404,Sqlite3_SafeUnload,tclsqlite.c,4007,0.34,0,0,1,1,2,0,0,1
3405,Sqlite3_Unload,tclsqlite.c,3999,0.34,0,0,1,1,2,0,0,1
3406,Sqlite_Init,tclsqlite.c,4017,0.34,0,0,1,1,1,0,0,1
3407,Sqlite_SafeInit,tclsqlite.c,4021,0.34,0,0,1,1,1,0,0,1
3408,Sqlite_SafeUnload,tclsqlite.c,4022,0.34,0,0,1,1,2,0,0,1
3409,Sqlite_Unload,tclsqlite.c,4019,0.34,0,0,1,1,2,0,0,1
3410,Tclsqlite3_Init,tclsqlite.c,3998,0.34,0,0,1,1,1,0,0,1
3411,Tclsqlite3_Unload,tclsqlite.c,4000,0.34,0,0,1,1,2,0,0,1
3412,Tclsqlite_Init,tclsqlite.c,4018,0.34,0,0,1,1,1,0,0,1
3413,Tclsqlite_Unload,tclsqlite.c,4020,0.34,0,0,1,1,2,0,0,1
3414,sqlite3_Init,tclsqlite.c,4027,0.34,0,0,1,1,1,0,0,1
3415,sqlite_Init,tclsqlite.c,4028,0.34,0,0,1,1,1,0,0,1
3416,noopValueFunc,window.c,579,0.34,0,0,1,1,1,0,0,1
