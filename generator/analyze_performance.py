import json
import re
import os
from llm_client import GeminiClient

class PerformanceAnalyzer:
    def __init__(self):
        self.llm_client = GeminiClient()
        self.sqlite_source_path = "sqlite3.c"
        self.function_scores_path = "tool/function_scores.json"
        self.risks_output_dir = "generator/function_risks"
        
        # 确保输出目录存在
        os.makedirs(self.risks_output_dir, exist_ok=True)
    
    def load_function_scores(self):
        """加载函数性能评分数据"""
        with open(self.function_scores_path, 'r') as f:
            return json.load(f)
    
    def find_function_in_source(self, function_name):
        """在sqlite3.c中找到函数的源码和注释"""
        with open(self.sqlite_source_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 查找函数定义
        pattern = rf'SQLITE_PRIVATE\s+\w+\s+{re.escape(function_name)}\s*\('
        matches = list(re.finditer(pattern, content))
        
        if not matches:
            # 尝试查找static函数
            pattern = rf'static\s+\w+\s+{re.escape(function_name)}\s*\('
            matches = list(re.finditer(pattern, content))
        
        if not matches:
            print(f"未找到函数 {function_name}")
            return None, None
        
        # 取第一个匹配
        match = matches[0]
        func_start = match.start()
        
        # 向前查找注释
        lines = content[:func_start].split('\n')
        comment_lines = []
        
        # 从函数定义前开始向上查找注释
        for i in range(len(lines) - 1, -1, -1):
            line = lines[i].strip()
            if line.startswith('*/'):
                # 找到注释结束，继续向上找注释开始
                comment_lines.insert(0, line)
                for j in range(i - 1, -1, -1):
                    comment_line = lines[j].strip()
                    comment_lines.insert(0, comment_line)
                    if comment_line.startswith('/*'):
                        break
                break
            elif line == '' or line.startswith('//'):
                continue
            else:
                # 遇到非注释行，停止查找
                break
        
        # 提取函数实现
        func_lines = content[func_start:].split('\n')
        function_code = []
        brace_count = 0
        started = False
        
        for line in func_lines:
            function_code.append(line)
            for char in line:
                if char == '{':
                    brace_count += 1
                    started = True
                elif char == '}':
                    brace_count -= 1
            
            if started and brace_count == 0:
                break
        
        comment_text = '\n'.join(comment_lines) if comment_lines else ""
        function_text = '\n'.join(function_code)
        
        return comment_text, function_text
    
    def analyze_function_risks(self, function_name, comment, source_code):
        """使用LLM分析函数的潜在性能风险"""
        prompt = f"""
请分析以下SQLite函数的潜在性能问题和风险。这是一个数据库内核函数，需要重点关注可能导致性能瓶颈的情况。

函数名: {function_name}

函数注释:
{comment}

函数源码:
{source_code}

请分析这个函数可能存在的性能风险，每个风险用一行描述，重点关注：
1. 可能导致大量I/O操作的情况
2. 可能导致内存分配/释放频繁的情况  
3. 可能导致CPU密集计算的情况
4. 可能导致锁竞争的情况
5. 可能导致缓存失效的情况
6. 其他可能的性能瓶颈

如果函数没有明显的性能风险，请回答"无明显性能风险"。

请直接列出风险点，每行一个，不要添加额外的解释或格式。
"""
        
        response = self.llm_client.generate_response(prompt)
        if response and response.strip() != "无明显性能风险":
            return [line.strip() for line in response.strip().split('\n') if line.strip()]
        return []
    
    def save_function_risks(self, function_name, risks):
        """保存函数风险到文件"""
        if risks:
            risk_file = os.path.join(self.risks_output_dir, f"{function_name}.txt")
            with open(risk_file, 'w', encoding='utf-8') as f:
                for risk in risks:
                    f.write(f"{risk}\n")
            print(f"保存了 {function_name} 的 {len(risks)} 个风险点")
    
    def analyze_all_functions(self):
        """分析所有函数的性能风险"""
        function_scores = self.load_function_scores()
        
        for function_name in function_scores.keys():
            print(f"分析函数: {function_name}")
            
            # 检查是否已经分析过
            risk_file = os.path.join(self.risks_output_dir, f"{function_name}.txt")
            if os.path.exists(risk_file):
                print(f"跳过已分析的函数: {function_name}")
                continue
            
            comment, source_code = self.find_function_in_source(function_name)
            
            if source_code:
                risks = self.analyze_function_risks(function_name, comment, source_code)
                self.save_function_risks(function_name, risks)
            else:
                print(f"跳过未找到源码的函数: {function_name}")

if __name__ == "__main__":
    analyzer = PerformanceAnalyzer()
    analyzer.analyze_all_functions()
