#!/usr/bin/env python3
"""
调试导入问题
"""

import sys
print("Python path:")
for path in sys.path:
    print(f"  {path}")

print("\nTrying to import google...")
try:
    import google
    print(f"Google module: {google}")
    print(f"Google module file: {google.__file__ if hasattr(google, '__file__') else 'No __file__'}")
    print(f"Google module path: {google.__path__ if hasattr(google, '__path__') else 'No __path__'}")
    print(f"Google module attributes: {dir(google)}")
except Exception as e:
    print(f"Failed to import google: {e}")

print("\nTrying to import google.generativeai...")
try:
    import google.generativeai as genai
    print(f"Genai module: {genai}")
    print(f"Genai configure: {hasattr(genai, 'configure')}")
    if hasattr(genai, 'configure'):
        print("Configure method exists!")
    else:
        print("Configure method missing!")
        print(f"Available methods: {[attr for attr in dir(genai) if not attr.startswith('_')]}")
except Exception as e:
    print(f"Failed to import google.generativeai: {e}")
