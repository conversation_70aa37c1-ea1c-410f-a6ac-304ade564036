# 基于LLM的SQL生成器

这个工具使用Gemini 2.0 Flash模型分析SQLite函数的性能风险，并生成易触发这些风险的SQL查询。

## 功能概述

1. **性能风险分析** (`analyze_performance.py`)
   - 读取 `tool/function_scores.json` 中的函数列表
   - 在 `sqlite3.c` 中查找函数源码和注释
   - 使用LLM分析函数的潜在性能风险
   - 将风险点保存到 `generator/function_risks/` 目录

2. **SQL生成** (`generate_sql.py`)
   - 读取函数风险分析结果
   - 结合TPC-H数据库模式
   - 使用LLM生成易触发性能异常的SQL查询
   - 将SQL保存到 `generated_sql/` 目录

## 使用方法

### 1. 安装依赖
```bash
cd generator
pip install -r requirements.txt
```

### 2. 运行生成器
```bash
python run_generator.py
```

或者分步运行：
```bash
# 只分析性能风险
python analyze_performance.py

# 只生成SQL（需要先运行性能分析）
python generate_sql.py
```

## 输出文件

- `generator/function_risks/`: 每个函数的风险分析结果，每个函数一个txt文件
- `generated_sql/`: 生成的SQL查询，每个函数一个sql文件

## 文件结构

```
generator/
├── llm_client.py          # Gemini API客户端
├── analyze_performance.py # 性能风险分析
├── generate_sql.py        # SQL生成
├── run_generator.py       # 主控制脚本
├── requirements.txt       # Python依赖
├── README.md             # 说明文档
└── function_risks/       # 风险分析结果目录
```

## 注意事项

- 确保 `tool/function_scores.json` 和 `sqlite3.c` 文件存在
- 生成过程可能需要较长时间，取决于函数数量和API响应速度
- 已生成的文件会被跳过，避免重复处理
