#!/usr/bin/env python3
"""
基于LLM的SQL生成器主控制脚本

使用方法:
运行: python run_generator.py

该脚本将：
1. 分析tool/function_scores.json中的所有函数
2. 在sqlite3.c中找到函数源码和注释
3. 使用LLM分析函数的潜在性能风险
4. 基于风险点生成易触发性能异常的SQL查询
"""

import os
import sys
from analyze_performance import PerformanceAnalyzer
from generate_sql import SQLGenerator

def main():
    print("=== 基于LLM的SQL生成器 ===")
    
    # 检查必要文件
    required_files = [
        "tool/function_scores.json",
        "sqlite3.c"
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"错误: 找不到必要文件 {file_path}")
            sys.exit(1)
    
    print("步骤1: 分析函数性能风险...")
    analyzer = PerformanceAnalyzer()
    analyzer.analyze_all_functions()
    
    print("\n步骤2: 生成SQL查询...")
    generator = SQLGenerator()
    generator.generate_sql_for_all_functions()
    
    print("\n=== 生成完成 ===")
    print(f"函数风险分析结果保存在: generator/function_risks/")
    print(f"生成的SQL查询保存在: generated_sql/")

if __name__ == "__main__":
    main()
