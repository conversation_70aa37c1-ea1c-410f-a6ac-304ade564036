#!/usr/bin/env python3
"""
测试单个函数的处理流程
"""

import os
import sys

# 确保从正确的目录导入
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from analyze_performance import PerformanceAnalyzer
from generate_sql import SQLGenerator

def test_single_function():
    """测试处理单个函数的完整流程"""
    
    # 选择一个高分函数进行测试
    test_function = "sqlite3BtreeNext"
    
    print(f"=== 测试函数: {test_function} ===")
    
    # 步骤1: 分析性能风险
    print("步骤1: 分析性能风险...")
    analyzer = PerformanceAnalyzer()
    
    # 检查函数是否存在于评分文件中
    function_scores = analyzer.load_function_scores()
    if test_function not in function_scores:
        print(f"错误: 函数 {test_function} 不在评分文件中")
        return False
    
    print(f"函数评分: {function_scores[test_function]['normalized_score']}")
    
    # 提取源码
    comment, source_code = analyzer.find_function_in_source(test_function)
    if not source_code:
        print(f"错误: 未找到函数 {test_function} 的源码")
        return False
    
    print(f"源码长度: {len(source_code)} 字符")
    print(f"注释长度: {len(comment) if comment else 0} 字符")
    
    # 分析风险
    risks = analyzer.analyze_function_risks(test_function, comment, source_code)
    if risks:
        print(f"识别到 {len(risks)} 个风险点:")
        for i, risk in enumerate(risks, 1):
            print(f"  {i}. {risk}")
        
        # 保存风险
        analyzer.save_function_risks(test_function, risks)
    else:
        print("未识别到明显的性能风险")
        return True
    
    # 步骤2: 生成SQL
    print("\n步骤2: 生成SQL查询...")
    generator = SQLGenerator()
    
    sql_statements = generator.generate_sql_for_function(test_function, risks)
    if sql_statements:
        print(f"生成了 {len(sql_statements)} 个SQL查询:")
        for i, sql in enumerate(sql_statements, 1):
            print(f"\n--- SQL {i} ---")
            print(sql[:200] + "..." if len(sql) > 200 else sql)
        
        # 保存SQL
        generator.save_generated_sql(test_function, sql_statements)
        print(f"\nSQL已保存到: generated_sql/{test_function}.sql")
    else:
        print("未生成SQL查询")
        return False
    
    return True

if __name__ == "__main__":
    success = test_single_function()
    if success:
        print("\n✓ 测试成功完成!")
    else:
        print("\n✗ 测试失败")
        sys.exit(1)
