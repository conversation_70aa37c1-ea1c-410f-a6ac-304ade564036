import google.generativeai as genai
import os
import time
from typing import Optional

class GeminiClient:
    def __init__(self):
        # 配置Gemini API
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            raise ValueError("请设置GEMINI_API_KEY环境变量")
        
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
    
    def generate_response(self, prompt: str, max_retries: int = 3) -> Optional[str]:
        """
        调用Gemini API生成响应
        """
        for attempt in range(max_retries):
            try:
                response = self.model.generate_content(prompt)
                return response.text
            except Exception as e:
                print(f"API调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    print(f"所有重试都失败了")
                    return None
        return None
