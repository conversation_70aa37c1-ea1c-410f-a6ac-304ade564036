#!/usr/bin/env python3
"""
测试脚本 - 验证生成器的基本功能
"""

import os
import json
from analyze_performance import PerformanceAnalyzer
from generate_sql import SQLGenerator

def test_function_loading():
    """测试函数加载功能"""
    print("测试1: 加载函数评分数据...")
    
    analyzer = PerformanceAnalyzer()
    try:
        function_scores = analyzer.load_function_scores()
        print(f"✓ 成功加载 {len(function_scores)} 个函数")
        
        # 显示前5个函数
        functions = list(function_scores.keys())[:5]
        for func in functions:
            print(f"  - {func}: score={function_scores[func]['normalized_score']}")
        
        return True
    except Exception as e:
        print(f"✗ 加载失败: {e}")
        return False

def test_source_code_extraction():
    """测试源码提取功能"""
    print("\n测试2: 源码提取功能...")
    
    analyzer = PerformanceAnalyzer()
    
    # 测试一个已知存在的函数
    test_function = "sqlite3BtreeNext"
    
    try:
        comment, source_code = analyzer.find_function_in_source(test_function)
        
        if source_code:
            print(f"✓ 成功提取 {test_function} 的源码")
            print(f"  注释长度: {len(comment) if comment else 0} 字符")
            print(f"  源码长度: {len(source_code)} 字符")
            
            # 显示源码的前几行
            lines = source_code.split('\n')[:3]
            for line in lines:
                print(f"  {line}")
            
            return True
        else:
            print(f"✗ 未找到 {test_function} 的源码")
            return False
            
    except Exception as e:
        print(f"✗ 源码提取失败: {e}")
        return False

def test_schema_loading():
    """测试数据库模式加载"""
    print("\n测试3: 数据库模式加载...")
    
    generator = SQLGenerator()
    
    if generator.tpch_schema:
        print("✓ TPC-H数据库模式加载成功")
        print(f"  模式描述长度: {len(generator.tpch_schema)} 字符")
        
        # 检查是否包含主要表名
        tables = ['REGION', 'NATION', 'SUPPLIER', 'CUSTOMER', 'PART', 'PARTSUPP', 'ORDERS', 'LINEITEM']
        found_tables = [table for table in tables if table in generator.tpch_schema]
        print(f"  包含表: {', '.join(found_tables)}")
        
        return len(found_tables) == len(tables)
    else:
        print("✗ 数据库模式为空")
        return False

def test_directory_creation():
    """测试目录创建功能"""
    print("\n测试4: 目录创建功能...")
    
    analyzer = PerformanceAnalyzer()
    generator = SQLGenerator()
    
    # 检查目录是否存在
    risks_dir = analyzer.risks_output_dir
    sql_dir = generator.sql_output_dir
    
    risks_exists = os.path.exists(risks_dir)
    sql_exists = os.path.exists(sql_dir)
    
    print(f"✓ 风险分析目录 ({risks_dir}): {'存在' if risks_exists else '不存在'}")
    print(f"✓ SQL输出目录 ({sql_dir}): {'存在' if sql_exists else '不存在'}")
    
    return risks_exists and sql_exists

def main():
    """运行所有测试"""
    print("=== 生成器功能测试 ===\n")
    
    tests = [
        test_function_loading,
        test_source_code_extraction,
        test_schema_loading,
        test_directory_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果: {passed}/{total} 通过 ===")
    
    if passed == total:
        print("✓ 所有测试通过，生成器准备就绪！")
        print("\n要运行完整的生成流程，请：")
        print("1. 设置 GEMINI_API_KEY 环境变量")
        print("2. 运行: python3 run_generator.py")
    else:
        print("✗ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
