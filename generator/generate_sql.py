import os
import json
from llm_client import GeminiClient

class SQLGenerator:
    def __init__(self):
        self.llm_client = GeminiClient()
        self.function_scores_path = "tool/function_scores.json"
        self.risks_input_dir = "generator/function_risks"
        self.sql_output_dir = "generated_sql"
        
        # 确保输出目录存在
        os.makedirs(self.sql_output_dir, exist_ok=True)
        
        # TPCH数据库模式
        self.tpch_schema = """
TPC-H数据库模式包含以下表：

REGION (R_REGIONKEY, R_NAME, R_COMMENT)
NATION (N_NATIONKEY, N_NAME, N_REGIONKEY, N_COMMENT)
SUPPLIER (S_SUPPKEY, S_NAME, S_ADDRESS, S_NATIONKEY, S_PHONE, S_ACCTBAL, S_COMMENT)
CUSTOMER (C_CUSTKEY, C_NAME, C_ADDRESS, C_NATIONKEY, C_PHON<PERSON>, C_ACCTBAL, C_MKTSEGMENT, C_COMMENT)
PART (P_PARTKEY, P_NAME, P_MFGR, P_BRAND, P_TYPE, P_SIZE, P_CONTAINER, P_RETAILPRICE, P_COMMENT)
PARTSUPP (PS_PARTKEY, PS_SUPPKEY, PS_AVAILQTY, PS_SUPPLYCOST, PS_COMMENT)
ORDERS (O_ORDERKEY, O_CUSTKEY, O_ORDERSTATUS, O_TOTALPRICE, O_ORDERDATE, O_ORDERPRIORITY, O_CLERK, O_SHIPPRIORITY, O_COMMENT)
LINEITEM (L_ORDERKEY, L_PARTKEY, L_SUPPKEY, L_LINENUMBER, L_QUANTITY, L_EXTENDEDPRICE, L_DISCOUNT, L_TAX, L_RETURNFLAG, L_LINESTATUS, L_SHIPDATE, L_COMMITDATE, L_RECEIPTDATE, L_SHIPINSTRUCT, L_SHIPMODE, L_COMMENT)

主要索引：
- 各表主键索引
- idx_customer_nationkey, idx_supplier_nationkey, idx_nation_regionkey
- idx_orders_custkey, idx_orders_orderdate
- idx_lineitem_orderkey, idx_lineitem_partkey, idx_lineitem_suppkey, idx_lineitem_shipdate
- idx_partsupp_partkey, idx_partsupp_suppkey
"""
    
    def load_function_scores(self):
        """加载函数性能评分数据"""
        with open(self.function_scores_path, 'r') as f:
            return json.load(f)
    
    def load_function_risks(self, function_name):
        """加载函数的风险点"""
        risk_file = os.path.join(self.risks_input_dir, f"{function_name}.txt")
        if not os.path.exists(risk_file):
            return []
        
        with open(risk_file, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f.readlines() if line.strip()]
    
    def generate_sql_for_function(self, function_name, risks):
        """为特定函数生成易触发性能异常的SQL"""
        if not risks:
            return []
        
        risks_text = '\n'.join([f"- {risk}" for risk in risks])
        
        prompt = f"""
基于以下SQLite内核函数的性能风险点，生成3-5个SQL查询语句，这些查询应该能够触发该函数的性能瓶颈。

函数名: {function_name}

已识别的性能风险点:
{risks_text}

数据库模式:
{self.tpch_schema}

请生成能够触发上述性能风险的SQL查询，要求：
1. 每个SQL查询都应该针对特定的风险点设计
2. 查询应该是有效的SQL语句，能在TPC-H数据库上执行
3. 查询应该尽可能复杂，以增加触发性能问题的可能性
4. 包含大表连接、复杂子查询、排序、聚合等操作
5. 每个SQL前用注释说明它试图触发哪个风险点

请直接输出SQL语句，每个SQL语句之间用空行分隔。
"""
        
        response = self.llm_client.generate_response(prompt)
        if response:
            # 分割SQL语句
            sql_statements = []
            current_sql = []
            
            for line in response.split('\n'):
                line = line.strip()
                if line == '' and current_sql:
                    # 空行且当前有SQL内容，表示一个SQL结束
                    sql_statements.append('\n'.join(current_sql))
                    current_sql = []
                elif line:
                    current_sql.append(line)
            
            # 添加最后一个SQL
            if current_sql:
                sql_statements.append('\n'.join(current_sql))
            
            return sql_statements
        
        return []
    
    def save_generated_sql(self, function_name, sql_statements):
        """保存生成的SQL到文件"""
        if sql_statements:
            sql_file = os.path.join(self.sql_output_dir, f"{function_name}.sql")
            with open(sql_file, 'w', encoding='utf-8') as f:
                f.write(f"-- SQL queries designed to trigger performance issues in {function_name}\n")
                f.write(f"-- Generated based on identified performance risks\n\n")
                
                for i, sql in enumerate(sql_statements, 1):
                    f.write(f"-- Query {i}\n")
                    f.write(f"{sql}\n\n")
            
            print(f"为 {function_name} 生成了 {len(sql_statements)} 个SQL查询")
    
    def generate_sql_for_all_functions(self):
        """为所有有风险的函数生成SQL"""
        function_scores = self.load_function_scores()
        
        for function_name in function_scores.keys():
            print(f"为函数 {function_name} 生成SQL...")
            
            # 检查是否已经生成过
            sql_file = os.path.join(self.sql_output_dir, f"{function_name}.sql")
            if os.path.exists(sql_file):
                print(f"跳过已生成SQL的函数: {function_name}")
                continue
            
            risks = self.load_function_risks(function_name)
            
            if risks:
                sql_statements = self.generate_sql_for_function(function_name, risks)
                self.save_generated_sql(function_name, sql_statements)
            else:
                print(f"跳过无风险点的函数: {function_name}")

if __name__ == "__main__":
    generator = SQLGenerator()
    generator.generate_sql_for_all_functions()
