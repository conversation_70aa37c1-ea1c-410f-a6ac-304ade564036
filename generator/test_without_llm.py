#!/usr/bin/env python3
"""
不依赖LLM的测试脚本 - 验证生成器的基本功能
"""

import os
import json
import re

class MockAnalyzer:
    """模拟性能分析器，不使用LLM"""
    def __init__(self):
        self.sqlite_source_path = "../sqlite3.c"
        self.function_scores_path = "../tool/function_scores.json"
        self.risks_output_dir = "function_risks"
        
        # 确保输出目录存在
        os.makedirs(self.risks_output_dir, exist_ok=True)
    
    def load_function_scores(self):
        """加载函数性能评分数据"""
        with open(self.function_scores_path, 'r') as f:
            return json.load(f)
    
    def find_function_in_source(self, function_name):
        """在sqlite3.c中找到函数的源码和注释"""
        with open(self.sqlite_source_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 查找函数定义
        pattern = rf'SQLITE_PRIVATE\s+\w+\s+{re.escape(function_name)}\s*\('
        matches = list(re.finditer(pattern, content))
        
        if not matches:
            # 尝试查找static函数
            pattern = rf'static\s+\w+\s+{re.escape(function_name)}\s*\('
            matches = list(re.finditer(pattern, content))
        
        if not matches:
            return None, None
        
        # 取第一个匹配
        match = matches[0]
        func_start = match.start()
        
        # 向前查找注释
        lines = content[:func_start].split('\n')
        comment_lines = []
        
        # 从函数定义前开始向上查找注释
        for i in range(len(lines) - 1, -1, -1):
            line = lines[i].strip()
            if line.startswith('*/'):
                # 找到注释结束，继续向上找注释开始
                comment_lines.insert(0, line)
                for j in range(i - 1, -1, -1):
                    comment_line = lines[j].strip()
                    comment_lines.insert(0, comment_line)
                    if comment_line.startswith('/*'):
                        break
                break
            elif line == '' or line.startswith('//'):
                continue
            else:
                # 遇到非注释行，停止查找
                break
        
        # 提取函数实现
        func_lines = content[func_start:].split('\n')
        function_code = []
        brace_count = 0
        started = False
        
        for line in func_lines:
            function_code.append(line)
            for char in line:
                if char == '{':
                    brace_count += 1
                    started = True
                elif char == '}':
                    brace_count -= 1
            
            if started and brace_count == 0:
                break
        
        comment_text = '\n'.join(comment_lines) if comment_lines else ""
        function_text = '\n'.join(function_code)
        
        return comment_text, function_text

def test_function_loading():
    """测试函数加载功能"""
    print("测试1: 加载函数评分数据...")
    
    analyzer = MockAnalyzer()
    try:
        function_scores = analyzer.load_function_scores()
        print(f"✓ 成功加载 {len(function_scores)} 个函数")
        
        # 显示前5个函数
        functions = list(function_scores.keys())[:5]
        for func in functions:
            print(f"  - {func}: score={function_scores[func]['normalized_score']}")
        
        return True
    except Exception as e:
        print(f"✗ 加载失败: {e}")
        return False

def test_source_code_extraction():
    """测试源码提取功能"""
    print("\n测试2: 源码提取功能...")
    
    analyzer = MockAnalyzer()
    
    # 测试一个已知存在的函数
    test_function = "sqlite3BtreeNext"
    
    try:
        comment, source_code = analyzer.find_function_in_source(test_function)
        
        if source_code:
            print(f"✓ 成功提取 {test_function} 的源码")
            print(f"  注释长度: {len(comment) if comment else 0} 字符")
            print(f"  源码长度: {len(source_code)} 字符")
            
            # 显示源码的前几行
            lines = source_code.split('\n')[:3]
            for line in lines:
                print(f"  {line}")
            
            return True
        else:
            print(f"✗ 未找到 {test_function} 的源码")
            return False
            
    except Exception as e:
        print(f"✗ 源码提取失败: {e}")
        return False

def test_tpch_schema():
    """测试TPC-H数据库模式"""
    print("\n测试3: TPC-H数据库模式...")
    
    tpch_schema = """
TPC-H数据库模式包含以下表：

REGION (R_REGIONKEY, R_NAME, R_COMMENT)
NATION (N_NATIONKEY, N_NAME, N_REGIONKEY, N_COMMENT)
SUPPLIER (S_SUPPKEY, S_NAME, S_ADDRESS, S_NATIONKEY, S_PHONE, S_ACCTBAL, S_COMMENT)
CUSTOMER (C_CUSTKEY, C_NAME, C_ADDRESS, C_NATIONKEY, C_PHONE, C_ACCTBAL, C_MKTSEGMENT, C_COMMENT)
PART (P_PARTKEY, P_NAME, P_MFGR, P_BRAND, P_TYPE, P_SIZE, P_CONTAINER, P_RETAILPRICE, P_COMMENT)
PARTSUPP (PS_PARTKEY, PS_SUPPKEY, PS_AVAILQTY, PS_SUPPLYCOST, PS_COMMENT)
ORDERS (O_ORDERKEY, O_CUSTKEY, O_ORDERSTATUS, O_TOTALPRICE, O_ORDERDATE, O_ORDERPRIORITY, O_CLERK, O_SHIPPRIORITY, O_COMMENT)
LINEITEM (L_ORDERKEY, L_PARTKEY, L_SUPPKEY, L_LINENUMBER, L_QUANTITY, L_EXTENDEDPRICE, L_DISCOUNT, L_TAX, L_RETURNFLAG, L_LINESTATUS, L_SHIPDATE, L_COMMITDATE, L_RECEIPTDATE, L_SHIPINSTRUCT, L_SHIPMODE, L_COMMENT)
"""
    
    if tpch_schema:
        print("✓ TPC-H数据库模式加载成功")
        print(f"  模式描述长度: {len(tpch_schema)} 字符")
        
        # 检查是否包含主要表名
        tables = ['REGION', 'NATION', 'SUPPLIER', 'CUSTOMER', 'PART', 'PARTSUPP', 'ORDERS', 'LINEITEM']
        found_tables = [table for table in tables if table in tpch_schema]
        print(f"  包含表: {', '.join(found_tables)}")
        
        return len(found_tables) == len(tables)
    else:
        print("✗ 数据库模式为空")
        return False

def test_directory_creation():
    """测试目录创建功能"""
    print("\n测试4: 目录创建功能...")
    
    risks_dir = "function_risks"
    sql_dir = "../generated_sql"
    
    # 创建目录
    os.makedirs(risks_dir, exist_ok=True)
    os.makedirs(sql_dir, exist_ok=True)
    
    risks_exists = os.path.exists(risks_dir)
    sql_exists = os.path.exists(sql_dir)
    
    print(f"✓ 风险分析目录 ({risks_dir}): {'存在' if risks_exists else '不存在'}")
    print(f"✓ SQL输出目录 ({sql_dir}): {'存在' if sql_exists else '不存在'}")
    
    return risks_exists and sql_exists

def main():
    """运行所有测试"""
    print("=== 生成器功能测试（无LLM版本）===\n")
    
    tests = [
        test_function_loading,
        test_source_code_extraction,
        test_tpch_schema,
        test_directory_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果: {passed}/{total} 通过 ===")
    
    if passed == total:
        print("✓ 所有基础功能测试通过！")
        print("\n生成器组件说明：")
        print("1. llm_client.py - Gemini API客户端")
        print("2. analyze_performance.py - 分析函数性能风险")
        print("3. generate_sql.py - 生成SQL查询")
        print("4. run_generator.py - 主控制脚本")
        print("\n要运行完整的生成流程，请：")
        print("1. 安装依赖: pip install google-generativeai")
        print("2. 设置 GEMINI_API_KEY 环境变量")
        print("3. 运行: python3 run_generator.py")
    else:
        print("✗ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
