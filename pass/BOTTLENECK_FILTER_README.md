# 瓶颈函数过滤功能

## 概述
FunctionLogger pass现在支持两种函数过滤机制：

1. **预定义的SQL相关函数**：硬编码的SQLite关键性能函数列表
2. **CSV瓶颈函数**：从`bottleneck_analysis.csv`文件动态加载的函数列表

## 功能特性

### 自动加载CSV文件
- 默认从`bottleneck_analysis.csv`文件读取前100个函数
- 如果CSV文件不存在，会输出警告但继续执行
- 不影响预定义函数列表的功能

### 环境变量控制
可以通过环境变量`BOTTLENECK_TOP_N`自定义要读取的函数数量：

```bash
# 读取前50个函数
BOTTLENECK_TOP_N=50 opt -load-pass-plugin=./build/lib/libFunctionLoggerPass.so -passes="function-logger" -S input.ll -o output.ll

# 读取前200个函数
BOTTLENECK_TOP_N=200 opt -load-pass-plugin=./build/lib/libFunctionLoggerPass.so -passes="function-logger" -S input.ll -o output.ll
```

### CSV文件格式
CSV文件应包含以下格式：
```csv
Rank,Function_Name,File,Line,Bottleneck_Score,...
1,sqlite3VdbeExec,vdbe.c,814,70.46,...
2,sqlite3Pragma,pragma.c,409,46.60,...
```

只有第一列（Rank）和第二列（Function_Name）会被使用。

## 过滤逻辑
函数会被记录如果满足以下任一条件：
- 函数名在预定义的`sqlRelatedFunctions`集合中
- 函数名在从CSV文件加载的`bottleneckFunctions`集合中

## 使用示例

### 默认使用（前100个函数）
```bash
opt -load-pass-plugin=./build/lib/libFunctionLoggerPass.so -passes="function-logger" -S sqlite3.ll -o sqlite3_logged.ll
```

### 自定义函数数量
```bash
BOTTLENECK_TOP_N=50 opt -load-pass-plugin=./build/lib/libFunctionLoggerPass.so -passes="function-logger" -S sqlite3.ll -o sqlite3_logged.ll
```

### 只使用预定义函数（移除CSV文件）
```bash
mv bottleneck_analysis.csv bottleneck_analysis.csv.bak
opt -load-pass-plugin=./build/lib/libFunctionLoggerPass.so -passes="function-logger" -S sqlite3.ll -o sqlite3_logged.ll
mv bottleneck_analysis.csv.bak bottleneck_analysis.csv
```

## 输出信息
运行时会看到类似的输出：
```
Loaded 100 bottleneck functions from CSV (top 100)
```

或者如果CSV文件不存在：
```
Warning: Could not open bottleneck_analysis.csv
```
