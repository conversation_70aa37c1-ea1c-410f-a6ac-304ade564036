#include "llvm/Pass.h"
#include "llvm/IR/Function.h"
#include "llvm/IR/Module.h"
#include "llvm/IR/BasicBlock.h"
#include "llvm/IR/Instruction.h"
#include "llvm/IR/Instructions.h"
#include "llvm/IR/IRBuilder.h"
#include "llvm/IR/LLVMContext.h"
#include "llvm/IR/Type.h"
#include "llvm/IR/DerivedTypes.h"
#include "llvm/IR/Constants.h"
#include "llvm/Support/raw_ostream.h"
#include "llvm/Transforms/Utils/BasicBlockUtils.h"
#include "llvm/IR/PassManager.h"
#include "llvm/Passes/PassBuilder.h"
#include "llvm/Passes/PassPlugin.h"
#include "llvm/Support/Casting.h"
#include <cstdio>
#include <cstring>
#include <cstdlib>
#include <set>
#include <string>
#include <map>
#include <fstream>
#include <sstream>

using namespace llvm;

namespace {

class FunctionLogger : public PassInfoMixin<FunctionLogger> {
private:
    /*
     * SQL执行相关的性能关键函数列表 - 基于SQLite源码分析
     *
     * 这个列表包含了SQLite中最可能成为性能瓶颈的函数，按照SQLite的分层架构组织：
     *
     * 1. 公共API层：用户直接调用的主要接口
     * 2. VDBE层：虚拟数据库引擎，执行字节码的核心
     * 3. B-Tree层：存储引擎，处理数据的存储和检索
     * 4. Pager层：页面管理，处理磁盘I/O和缓存
     * 5. 解析器和编译器：SQL到字节码的转换
     * 6. 内存管理：频繁的内存分配和释放
     * 7. 哈希表和查找：元数据查找和缓存
     * 8. 表达式处理：WHERE子句和计算
     * 9. 排序和聚合：ORDER BY和GROUP BY操作
     * 10. 锁和并发控制：事务处理
     * 11. 索引操作：索引的维护和查询
     *
     * 重点关注的性能瓶颈函数：
     * - sqlite3VdbeExec: VDBE执行循环，所有SQL操作的核心
     * - sqlite3BtreeNext/Previous: 表扫描，高频调用
     * - sqlite3PagerGet: 页面获取，磁盘I/O的主要入口
     * - sqlite3BtreeInsert/Delete: 数据修改操作
     * - sqlite3PagerWrite: 页面写入，影响事务性能
     * - sqlite3HashFind: 元数据查找，影响编译性能
     * - 内存管理函数：频繁分配释放影响整体性能
     */
    std::set<std::string> sqlRelatedFunctions = {

        "sqlite3BtreeOpen",             // 打开B-tree
        "sqlite3BtreeClose",            // 关闭B-tree
        "sqlite3BtreeCursor",           // 创建游标
        "sqlite3BtreeCloseCursor",      // 关闭游标
        "sqlite3BtreeNext",             // 移动到下一条记录
        "sqlite3BtreePrevious",         // 移动到上一条记录
        "sqlite3BtreeFirst",            // 移动到第一条记录
        "sqlite3BtreeLast",             // 移动到最后一条记录
        "sqlite3BtreeMovetoUnpacked",   // 定位到指定键
        "sqlite3BtreeInsert",           // 插入记录
        "sqlite3BtreeDelete",           // 删除记录
        "sqlite3BtreeData",             // 读取数据
        "sqlite3BtreeDataSize",         // 获取数据大小
        "sqlite3BtreeKey",              // 读取键
        "sqlite3BtreeKeySize",          // 获取键大小
        "sqlite3BtreePayload",          // 获取负载数据
        "sqlite3BtreePayloadChecked",   // 安全获取负载数据
        "sqlite3BtreeIntegerKey",       // 获取整数键
        "sqlite3BtreeBeginTrans",       // 开始事务
        "sqlite3BtreeCommit",           // 提交事务
        "sqlite3BtreeRollback",         // 回滚事务
        "sqlite3BtreeSavepoint",        // 保存点操作

        "sqlite3PagerOpen",             // 打开页面管理器
        "sqlite3PagerClose",            // 关闭页面管理器
        "sqlite3PagerGet",              // 获取页面
        "sqlite3PagerRef",              // 页面引用
        "sqlite3PagerUnref",            // 页面解引用
        "sqlite3PagerWrite",            // 写页面
        "sqlite3PagerBegin",            // 开始页面事务
        "sqlite3PagerCommitPhaseOne",   // 提交阶段1
        "sqlite3PagerCommitPhaseTwo",   // 提交阶段2
        "sqlite3PagerRollback",         // 页面回滚
        "sqlite3PagerSavepoint",        // 页面保存点
        "sqlite3PagerSharedLock",       // 共享锁
        "sqlite3PagerExclusiveLock",    // 排他锁
        "sqlite3PagerUnlock",           // 解锁
        "sqlite3PagerSync",             // 同步到磁盘
        "sqlite3PagerPagecount",        // 页面计数

        "sqlite3Malloc",                // 内存分配
        "sqlite3MallocZero",            // 零初始化分配
        "sqlite3Realloc",               // 重新分配
        "sqlite3DbMalloc",              // 数据库内存分配
        "sqlite3DbMallocZero",          // 数据库零初始化分配
        "sqlite3DbRealloc",             // 数据库重新分配
        "sqlite3DbFree",                // 数据库内存释放
        "sqlite3MemCompare",            // 内存比较
        "sqlite3MemMove",               // 内存移动

        "sqlite3HashInit",              // 哈希表初始化
        "sqlite3HashInsert",            // 哈希表插入
        "sqlite3HashFind",              // 哈希表查找
        "sqlite3SrcListLookup",         // 源列表查找
        "sqlite3LocateTable",           // 定位表
        "sqlite3LocateTableItem",       // 定位表项

        "sqlite3ExprCode",              // 表达式编译
        "sqlite3ExprCodeTarget",        // 表达式编译到目标
        "sqlite3ExprEvaluate",          // 表达式求值
        "sqlite3ExprCompare",           // 表达式比较
        "sqlite3ExprCollSeq",           // 表达式排序规则

        "sqlite3VdbeSorterInit",        // 排序器初始化
        "sqlite3VdbeSorterWrite",       // 排序器写入
        "sqlite3VdbeSorterRewind",      // 排序器重置
        "sqlite3VdbeSorterNext",        // 排序器下一个
        "sqlite3VdbeSorterRowkey",      // 排序器行键
        "sqlite3VdbeSorterCompare",     // 排序器比较

        "sqlite3BtreeBeginStmt",        // 开始语句
        "sqlite3BtreeCommitStmt",       // 提交语句
        "sqlite3BtreeRollbackStmt",     // 回滚语句
        "sqlite3BtreeLockTable",        // 锁定表
        "sqlite3BtreeUnlockTable",      // 解锁表

        //索引
        "sqlite3BtreeIndexMoveto",      // 索引定位
        "sqlite3BtreeIndexNext",        // 索引下一个
        "sqlite3BtreeIndexPrev",        // 索引上一个
        "sqlite3BtreeIndexInsert",      // 索引插入
        "sqlite3BtreeIndexDelete"       // 索引删除
    };

    // 从bottleneck_analysis.csv文件读取的函数名集合
    std::set<std::string> bottleneckFunctions;
    
    // 默认从CSV文件读取的函数数量
    static const int DEFAULT_TOP_N_FUNCTIONS = 100;

    // 从CSV文件加载瓶颈函数列表
    void loadBottleneckFunctions(int topN = DEFAULT_TOP_N_FUNCTIONS) {
        // 检查环境变量是否设置了自定义的数量
        const char* envTopN = std::getenv("BOTTLENECK_TOP_N");
        if (envTopN) {
            int customTopN = std::atoi(envTopN);
            if (customTopN > 0) {
                topN = customTopN;
            }
        }
        
        std::ifstream file("bottleneck_analysis.csv");
        if (!file.is_open()) {
            // 如果文件不存在，记录错误但继续执行
            llvm::errs() << "Warning: Could not open bottleneck_analysis.csv\n";
            return;
        }

        std::string line;
        // 跳过标题行
        if (std::getline(file, line)) {
            int count = 0;
            while (std::getline(file, line) && count < topN) {
                std::stringstream ss(line);
                std::string rank, funcName;
                
                // 读取排名（第一列）
                if (std::getline(ss, rank, ',')) {
                    // 读取函数名（第二列）
                    if (std::getline(ss, funcName, ',')) {
                        // 移除可能的引号和空格
                        if (!funcName.empty() && funcName.front() == '"') {
                            funcName = funcName.substr(1);
                        }
                        if (!funcName.empty() && funcName.back() == '"') {
                            funcName.pop_back();
                        }
                        
                        if (!funcName.empty()) {
                            bottleneckFunctions.insert(funcName);
                            count++;
                        }
                    }
                }
            }
        }
        
        file.close();
        llvm::errs() << "Loaded " << bottleneckFunctions.size() << " bottleneck functions from CSV (top " << topN << ")\n";
    }

    bool isPerformanceCriticalFunction(const std::string& funcName) {
        // 检查是否在预定义的SQL相关函数集合中
        if (sqlRelatedFunctions.find(funcName) != sqlRelatedFunctions.end()) {
            return true;
        }
        
        // 检查是否在从CSV文件读取的瓶颈函数集合中
        if (bottleneckFunctions.find(funcName) != bottleneckFunctions.end()) {
            return true;
        }
        
        return false;
    }

public:
    PreservedAnalyses run(Module &M, ModuleAnalysisManager &AM) {
        LLVMContext &Context = M.getContext();
        
        // 加载瓶颈函数列表
        loadBottleneckFunctions();
        
        // 创建日志函数声明
        Type *Int8PtrTy = PointerType::getUnqual(Type::getInt8Ty(Context));
        Type *Int64Ty = Type::getInt64Ty(Context);

        // log_function_entry(const char* func_name, int64_t timestamp)
        FunctionType *LogEntryFuncType = FunctionType::get(
            Type::getVoidTy(Context),
            ArrayRef<Type*>{Int8PtrTy, Int64Ty},
            false
        );

        // log_function_exit(const char* func_name, int64_t timestamp)
        FunctionType *LogExitFuncType = FunctionType::get(
            Type::getVoidTy(Context),
            ArrayRef<Type*>{Int8PtrTy, Int64Ty},
            false
        );

        FunctionCallee LogEntryFunc = M.getOrInsertFunction("log_function_entry", LogEntryFuncType);
        FunctionCallee LogExitFunc = M.getOrInsertFunction("log_function_exit", LogExitFuncType);
        
        bool Modified = false;
        
        // 遍历模块中的所有函数
        for (Function &F : M) {
            if (F.isDeclaration()) continue;
            if (F.getName() == "log_function_call") continue;

            // 只记录关键函数
            std::string funcName = F.getName().str();
            if (!isPerformanceCriticalFunction(funcName)) {
                continue;
            }

            // 在函数入口插入日志调用
            BasicBlock &EntryBB = F.getEntryBlock();
            IRBuilder<> EntryBuilder(&EntryBB, EntryBB.begin());

            // 创建函数名字符串
            Value *FuncName = EntryBuilder.CreateGlobalStringPtr(F.getName());

            // 获取当前时间戳并插入入口日志调用
            Value *EntryTimestamp = getCurrentTimestamp(EntryBuilder, M, Context);
            EntryBuilder.CreateCall(LogEntryFunc, {FuncName, EntryTimestamp});

            // 在所有返回指令前插入出口日志调用
            for (BasicBlock &BB : F) {
                for (Instruction &I : BB) {
                    if (isa<ReturnInst>(&I)) {
                        IRBuilder<> ExitBuilder(&I);
                        Value *ExitTimestamp = getCurrentTimestamp(ExitBuilder, M, Context);
                        ExitBuilder.CreateCall(LogExitFunc, {FuncName, ExitTimestamp});
                    }
                }
            }

            Modified = true;
        }
        
        // 如果修改了模块，添加日志函数的实现
        if (Modified) {
            addInitFunction(M, Context);
            addLogEntryFunction(M, Context);
            addLogExitFunction(M, Context);
            addGetCurrentTimestampFunction(M, Context);
        }

        return Modified ? PreservedAnalyses::none() : PreservedAnalyses::all();
    }

private:
    // 辅助函数：计算字符串的简单哈希值
    Value* computeSimpleHash(IRBuilder<> &Builder, Value *Str, LLVMContext &Context) {
        Type *Int32Ty = Type::getInt32Ty(Context);
        Type *Int8Ty = Type::getInt8Ty(Context);
        Type *Int8PtrTy = PointerType::getUnqual(Int8Ty);

        // 使用一个更简单的方法：直接将指针转换为整数作为哈希值
        Value *PtrInt = Builder.CreatePtrToInt(Str, Int32Ty);

        // 为了避免冲突，我们可以对指针值进行一些简单的变换
        Value *Hash1 = Builder.CreateMul(PtrInt, ConstantInt::get(Int32Ty, 31));
        Value *Hash2 = Builder.CreateXor(Hash1, ConstantInt::get(Int32Ty, 0x12345678));

        return Hash2;
    }

    // 辅助函数：获取当前时间戳（毫秒）
    Value* getCurrentTimestamp(IRBuilder<> &Builder, Module &M, LLVMContext &Context) {
        // 获取get_current_timestamp函数
        Type *Int64Ty = Type::getInt64Ty(Context);
        FunctionType *GetTimestampType = FunctionType::get(Int64Ty, ArrayRef<Type*>{}, false);
        FunctionCallee GetTimestampFunc = M.getOrInsertFunction("get_current_timestamp", GetTimestampType);

        return Builder.CreateCall(GetTimestampFunc, {});
    }

    void addGetCurrentTimestampFunction(Module &M, LLVMContext &Context) {
        Function *GetTimestampFunc = M.getFunction("get_current_timestamp");
        if (!GetTimestampFunc || !GetTimestampFunc->isDeclaration()) return;

        Type *Int64Ty = Type::getInt64Ty(Context);
        Type *Int32Ty = Type::getInt32Ty(Context);

        // 声明gettimeofday
        StructType *TimevalType = StructType::create(Context, {Int64Ty, Int64Ty}, "struct.timeval");
        Type *TimevalPtrTy = PointerType::getUnqual(TimevalType);
        Type *Int8PtrTy = PointerType::getUnqual(Type::getInt8Ty(Context));
        FunctionType *GettimeofdayType = FunctionType::get(Int32Ty, ArrayRef<Type*>{TimevalPtrTy, Int8PtrTy}, false);
        FunctionCallee GettimeofdayFunc = M.getOrInsertFunction("gettimeofday", GettimeofdayType);

        // 创建函数实现
        FunctionType *GetTimestampFuncType = FunctionType::get(Int64Ty, ArrayRef<Type*>{}, false);
        Function *GetTimestampFuncImpl = Function::Create(GetTimestampFuncType, Function::ExternalLinkage,
                                                         "get_current_timestamp", &M);

        GetTimestampFunc->replaceAllUsesWith(GetTimestampFuncImpl);
        GetTimestampFunc->eraseFromParent();

        BasicBlock *BB = BasicBlock::Create(Context, "entry", GetTimestampFuncImpl);
        IRBuilder<> Builder(BB);

        // 创建timeval结构体实例
        Value *TimevalPtr = Builder.CreateAlloca(TimevalType);
        Value *NullPtr = ConstantPointerNull::get(PointerType::getUnqual(Type::getInt8Ty(Context)));

        // 调用gettimeofday获取当前时间
        Builder.CreateCall(GettimeofdayFunc, {TimevalPtr, NullPtr});

        // 获取秒和微秒
        Value *SecPtr = Builder.CreateStructGEP(TimevalType, TimevalPtr, 0);
        Value *UsecPtr = Builder.CreateStructGEP(TimevalType, TimevalPtr, 1);
        Value *Seconds = Builder.CreateLoad(Int64Ty, SecPtr);
        Value *Microseconds = Builder.CreateLoad(Int64Ty, UsecPtr);

        // 计算毫秒时间戳: seconds * 1000 + microseconds / 1000
        Value *SecondsInMs = Builder.CreateMul(Seconds, ConstantInt::get(Int64Ty, 1000));
        Value *MicrosecondsInMs = Builder.CreateUDiv(Microseconds, ConstantInt::get(Int64Ty, 1000));
        Value *TimestampMs = Builder.CreateAdd(SecondsInMs, MicrosecondsInMs);

        Builder.CreateRet(TimestampMs);
    }

    void addLogEntryFunction(Module &M, LLVMContext &Context) {
        Function *LogEntryFunc = M.getFunction("log_function_entry");
        if (!LogEntryFunc || !LogEntryFunc->isDeclaration()) return;

        Type *VoidTy = Type::getVoidTy(Context);
        Type *Int8PtrTy = PointerType::getUnqual(Type::getInt8Ty(Context));
        Type *Int64Ty = Type::getInt64Ty(Context);
        Type *Int32Ty = Type::getInt32Ty(Context);

        // 声明需要的C库函数
        FunctionType *FopenType = FunctionType::get(Int8PtrTy, ArrayRef<Type*>{Int8PtrTy, Int8PtrTy}, false);
        FunctionCallee FopenFunc = M.getOrInsertFunction("fopen", FopenType);

        FunctionType *FcloseType = FunctionType::get(Int32Ty, ArrayRef<Type*>{Int8PtrTy}, false);
        FunctionCallee FcloseFunc = M.getOrInsertFunction("fclose", FcloseType);

        FunctionType *FprintfType = FunctionType::get(Int32Ty, ArrayRef<Type*>{Int8PtrTy, Int8PtrTy}, true);
        FunctionCallee FprintfFunc = M.getOrInsertFunction("fprintf", FprintfType);

        FunctionType *FflushType = FunctionType::get(Int32Ty, ArrayRef<Type*>{Int8PtrTy}, false);
        FunctionCallee FflushFunc = M.getOrInsertFunction("fflush", FflushType);

        // 创建log_function_entry函数的实现
        FunctionType *LogEntryFuncType = FunctionType::get(VoidTy, ArrayRef<Type*>{Int8PtrTy, Int64Ty}, false);
        Function *LogEntryFuncImpl = Function::Create(LogEntryFuncType, Function::ExternalLinkage,
                                                     "log_function_entry", &M);

        LogEntryFunc->replaceAllUsesWith(LogEntryFuncImpl);
        LogEntryFunc->eraseFromParent();

        BasicBlock *BB = BasicBlock::Create(Context, "entry", LogEntryFuncImpl);
        IRBuilder<> Builder(BB);

        Value *FuncName = LogEntryFuncImpl->getArg(0);
        Value *EntryTimestamp = LogEntryFuncImpl->getArg(1);

        // 使用一个简单的方法：将入口时间戳写入文件，在exit时读取
        // 这样避免了哈希冲突的问题
        Value *EntryFileName = Builder.CreateGlobalStringPtr("current_entry.tmp");
        Value *WriteMode = Builder.CreateGlobalStringPtr("w");
        Value *EntryFilePtr = Builder.CreateCall(FopenFunc, {EntryFileName, WriteMode});

        Value *EntryFormat = Builder.CreateGlobalStringPtr("%s:%ld\n");
        Builder.CreateCall(FprintfFunc, {EntryFilePtr, EntryFormat, FuncName, EntryTimestamp});
        Builder.CreateCall(FflushFunc, {EntryFilePtr});
        Builder.CreateCall(FcloseFunc, {EntryFilePtr});

        // 为了调试，我们也将入口信息写入一个单独的文件
        Value *FileName = Builder.CreateGlobalStringPtr("function_entries.json");
        Value *FileMode = Builder.CreateGlobalStringPtr("a");
        Value *FilePtr = Builder.CreateCall(FopenFunc, {FileName, FileMode});

        Value *JsonFormat = Builder.CreateGlobalStringPtr(
            "{\"function\":\"%s\",\"entry_timestamp\":%ld}\n");

        Builder.CreateCall(FprintfFunc, {FilePtr, JsonFormat, FuncName, EntryTimestamp});
        Builder.CreateCall(FflushFunc, {FilePtr});
        Builder.CreateCall(FcloseFunc, {FilePtr});

        Builder.CreateRetVoid();
    }

    void addLogExitFunction(Module &M, LLVMContext &Context) {
        Function *LogExitFunc = M.getFunction("log_function_exit");
        if (!LogExitFunc || !LogExitFunc->isDeclaration()) return;

        Type *VoidTy = Type::getVoidTy(Context);
        Type *Int8PtrTy = PointerType::getUnqual(Type::getInt8Ty(Context));
        Type *Int32Ty = Type::getInt32Ty(Context);
        Type *Int64Ty = Type::getInt64Ty(Context);

        // 声明需要的C库函数
        FunctionType *FopenType = FunctionType::get(Int8PtrTy, ArrayRef<Type*>{Int8PtrTy, Int8PtrTy}, false);
        FunctionCallee FopenFunc = M.getOrInsertFunction("fopen", FopenType);

        FunctionType *FcloseType = FunctionType::get(Int32Ty, ArrayRef<Type*>{Int8PtrTy}, false);
        FunctionCallee FcloseFunc = M.getOrInsertFunction("fclose", FcloseType);

        FunctionType *FprintfType = FunctionType::get(Int32Ty, ArrayRef<Type*>{Int8PtrTy, Int8PtrTy}, true);
        FunctionCallee FprintfFunc = M.getOrInsertFunction("fprintf", FprintfType);

        FunctionType *FflushType = FunctionType::get(Int32Ty, ArrayRef<Type*>{Int8PtrTy}, false);
        FunctionCallee FflushFunc = M.getOrInsertFunction("fflush", FflushType);

        // 创建log_function_exit函数的实现
        FunctionType *LogExitFuncType = FunctionType::get(VoidTy, ArrayRef<Type*>{Int8PtrTy, Int64Ty}, false);
        Function *LogExitFuncImpl = Function::Create(LogExitFuncType, Function::ExternalLinkage,
                                                    "log_function_exit", &M);

        LogExitFunc->replaceAllUsesWith(LogExitFuncImpl);
        LogExitFunc->eraseFromParent();

        BasicBlock *BB = BasicBlock::Create(Context, "entry", LogExitFuncImpl);
        IRBuilder<> Builder(BB);

        Value *FuncName = LogExitFuncImpl->getArg(0);
        Value *ExitTimestamp = LogExitFuncImpl->getArg(1);

        // 从临时文件读取入口时间戳
        // 声明需要的C库函数
        FunctionType *FgetsType = FunctionType::get(Int8PtrTy, ArrayRef<Type*>{Int8PtrTy, Int32Ty, Int8PtrTy}, false);
        FunctionCallee FgetsFunc = M.getOrInsertFunction("fgets", FgetsType);

        FunctionType *SscanfType = FunctionType::get(Int32Ty, ArrayRef<Type*>{Int8PtrTy, Int8PtrTy}, true);
        FunctionCallee SscanfFunc = M.getOrInsertFunction("sscanf", SscanfType);

        FunctionType *StrstrType = FunctionType::get(Int8PtrTy, ArrayRef<Type*>{Int8PtrTy, Int8PtrTy}, false);
        FunctionCallee StrstrFunc = M.getOrInsertFunction("strstr", StrstrType);

        // 读取临时文件
        Value *EntryFileName = Builder.CreateGlobalStringPtr("current_entry.tmp");
        Value *ReadMode = Builder.CreateGlobalStringPtr("r");
        Value *EntryFilePtr = Builder.CreateCall(FopenFunc, {EntryFileName, ReadMode});

        // 分配缓冲区读取文件内容
        Value *BufferSize = ConstantInt::get(Int32Ty, 1024);
        Value *Buffer = Builder.CreateAlloca(Type::getInt8Ty(Context), BufferSize);
        Value *ReadResult = Builder.CreateCall(FgetsFunc, {Buffer, BufferSize, EntryFilePtr});
        Builder.CreateCall(FcloseFunc, {EntryFilePtr});

        // 查找当前函数名
        Value *ColonPos = Builder.CreateCall(StrstrFunc, {Buffer, Builder.CreateGlobalStringPtr(":")});

        // 分配空间存储解析出的时间戳
        Value *EntryTimestampPtr = Builder.CreateAlloca(Int64Ty);
        Builder.CreateStore(ConstantInt::get(Int64Ty, 0), EntryTimestampPtr);

        // 解析时间戳 (简化版本，假设文件格式正确)
        Value *ScanFormat = Builder.CreateGlobalStringPtr("%*[^:]:%ld");
        Builder.CreateCall(SscanfFunc, {Buffer, ScanFormat, EntryTimestampPtr});
        Value *EntryTimestamp = Builder.CreateLoad(Int64Ty, EntryTimestampPtr);

        // 计算执行时间（毫秒）
        Value *ExecutionTime = Builder.CreateSub(ExitTimestamp, EntryTimestamp);

        // 检查执行时间是否大于等于2ms
        Value *Threshold = ConstantInt::get(Int64Ty, 1);
        Value *ShouldLog = Builder.CreateICmpUGE(ExecutionTime, Threshold);

        // 创建条件分支
        BasicBlock *LogBB = BasicBlock::Create(Context, "log", LogExitFuncImpl);
        BasicBlock *ExitBB = BasicBlock::Create(Context, "exit", LogExitFuncImpl);

        Builder.CreateCondBr(ShouldLog, LogBB, ExitBB);

        // 实现日志记录分支
        Builder.SetInsertPoint(LogBB);

        Value *FileName = Builder.CreateGlobalStringPtr("function_calls.json");
        Value *FileMode = Builder.CreateGlobalStringPtr("a");
        Value *FilePtr = Builder.CreateCall(FopenFunc, {FileName, FileMode});

        Value *JsonFormat = Builder.CreateGlobalStringPtr(
            "{\"function\":\"%s\",\"entry_timestamp\":%ld,\"exit_timestamp\":%ld,\"execution_time_ms\":%ld}\n");

        Builder.CreateCall(FprintfFunc, {FilePtr, JsonFormat, FuncName, EntryTimestamp, ExitTimestamp, ExecutionTime});
        Builder.CreateCall(FflushFunc, {FilePtr});
        Builder.CreateCall(FcloseFunc, {FilePtr});

        Builder.CreateBr(ExitBB);

        // 实现退出分支
        Builder.SetInsertPoint(ExitBB);
        Builder.CreateRetVoid();
    }

    void addInitFunction(Module &M, LLVMContext &Context) {
        // 获取必要的类型
        Type *VoidTy = Type::getVoidTy(Context);
        Type *Int8PtrTy = PointerType::getUnqual(Type::getInt8Ty(Context));
        Type *Int32Ty = Type::getInt32Ty(Context);

        // 声明需要的C库函数
        FunctionType *FopenType = FunctionType::get(Int8PtrTy, ArrayRef<Type*>{Int8PtrTy, Int8PtrTy}, false);
        FunctionCallee FopenFunc = M.getOrInsertFunction("fopen", FopenType);

        FunctionType *FcloseType = FunctionType::get(Int32Ty, ArrayRef<Type*>{Int8PtrTy}, false);
        FunctionCallee FcloseFunc = M.getOrInsertFunction("fclose", FcloseType);

        // 创建初始化函数
        FunctionType *InitFuncType = FunctionType::get(VoidTy, ArrayRef<Type*>{}, false);
        Function *InitFunc = Function::Create(InitFuncType, Function::ExternalLinkage,
                                            "init_json_log", &M);

        // 创建函数体
        BasicBlock *BB = BasicBlock::Create(Context, "entry", InitFunc);
        IRBuilder<> Builder(BB);

        // 创建文件名和模式字符串
        Value *FileName = Builder.CreateGlobalStringPtr("function_calls.json");
        Value *FileMode = Builder.CreateGlobalStringPtr("w");  // write mode (overwrite)

        // 打开文件并立即关闭，用于清空文件
        Value *FilePtr = Builder.CreateCall(FopenFunc, {FileName, FileMode});
        Builder.CreateCall(FcloseFunc, {FilePtr});

        // 返回
        Builder.CreateRetVoid();
    }

};

} // namespace

// 注册Pass插件
extern "C" LLVM_ATTRIBUTE_WEAK ::llvm::PassPluginLibraryInfo
llvmGetPassPluginInfo() {
    return {
        LLVM_PLUGIN_API_VERSION, "FunctionLogger", LLVM_VERSION_STRING,
        [](PassBuilder &PB) {
            PB.registerPipelineParsingCallback(
                [](StringRef Name, ModulePassManager &MPM,
                   ArrayRef<PassBuilder::PipelineElement>) {
                    if (Name == "function-logger") {
                        MPM.addPass(FunctionLogger());
                        return true;
                    }
                    return false;
                });
        }};
}
