-- 简单查询测试
-- 测试基本的SELECT、WHERE、ORDER BY操作

-- Q1: 查询所有客户信息
SELECT * FROM CUSTOMER LIMIT 10;

-- Q2: 根据客户ID查询特定客户
SELECT * FROM CUSTOMER WHERE "1" = '1000';

-- Q3: 查询客户总数
SELECT COUNT(*) as customer_count FROM CUSTOMER;

-- Q4: 查询订单状态分布
SELECT "3" as order_status, COUNT(*) as order_count 
FROM ORDERS 
GROUP BY "3"
ORDER BY order_count DESC;

-- Q5: 查询订单按日期排序
SELECT "1", "2", "3", "4", "5" 
FROM ORDERS 
ORDER BY "5" 
LIMIT 20;

-- Q6: 查询高价值订单
SELECT "1", "2", "4" 
FROM ORDERS 
WHERE CAST("4" AS REAL) > 200000 
ORDER BY CAST("4" AS REAL) DESC;

-- Q7: 查询订单总价值
SELECT SUM(CAST("4" AS REAL)) as total_revenue FROM ORDERS;

-- Q8: 查询平均订单价值
SELECT AVG(CAST("4" AS REAL)) as avg_order_value FROM ORDERS;

-- Q9: 查询订单价值范围统计
SELECT 
    CASE 
        WHEN CAST("4" AS REAL) < 50000 THEN 'Low'
        WHEN CAST("4" AS REAL) < 150000 THEN 'Medium'
        ELSE 'High'
    END as price_category,
    COUNT(*) as order_count
FROM ORDERS
GROUP BY 
    CASE 
        WHEN CAST("4" AS REAL) < 50000 THEN 'Low'
        WHEN CAST("4" AS REAL) < 150000 THEN 'Medium'
        ELSE 'High'
    END;

-- Q10: 查询特定日期范围的订单
SELECT * FROM ORDERS 
WHERE "5" >= '1995-01-01' AND "5" <= '1995-12-31'
LIMIT 100;
