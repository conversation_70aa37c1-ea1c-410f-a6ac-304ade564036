-- 测试脚本执行器
-- 用于批量执行所有测试SQL文件的脚本

-- 使用说明：
-- 1. 在pass目录下运行：sqlite3 tpch.db < test_sql/run_all_tests.sql
-- 2. 或者使用shell脚本逐个执行测试文件

.print "=== 开始执行TPC-H数据库测试查询 ==="
.print "数据库：tpch.db"
.print "测试时间：" 
.shell date

.print ""
.print "=== 基础信息查询 ==="
.print "表数量："
SELECT COUNT(*) as table_count FROM sqlite_master WHERE type='table';

.print "CUSTOMER表记录数："
SELECT COUNT(*) FROM CUSTOMER;

.print "ORDERS表记录数："
SELECT COUNT(*) FROM ORDERS;

.print "LINEITEM表记录数："
SELECT COUNT(*) FROM LINEITEM;

.print "PART表记录数："
SELECT COUNT(*) FROM PART;

.print "SUPPLIER表记录数："
SELECT COUNT(*) FROM SUPPLIER;

.print "PARTSUPP表记录数："
SELECT COUNT(*) FROM PARTSUPP;

.print ""
.print "=== 执行简单查询测试 (01_simple_queries.sql) ==="
.read test_sql/01_simple_queries.sql

.print ""
.print "=== 执行连接查询测试 (02_join_queries.sql) ==="
.read test_sql/02_join_queries.sql

.print ""
.print "=== 执行聚合查询测试 (03_aggregation_queries.sql) ==="
.read test_sql/03_aggregation_queries.sql

.print ""
.print "=== 执行复杂查询测试 (04_complex_queries.sql) ==="
.read test_sql/04_complex_queries.sql

.print ""
.print "=== 执行性能测试查询 (05_performance_queries.sql) ==="
.read test_sql/05_performance_queries.sql

.print ""
.print "=== 执行TPC-H标准查询 (06_tpch_standard_queries.sql) ==="
.read test_sql/06_tpch_standard_queries.sql

.print ""
.print "=== 所有测试查询执行完成 ==="
.shell date
