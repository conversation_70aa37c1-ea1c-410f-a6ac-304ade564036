# TPC-H 数据库测试 SQL 文件

这个目录包含了专门为TPC-H数据库环境设计的测试SQL文件，用于测试SQLite的各种功能和性能。

## 文件说明

### 测试SQL文件

1. **01_simple_queries.sql** - 简单查询测试
   - 基本的SELECT、WHERE、ORDER BY操作
   - 单表查询和简单聚合
   - 适合初始测试和验证数据完整性

2. **02_join_queries.sql** - 连接查询测试
   - 各种类型的JOIN操作
   - 多表关联查询
   - 测试JOIN算法的性能

3. **03_aggregation_queries.sql** - 聚合和分组查询测试
   - GROUP BY和HAVING子句
   - 各种聚合函数（COUNT、SUM、AVG、MIN、MAX）
   - 复杂的分组统计

4. **04_complex_queries.sql** - 复杂查询测试
   - 子查询和嵌套查询
   - CTE（通用表表达式）
   - 复杂的业务逻辑查询

5. **05_performance_queries.sql** - 性能测试查询
   - 专门设计用于测试性能瓶颈
   - 大数据量扫描和处理
   - 复杂的排序和聚合操作

6. **06_tpch_standard_queries.sql** - TPC-H标准查询
   - 基于TPC-H基准测试的标准查询
   - 适配当前数据库格式
   - 行业标准的性能测试

### 执行脚本

- **run_all_tests.sql** - SQLite脚本，批量执行所有测试
- **run_tests.sh** - 交互式Shell脚本，提供菜单选择执行测试

## 数据库表结构

当前TPC-H数据库包含以下表：

- **CUSTOMER** - 客户信息
- **ORDERS** - 订单信息  
- **LINEITEM** - 订单项详情
- **PART** - 零件信息
- **SUPPLIER** - 供应商信息
- **PARTSUPP** - 零件供应关系
- **NATION** - 国家信息
- **REGION** - 地区信息

## 使用方法

### 方法1：使用交互式脚本（推荐）

```bash
cd /home/<USER>/sqlite_loger/pass
./test_sql/run_tests.sh
```

这将启动一个交互式菜单，允许你选择要执行的测试类型。

### 方法2：直接执行SQL文件

```bash
cd /home/<USER>/sqlite_loger/pass

# 执行单个测试文件
sqlite3 tpch.db < test_sql/01_simple_queries.sql

# 执行所有测试
sqlite3 tpch.db < test_sql/run_all_tests.sql
```

### 方法3：逐个执行查询

```bash
cd /home/<USER>/sqlite_loger/pass
sqlite3 tpch.db

# 在SQLite命令行中
.read test_sql/01_simple_queries.sql
```

## 性能测试建议

1. **基础验证**：先运行简单查询测试，确保数据完整性
2. **功能测试**：依次执行连接查询和聚合查询测试
3. **性能测试**：使用性能测试查询来识别瓶颈
4. **标准对比**：运行TPC-H标准查询进行行业对比

## 日志和结果

使用`run_tests.sh`脚本时，测试结果会保存在`test_logs/`目录中：

- 每个测试会生成独立的日志文件
- 记录执行时间和结果
- 便于后续性能分析

## 注意事项

1. **列名映射**：由于数据格式的特殊性，查询中使用了数字列名（如"1"、"2"等）
2. **数据类型**：需要使用CAST函数进行数据类型转换
3. **性能影响**：某些复杂查询可能需要较长时间执行
4. **资源使用**：大型聚合查询可能消耗大量内存

## 扩展

你可以根据需要：

1. 添加新的测试查询到相应的SQL文件中
2. 创建新的测试文件针对特定功能
3. 修改查询参数来测试不同的数据集
4. 结合函数日志记录来分析性能瓶颈

## 与函数记录器的结合使用

这些测试SQL文件特别适合与FunctionLogger pass结合使用：

1. 先使用FunctionLogger编译SQLite
2. 运行这些测试查询
3. 分析生成的函数调用日志
4. 识别性能瓶颈函数

这样可以深入了解SQLite在处理不同类型查询时的内部行为。
