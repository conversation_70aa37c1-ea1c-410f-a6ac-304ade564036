-- TPC-H标准查询（简化版本）
-- 基于TPC-H基准测试的标准查询，适配当前数据格式

-- TPC-H Q1: 定价汇总报告查询（简化版）
SELECT 
    L."10" as return_flag,
    L."9" as line_status,
    SUM(CAST(L."5" AS REAL)) as sum_qty,
    SUM(CAST(L."6" AS REAL)) as sum_base_price,
    SUM(CAST(L."6" AS REAL) * (1 - CAST(L."7" AS REAL))) as sum_disc_price,
    COUNT(*) as count_order
FROM LINEITEM L
WHERE L."11" <= '1996-12-01'
GROUP BY L."10", L."9"
ORDER BY L."10", L."9";

-- TPC-H Q3: 运输优先级查询（简化版）
SELECT 
    O."1" as order_id,
    O."4" as revenue,
    O."5" as order_date,
    O."6" as ship_priority
FROM CUSTOMER C
JOIN ORDERS O ON C."1" = O."2"
WHERE C."7" = 'BUILDING'
  AND O."5" < '1995-03-15'
ORDER BY CAST(O."4" AS REAL) DESC, O."5"
LIMIT 10;

-- TPC-H Q5: 本地供应商收入查询（简化版）
SELECT 
    N."0_1" as nation,
    SUM(CAST(L."6" AS REAL) * (1 - CAST(L."7" AS REAL))) as revenue
FROM CUSTOMER C
JOIN ORDERS O ON C."1" = O."2"
JOIN LINEITEM L ON O."1" = L."1_1"
JOIN SUPPLIER S ON L."2" = S."1"
JOIN NATION N ON S."3" = N."0_1"
WHERE O."5" >= '1995-01-01' AND O."5" < '1996-01-01'
GROUP BY N."0_1"
ORDER BY revenue DESC;

-- TPC-H Q6: 预测收入变化查询
SELECT 
    SUM(CAST(L."6" AS REAL) * CAST(L."7" AS REAL)) as revenue
FROM LINEITEM L
WHERE L."11" >= '1995-01-01'
  AND L."11" < '1996-01-01'
  AND CAST(L."7" AS REAL) >= 0.05
  AND CAST(L."7" AS REAL) <= 0.07
  AND CAST(L."5" AS REAL) < 24;

-- TPC-H Q10: 退货客户查询（简化版）
SELECT 
    C."1" as customer_id,
    C."2" as customer_name,
    SUM(CAST(L."6" AS REAL) * (1 - CAST(L."7" AS REAL))) as revenue,
    C."6" as account_balance,
    N."0_1" as nation,
    C."8" as customer_comment
FROM CUSTOMER C
JOIN ORDERS O ON C."1" = O."2"
JOIN LINEITEM L ON O."1" = L."1_1"
JOIN NATION N ON C."4" = N."0_1"
WHERE O."5" >= '1995-10-01'
  AND O."5" < '1996-01-01'
  AND L."10" = 'R'
GROUP BY C."1", C."2", C."6", N."0_1", C."8"
ORDER BY revenue DESC
LIMIT 20;

-- TPC-H Q12: 运输模式和订单优先级查询
SELECT 
    L."15" as ship_mode,
    SUM(CASE WHEN O."6" = '1-URGENT' OR O."6" = '2-HIGH' 
             THEN 1 ELSE 0 END) as high_line_count,
    SUM(CASE WHEN O."6" != '1-URGENT' AND O."6" != '2-HIGH' 
             THEN 1 ELSE 0 END) as low_line_count
FROM ORDERS O
JOIN LINEITEM L ON O."1" = L."1_1"
WHERE L."15" IN ('MAIL', 'SHIP')
  AND L."12" < L."13"
  AND L."11" < L."13"
  AND L."13" >= '1995-01-01'
  AND L."13" < '1996-01-01'
GROUP BY L."15"
ORDER BY L."15";

-- TPC-H Q14: 促销效果查询
SELECT 
    100.00 * SUM(CASE WHEN P."5" LIKE 'PROMO%' 
                      THEN CAST(L."6" AS REAL) * (1 - CAST(L."7" AS REAL))
                      ELSE 0 END) / 
             SUM(CAST(L."6" AS REAL) * (1 - CAST(L."7" AS REAL))) as promo_revenue
FROM LINEITEM L
JOIN PART P ON L."3" = P."1"
WHERE L."11" >= '1995-09-01' AND L."11" < '1995-10-01';

-- TPC-H Q18: 大客户查询
SELECT 
    C."2" as customer_name,
    C."1" as customer_id,
    O."1" as order_id,
    O."5" as order_date,
    O."4" as total_price,
    SUM(CAST(L."5" AS REAL)) as order_quantity
FROM CUSTOMER C
JOIN ORDERS O ON C."1" = O."2"
JOIN LINEITEM L ON O."1" = L."1_1"
WHERE O."1" IN (
    SELECT L2."1_1"
    FROM LINEITEM L2
    GROUP BY L2."1_1"
    HAVING SUM(CAST(L2."5" AS REAL)) > 300
)
GROUP BY C."2", C."1", O."1", O."5", O."4"
ORDER BY CAST(O."4" AS REAL) DESC, O."5"
LIMIT 100;

-- TPC-H Q19: 折扣收入查询
SELECT 
    SUM(CAST(L."6" AS REAL) * (1 - CAST(L."7" AS REAL))) as revenue
FROM LINEITEM L
JOIN PART P ON P."1" = L."3"
WHERE (
    (P."4" = 'Brand#12' AND P."7" IN ('SM CASE', 'SM BOX', 'SM PACK', 'SM PKG')
     AND CAST(L."5" AS REAL) >= 1 AND CAST(L."5" AS REAL) <= 11
     AND CAST(P."6" AS REAL) BETWEEN 1 AND 5
     AND L."15" IN ('AIR', 'AIR REG'))
    OR
    (P."4" = 'Brand#23' AND P."7" IN ('MED BAG', 'MED BOX', 'MED PKG', 'MED PACK')
     AND CAST(L."5" AS REAL) >= 10 AND CAST(L."5" AS REAL) <= 20
     AND CAST(P."6" AS REAL) BETWEEN 1 AND 10
     AND L."15" IN ('AIR', 'AIR REG'))
    OR
    (P."4" = 'Brand#34' AND P."7" IN ('LG CASE', 'LG BOX', 'LG PACK', 'LG PKG')
     AND CAST(L."5" AS REAL) >= 20 AND CAST(L."5" AS REAL) <= 30
     AND CAST(P."6" AS REAL) BETWEEN 1 AND 15
     AND L."15" IN ('AIR', 'AIR REG'))
);

-- TPC-H Q20: 潜在零件促销查询（简化版）
SELECT 
    S."2" as supplier_name,
    S."3" as supplier_address
FROM SUPPLIER S
JOIN NATION N ON S."3" = N."0_1"
WHERE S."1" IN (
    SELECT PS."2"
    FROM PARTSUPP PS
    JOIN PART P ON PS."1" = P."1"
    WHERE P."2" LIKE 'forest%'
      AND CAST(PS."3" AS REAL) > (
          SELECT 0.5 * SUM(CAST(L."5" AS REAL))
          FROM LINEITEM L
          WHERE L."3" = PS."1"
            AND L."2" = PS."2"
            AND L."11" >= '1995-01-01'
            AND L."11" < '1996-01-01'
      )
)
AND N."0_1" = 'CANADA'
ORDER BY S."2";
