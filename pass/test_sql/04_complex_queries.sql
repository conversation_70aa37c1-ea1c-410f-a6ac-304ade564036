-- 复杂查询测试
-- 测试子查询、窗口函数、CTE等高级功能

-- Q1: 查找高于平均订单价值的订单
SELECT "1", "2", "4", "5"
FROM ORDERS
WHERE CAST("4" AS REAL) > (
    SELECT AVG(CAST("4" AS REAL)) FROM ORDERS
)
ORDER BY CAST("4" AS REAL) DESC
LIMIT 50;

-- Q2: 查找每个客户的最大订单
SELECT C."1", C."2", O."1", O."4", O."5"
FROM CUSTOMER C
JOIN ORDERS O ON C."1" = O."2"
WHERE O."4" = (
    SELECT MAX(O2."4")
    FROM ORDERS O2
    WHERE O2."2" = C."1"
)
LIMIT 30;

-- Q3: 查找从未下过订单的客户
SELECT C."1", C."2", C."7"
FROM CUSTOMER C
WHERE NOT EXISTS (
    SELECT 1 FROM ORDERS O WHERE O."2" = C."1"
)
LIMIT 20;

-- Q4: 查找订单数量最多的前10个客户
SELECT 
    C."1",
    C."2",
    COUNT(O."1") as order_count,
    SUM(CAST(O."4" AS REAL)) as total_spent
FROM CUSTOMER C
JOIN ORDERS O ON C."1" = O."2"
GROUP BY C."1", C."2"
ORDER BY order_count DESC
LIMIT 10;

-- Q5: 查找价格高于同类型零件平均价格的零件
SELECT P1."1", P1."2", P1."5", P1."8"
FROM PART P1
WHERE CAST(P1."8" AS REAL) > (
    SELECT AVG(CAST(P2."8" AS REAL))
    FROM PART P2
    WHERE P2."5" = P1."5"
)
ORDER BY CAST(P1."8" AS REAL) DESC
LIMIT 50;

-- Q6: 查找每个供应商提供的零件数量
SELECT 
    S."1",
    S."2",
    COUNT(DISTINCT PS."1") as parts_supplied,
    SUM(CAST(PS."3" AS REAL)) as total_available_qty,
    AVG(CAST(PS."4" AS REAL)) as avg_supply_cost
FROM SUPPLIER S
JOIN PARTSUPP PS ON S."1" = PS."2"
GROUP BY S."1", S."2"
ORDER BY parts_supplied DESC
LIMIT 20;

-- Q7: 订单项价值分析
SELECT 
    L."1_1",
    L."3",
    L."5",
    L."6",
    CAST(L."6" AS REAL) / CAST(L."5" AS REAL) as unit_price,
    CASE 
        WHEN CAST(L."6" AS REAL) > 50000 THEN 'High Value'
        WHEN CAST(L."6" AS REAL) > 20000 THEN 'Medium Value'
        ELSE 'Low Value'
    END as value_category
FROM LINEITEM L
WHERE CAST(L."5" AS REAL) > 0
ORDER BY CAST(L."6" AS REAL) DESC
LIMIT 100;

-- Q8: 客户订单频率分析
WITH customer_order_stats AS (
    SELECT 
        C."1" as customer_id,
        C."2" as customer_name,
        COUNT(O."1") as order_count,
        MIN(O."5") as first_order_date,
        MAX(O."5") as last_order_date
    FROM CUSTOMER C
    LEFT JOIN ORDERS O ON C."1" = O."2"
    GROUP BY C."1", C."2"
)
SELECT 
    customer_id,
    customer_name,
    order_count,
    first_order_date,
    last_order_date,
    CASE 
        WHEN order_count = 0 THEN 'No Orders'
        WHEN order_count = 1 THEN 'Single Order'
        WHEN order_count <= 5 THEN 'Low Frequency'
        WHEN order_count <= 15 THEN 'Medium Frequency'
        ELSE 'High Frequency'
    END as frequency_category
FROM customer_order_stats
ORDER BY order_count DESC
LIMIT 50;

-- Q9: 零件竞争分析（同一零件的多个供应商）
SELECT 
    PS1."1" as part_id,
    COUNT(DISTINCT PS1."2") as supplier_count,
    MIN(CAST(PS1."4" AS REAL)) as min_cost,
    MAX(CAST(PS1."4" AS REAL)) as max_cost,
    (MAX(CAST(PS1."4" AS REAL)) - MIN(CAST(PS1."4" AS REAL))) as cost_range
FROM PARTSUPP PS1
GROUP BY PS1."1"
HAVING COUNT(DISTINCT PS1."2") > 1
ORDER BY cost_range DESC
LIMIT 30;

-- Q10: 季度订单趋势分析
SELECT 
    substr("5", 1, 4) as year,
    CASE 
        WHEN substr("5", 6, 2) IN ('01', '02', '03') THEN 'Q1'
        WHEN substr("5", 6, 2) IN ('04', '05', '06') THEN 'Q2'
        WHEN substr("5", 6, 2) IN ('07', '08', '09') THEN 'Q3'
        ELSE 'Q4'
    END as quarter,
    COUNT(*) as order_count,
    SUM(CAST("4" AS REAL)) as quarterly_revenue,
    AVG(CAST("4" AS REAL)) as avg_order_value
FROM ORDERS
GROUP BY 
    substr("5", 1, 4),
    CASE 
        WHEN substr("5", 6, 2) IN ('01', '02', '03') THEN 'Q1'
        WHEN substr("5", 6, 2) IN ('04', '05', '06') THEN 'Q2'
        WHEN substr("5", 6, 2) IN ('07', '08', '09') THEN 'Q3'
        ELSE 'Q4'
    END
ORDER BY year, quarter;
