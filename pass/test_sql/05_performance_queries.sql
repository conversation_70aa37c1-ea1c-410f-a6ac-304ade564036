-- 性能测试查询
-- 专门用于测试数据库性能和瓶颈函数的查询

-- Q1: 大表全扫描测试
SELECT COUNT(*) as total_lineitems FROM LINEITEM;

-- Q2: 无索引条件查询（测试B-tree遍历性能）
SELECT * FROM LINEITEM 
WHERE CAST("6" AS REAL) > 50000 
LIMIT 1000;

-- Q3: 复杂多表连接（测试JOIN性能）
SELECT 
    C."2" as customer_name,
    O."1" as order_id,
    L."3" as part_id,
    P."2" as part_name,
    L."5" as quantity,
    L."6" as extended_price
FROM CUSTOMER C
JOIN ORDERS O ON C."1" = O."2"
JOIN LINEITEM L ON O."1" = L."1_1"
JOIN PART P ON L."3" = P."1"
WHERE CAST(L."6" AS REAL) > 30000
LIMIT 500;

-- Q4: 大数据聚合测试
SELECT 
    L."3" as part_id,
    COUNT(*) as order_frequency,
    SUM(CAST(L."5" AS REAL)) as total_quantity,
    SUM(CAST(L."6" AS REAL)) as total_revenue,
    AVG(CAST(L."6" AS REAL)) as avg_revenue,
    MIN(CAST(L."6" AS REAL)) as min_revenue,
    MAX(CAST(L."6" AS REAL)) as max_revenue
FROM LINEITEM L
GROUP BY L."3"
HAVING COUNT(*) > 10
ORDER BY total_revenue DESC;

-- Q5: 复杂子查询测试
SELECT 
    O."1",
    O."2", 
    O."4",
    (SELECT COUNT(*) FROM LINEITEM L WHERE L."1_1" = O."1") as line_count,
    (SELECT AVG(CAST(L2."6" AS REAL)) FROM LINEITEM L2 WHERE L2."1_1" = O."1") as avg_line_value
FROM ORDERS O
WHERE CAST(O."4" AS REAL) > (
    SELECT AVG(CAST(O2."4" AS REAL)) * 1.5 FROM ORDERS O2
)
ORDER BY CAST(O."4" AS REAL) DESC
LIMIT 200;

-- Q6: 排序性能测试
SELECT 
    L."1_1",
    L."3",
    L."6",
    L."11",
    L."12"
FROM LINEITEM L
ORDER BY 
    CAST(L."6" AS REAL) DESC,
    L."11",
    L."12"
LIMIT 2000;

-- Q7: 字符串模式匹配测试
SELECT 
    C."1",
    C."2",
    C."3",
    C."8"
FROM CUSTOMER C
WHERE C."2" LIKE '%Customer%'
   OR C."8" LIKE '%special%'
   OR C."8" LIKE '%regular%'
LIMIT 500;

-- Q8: 复杂CASE语句测试
SELECT 
    O."1",
    O."3",
    O."4",
    O."5",
    CASE O."3"
        WHEN 'O' THEN 'Open'
        WHEN 'F' THEN 'Fulfilled'
        WHEN 'P' THEN 'Pending'
        ELSE 'Other'
    END as status_description,
    CASE 
        WHEN CAST(O."4" AS REAL) < 10000 THEN 'Small'
        WHEN CAST(O."4" AS REAL) < 50000 THEN 'Medium'
        WHEN CAST(O."4" AS REAL) < 200000 THEN 'Large'
        ELSE 'Huge'
    END as size_category,
    CASE 
        WHEN O."5" < '1995-01-01' THEN 'Old'
        WHEN O."5" < '1996-01-01' THEN 'Recent'
        ELSE 'Current'
    END as time_category
FROM ORDERS O
ORDER BY CAST(O."4" AS REAL) DESC
LIMIT 1000;

-- Q9: 自连接测试（查找同一客户的多个订单）
SELECT 
    O1."1" as order1_id,
    O2."1" as order2_id,
    O1."2" as customer_id,
    O1."4" as order1_total,
    O2."4" as order2_total,
    O1."5" as order1_date,
    O2."5" as order2_date
FROM ORDERS O1
JOIN ORDERS O2 ON O1."2" = O2."2" AND O1."1" != O2."1"
WHERE CAST(O1."4" AS REAL) > 100000 
  AND CAST(O2."4" AS REAL) > 100000
LIMIT 300;

-- Q10: 极端聚合查询（测试内存使用和排序）
SELECT 
    substr(L."11", 1, 7) as ship_month,
    L."15" as ship_mode,
    COUNT(*) as shipment_count,
    SUM(CAST(L."5" AS REAL)) as total_quantity,
    SUM(CAST(L."6" AS REAL)) as total_extended_price,
    AVG(CAST(L."7" AS REAL)) as avg_discount,
    AVG(CAST(L."8" AS REAL)) as avg_tax,
    SUM(CAST(L."6" AS REAL) * (1 - CAST(L."7" AS REAL))) as discounted_revenue,
    SUM(CAST(L."6" AS REAL) * (1 - CAST(L."7" AS REAL)) * (1 + CAST(L."8" AS REAL))) as net_revenue
FROM LINEITEM L
WHERE L."11" >= '1995-01-01' AND L."11" <= '1996-12-31'
GROUP BY 
    substr(L."11", 1, 7),
    L."15"
ORDER BY 
    ship_month,
    total_extended_price DESC;
