#!/bin/bash

# TPC-H 数据库测试脚本
# 用于执行各种SQL测试查询并记录性能

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PASS_DIR="$(dirname "$SCRIPT_DIR")"
DB_PATH="$PASS_DIR/tpch.db"
TEST_SQL_DIR="$SCRIPT_DIR"
LOG_DIR="$PASS_DIR/test_logs"

# 创建日志目录
mkdir -p "$LOG_DIR"

echo "=== TPC-H 数据库测试脚本 ==="
echo "脚本目录: $SCRIPT_DIR"
echo "Pass目录: $PASS_DIR"
echo "数据库路径: $DB_PATH"
echo "日志目录: $LOG_DIR"
echo "开始时间: $(date)"
echo ""

# 检查数据库文件是否存在
if [ ! -f "$DB_PATH" ]; then
    echo "错误: 找不到数据库文件 $DB_PATH"
    exit 1
fi

# 函数：执行SQL文件并记录时间
execute_sql_file() {
    local sql_file="$1"
    local test_name="$2"
    local log_file="$LOG_DIR/${test_name}_$(date +%Y%m%d_%H%M%S).log"
    
    echo "=== 执行 $test_name ==="
    echo "SQL文件: $sql_file"
    echo "日志文件: $log_file"
    
    # 记录开始时间
    start_time=$(date +%s.%N)
    echo "开始时间: $(date)" > "$log_file"
    echo "" >> "$log_file"
    
    # 执行SQL文件
    if sqlite3 "$DB_PATH" < "$sql_file" >> "$log_file" 2>&1; then
        # 记录结束时间
        end_time=$(date +%s.%N)
        duration=$(echo "$end_time - $start_time" | bc)
        
        echo "结束时间: $(date)" >> "$log_file"
        echo "执行时长: ${duration} 秒" >> "$log_file"
        echo "✓ 执行成功 (耗时: ${duration} 秒)"
    else
        echo "结束时间: $(date)" >> "$log_file"
        echo "执行失败" >> "$log_file"
        echo "✗ 执行失败，详情请查看日志: $log_file"
    fi
    echo ""
}

# 函数：执行单个查询并记录性能
execute_single_query() {
    local query="$1"
    local query_name="$2"
    local log_file="$LOG_DIR/single_query_$(date +%Y%m%d_%H%M%S).log"
    
    echo "=== 执行单个查询: $query_name ==="
    echo "查询: $query"
    
    start_time=$(date +%s.%N)
    echo "开始时间: $(date)" > "$log_file"
    echo "查询: $query" >> "$log_file"
    echo "" >> "$log_file"
    
    if echo "$query" | sqlite3 "$DB_PATH" >> "$log_file" 2>&1; then
        end_time=$(date +%s.%N)
        duration=$(echo "$end_time - $start_time" | bc)
        
        echo "结束时间: $(date)" >> "$log_file"
        echo "执行时长: ${duration} 秒" >> "$log_file"
        echo "✓ 执行成功 (耗时: ${duration} 秒)"
    else
        echo "结束时间: $(date)" >> "$log_file"
        echo "执行失败" >> "$log_file"
        echo "✗ 执行失败，详情请查看日志: $log_file"
    fi
    echo ""
}

# 检查bc命令是否可用（用于计算时间差）
if ! command -v bc &> /dev/null; then
    echo "警告: bc命令不可用，无法计算精确的执行时间"
    echo "安装建议: sudo apt-get install bc"
    echo ""
fi

# 主菜单
while true; do
    echo "请选择要执行的测试："
    echo "1. 简单查询测试 (01_simple_queries.sql)"
    echo "2. 连接查询测试 (02_join_queries.sql)"
    echo "3. 聚合查询测试 (03_aggregation_queries.sql)"
    echo "4. 复杂查询测试 (04_complex_queries.sql)"
    echo "5. 性能测试查询 (05_performance_queries.sql)"
    echo "6. TPC-H标准查询 (06_tpch_standard_queries.sql)"
    echo "7. 执行所有测试"
    echo "8. 执行自定义查询"
    echo "9. 查看数据库基本信息"
    echo "0. 退出"
    echo ""
    read -p "请输入选择 (0-9): " choice

    case $choice in
        1)
            execute_sql_file "$TEST_SQL_DIR/01_simple_queries.sql" "simple_queries"
            ;;
        2)
            execute_sql_file "$TEST_SQL_DIR/02_join_queries.sql" "join_queries"
            ;;
        3)
            execute_sql_file "$TEST_SQL_DIR/03_aggregation_queries.sql" "aggregation_queries"
            ;;
        4)
            execute_sql_file "$TEST_SQL_DIR/04_complex_queries.sql" "complex_queries"
            ;;
        5)
            execute_sql_file "$TEST_SQL_DIR/05_performance_queries.sql" "performance_queries"
            ;;
        6)
            execute_sql_file "$TEST_SQL_DIR/06_tpch_standard_queries.sql" "tpch_standard_queries"
            ;;
        7)
            echo "=== 执行所有测试 ==="
            execute_sql_file "$TEST_SQL_DIR/01_simple_queries.sql" "simple_queries"
            execute_sql_file "$TEST_SQL_DIR/02_join_queries.sql" "join_queries"
            execute_sql_file "$TEST_SQL_DIR/03_aggregation_queries.sql" "aggregation_queries"
            execute_sql_file "$TEST_SQL_DIR/04_complex_queries.sql" "complex_queries"
            execute_sql_file "$TEST_SQL_DIR/05_performance_queries.sql" "performance_queries"
            execute_sql_file "$TEST_SQL_DIR/06_tpch_standard_queries.sql" "tpch_standard_queries"
            echo "=== 所有测试执行完成 ==="
            ;;
        8)
            echo "请输入SQL查询 (以分号结束):"
            read -p "SQL> " custom_query
            if [ ! -z "$custom_query" ]; then
                execute_single_query "$custom_query" "custom_query"
            fi
            ;;
        9)
            echo "=== 数据库基本信息 ==="
            sqlite3 "$DB_PATH" "SELECT name FROM sqlite_master WHERE type='table';" | while read table; do
                count=$(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM $table;")
                echo "$table: $count 条记录"
            done
            echo ""
            ;;
        0)
            echo "退出测试脚本"
            break
            ;;
        *)
            echo "无效选择，请重新输入"
            ;;
    esac
done

echo "测试脚本结束时间: $(date)"
