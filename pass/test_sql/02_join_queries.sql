-- 连接查询测试
-- 测试JOIN操作，涉及多表查询

-- Q1: 客户和订单关联查询
SELECT 
    C."2" as customer_name,
    O."1" as order_id,
    O."4" as total_price,
    O."5" as order_date
FROM CUSTOMER C
JOIN ORDERS O ON C."1" = O."2"
LIMIT 50;

-- Q2: 订单和订单项关联查询
SELECT 
    O."1" as order_id,
    O."4" as order_total,
    L."3" as part_id,
    L."5" as quantity,
    L."6" as extended_price
FROM ORDERS O
JOIN LINEITEM L ON O."1" = L."1_1"
LIMIT 100;

-- Q3: 三表连接：客户-订单-订单项
SELECT 
    C."2" as customer_name,
    O."1" as order_id,
    O."4" as order_total,
    L."3" as part_id,
    L."5" as quantity
FROM CUSTOMER C
JOIN ORDERS O ON C."1" = O."2"
JOIN LINEITEM L ON O."1" = L."1_1"
LIMIT 100;

-- Q4: 客户订单汇总
SELECT 
    C."1" as customer_id,
    C."2" as customer_name,
    COUNT(O."1") as order_count,
    SUM(CAST(O."4" AS REAL)) as total_spent
FROM CUSTOMER C
LEFT JOIN ORDERS O ON C."1" = O."2"
GROUP BY C."1", C."2"
ORDER BY total_spent DESC
LIMIT 20;

-- Q5: 零件供应商关联查询
SELECT 
    P."2" as part_name,
    P."4" as brand,
    S."2" as supplier_name,
    PS."3" as available_qty,
    PS."4" as supply_cost
FROM PART P
JOIN PARTSUPP PS ON P."1" = PS."1"
JOIN SUPPLIER S ON PS."2" = S."1"
LIMIT 50;

-- Q6: 客户地区分布
SELECT 
    N."0_1" as nation,
    COUNT(C."1") as customer_count
FROM CUSTOMER C
JOIN NATION N ON C."4" = N."0_1"
GROUP BY N."0_1"
ORDER BY customer_count DESC;

-- Q7: 订单状态和客户关联
SELECT 
    O."3" as order_status,
    C."7" as market_segment,
    COUNT(*) as order_count,
    AVG(CAST(O."4" AS REAL)) as avg_order_value
FROM ORDERS O
JOIN CUSTOMER C ON O."2" = C."1"
GROUP BY O."3", C."7"
ORDER BY order_count DESC;

-- Q8: 高价值客户的订单分析
SELECT 
    C."1" as customer_id,
    C."2" as customer_name,
    C."7" as market_segment,
    O."3" as order_status,
    COUNT(O."1") as order_count,
    SUM(CAST(O."4" AS REAL)) as total_revenue
FROM CUSTOMER C
JOIN ORDERS O ON C."1" = O."2"
GROUP BY C."1", C."2", C."7", O."3"
HAVING SUM(CAST(O."4" AS REAL)) > 500000
ORDER BY total_revenue DESC;
