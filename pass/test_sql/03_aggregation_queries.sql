-- 聚合和分组查询测试
-- 测试GROUP BY, HAVING, 聚合函数等

-- Q1: 按年份统计订单
SELECT 
    substr("5", 1, 4) as order_year,
    COUNT(*) as order_count,
    SUM(CAST("4" AS REAL)) as total_revenue,
    AVG(CAST("4" AS REAL)) as avg_order_value,
    MIN(CAST("4" AS REAL)) as min_order_value,
    MAX(CAST("4" AS REAL)) as max_order_value
FROM ORDERS
GROUP BY substr("5", 1, 4)
ORDER BY order_year;

-- Q2: 按月份统计订单
SELECT 
    substr("5", 1, 7) as order_month,
    COUNT(*) as order_count,
    SUM(CAST("4" AS REAL)) as monthly_revenue
FROM ORDERS
WHERE "5" >= '1995-01-01' AND "5" <= '1996-12-31'
GROUP BY substr("5", 1, 7)
ORDER BY order_month;

-- Q3: 订单优先级分析
SELECT 
    "6" as order_priority,
    COUNT(*) as order_count,
    SUM(CAST("4" AS REAL)) as total_revenue,
    AVG(CAST("4" AS REAL)) as avg_revenue
FROM ORDERS
GROUP BY "6"
ORDER BY total_revenue DESC;

-- Q4: 客户市场细分分析
SELECT 
    "7" as market_segment,
    COUNT(*) as customer_count,
    AVG(CAST("6" AS REAL)) as avg_account_balance
FROM CUSTOMER
GROUP BY "7"
ORDER BY customer_count DESC;

-- Q5: 零件品牌分析
SELECT 
    "4" as brand,
    COUNT(*) as part_count,
    AVG(CAST("8" AS REAL)) as avg_retail_price,
    MIN(CAST("8" AS REAL)) as min_price,
    MAX(CAST("8" AS REAL)) as max_price
FROM PART
GROUP BY "4"
HAVING COUNT(*) > 10
ORDER BY part_count DESC;

-- Q6: 零件类型统计
SELECT 
    "5" as part_type,
    COUNT(*) as part_count,
    AVG(CAST("8" AS REAL)) as avg_price
FROM PART
GROUP BY "5"
ORDER BY part_count DESC
LIMIT 20;

-- Q7: 供应商国家分析
SELECT 
    "3" as nation_id,
    COUNT(*) as supplier_count,
    AVG(CAST("6" AS REAL)) as avg_account_balance
FROM SUPPLIER
GROUP BY "3"
ORDER BY supplier_count DESC;

-- Q8: 订单行项目统计
SELECT 
    L."1_1" as order_id,
    COUNT(*) as line_count,
    SUM(CAST(L."5" AS REAL)) as total_quantity,
    SUM(CAST(L."6" AS REAL)) as total_extended_price,
    AVG(CAST(L."7" AS REAL)) as avg_discount
FROM LINEITEM L
GROUP BY L."1_1"
HAVING COUNT(*) > 3
ORDER BY total_extended_price DESC
LIMIT 50;

-- Q9: 零件供应情况统计
SELECT 
    PS."1" as part_id,
    COUNT(*) as supplier_count,
    MIN(CAST(PS."4" AS REAL)) as min_supply_cost,
    MAX(CAST(PS."4" AS REAL)) as max_supply_cost,
    AVG(CAST(PS."4" AS REAL)) as avg_supply_cost,
    SUM(CAST(PS."3" AS REAL)) as total_available_qty
FROM PARTSUPP PS
GROUP BY PS."1"
HAVING COUNT(*) > 1
ORDER BY total_available_qty DESC
LIMIT 30;

-- Q10: 每日订单趋势
SELECT 
    "5" as order_date,
    COUNT(*) as daily_orders,
    SUM(CAST("4" AS REAL)) as daily_revenue
FROM ORDERS
WHERE "5" >= '1996-01-01' AND "5" <= '1996-01-31'
GROUP BY "5"
ORDER BY "5";
