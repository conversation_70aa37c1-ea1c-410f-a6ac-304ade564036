### 手动步骤

1. **构建LLVM Pass**
   ```bash
   ./build.sh
   ```

2. **编译SQLite为LLVM IR**
   ```bash
   clang -S -emit-llvm ../sqlite3.c -o sqlite3.ll -DSQLITE_THREADSAFE=0
   ```

3. **应用函数日志Pass**
   ```bash
   opt -load-pass-plugin=./build/lib/libFunctionLoggerPass.so \
       -passes="function-logger" sqlite3.ll -S -o sqlite3_logged.ll
   ```

4. **编译最终可执行文件**
   ```bash
   clang sqlite3_logged.ll ../shell.c -o sqlite3_logged -ldl -lpthread -lm
   ```


### 测试示例

创建一个简单的数据库操作：
```bash
echo "CREATE TABLE test(id INTEGER); INSERT INTO test VALUES(1); SELECT * FROM test;" | ./sqlite3_logged
```
